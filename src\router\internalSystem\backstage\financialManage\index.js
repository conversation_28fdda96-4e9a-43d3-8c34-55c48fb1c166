export default [{
  path: 'financialManage',
  name: 'financialManage',
  title: '财务管理',
  icon: 'home',
  show: true,
  meta: {
    keepAlive: false
  },
  component: resolve =>
    require([
      "@/views/internalSystem/backstage/components/view/view.vue"
    ], resolve),
  children: [
    {
      path: 'receivables',
      name: 'receivables',
      title: '销售收款单',
      meta: {
        title: "销售收款单"
      },
      component: () => import('@/views/internalSystem/backstage/financialManage/receivables/index.vue')
    },
    // {
    //   path: 'receivables2',
    //   name: 'receivables2',
    //   title: '销售收款单(新)',
    //   meta: {
    //     title: "销售收款单(新)"
    //   },
    //   component: () => import('@/views/internalSystem/backstage/financialManage/receivables/index2.vue')
    // },
    {
      path: 'invoice',
      name: 'invoice',
      title: '费用报销单',
      meta: {
        title: "费用报销单"
      },
      component: () => import('@/views/internalSystem/backstage/financialManage/invoice/index.vue')
    },
    {
      path: 'income',
      name: 'income',
      title: '其它收入单',
      meta: {
        title: "其它收入单"
      },
      component: () => import('@/views/internalSystem/backstage/financialManage/income/index.vue')
    },
    {
      path: 'invoiceIncome',
      name: 'invoiceIncome',
      title: '发票进项单',
      meta: {
        title: "发票进项单"
      },
      component: () => import('@/views/internalSystem/backstage/financialManage/invoiceIncome/index.vue')
    },
    {
      path: 'bankAccout',
      name: 'bankAccout',
      title: '银行账户管理',
      meta: {
        title: "银行账户管理"
      },
      component: () => import('@/views/internalSystem/backstage/financialManage/bankAccout/index.vue')
    },
  ]
}]