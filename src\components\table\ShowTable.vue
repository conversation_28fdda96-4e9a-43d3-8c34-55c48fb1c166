<template>
  <div class="tableContainer">
    <!--参数：tableHeader:表头字段[{title：xxx，name:xxx},]
              isCheck:boolean 是否显示多选框
              tableData:[]    显示的数据
              tableHeight：number 表格高度
              clearSelect:boolean 是否删除选择
        方法：getSelectRecords（）：获取选中的行数据
              handlePageChange():改变值页码等的值之后得重新请求数据
    -->
    <vxe-toolbar
      :id="tableId"
      :custom="{ storage: true, immediate: true }"
      :resizable="{ storage: true }"
      class="hiddenButton"
      title="点击可隐藏列表项目"
    >
    </vxe-toolbar>
    <!-- :checkbox-config="{ reserve: true }" 缓存上次选择的数据 -->
    <!-- row-id="id"   把row-id变成id -->
    <vxe-table
      column-key
      border
      resizable
      show-overflow
      show-header-overflow
      highlight-hover-row
      highlight-current-row
      auto-resize
      ref="xTable"
      :height="tableHeight"
      :loading="loading"
      align="center"
      @cell-click="selectCheck"
      @select-change="getSelectRecords"
      @radio-change="getRadioRow"
      @select-all="getSelectRecords"
      @context-menu-click="contextMenuClickEvent"
      @cell-dblclick="dblclick"
      :checkbox-config="{
        reserve: true,
        showHeader: !isFromProduct,
        checkMethod,
      }"
      :data="tableData"
      :customs="customColumns"
      size="small"
      :keyboard-config="{ isArrow: true }"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
    >
      <!-- :customs.sync="customColumns"    隐藏指定列 -->
      <!-- 多选 -->
      <vxe-table-column
        type="checkbox"
        align="center"
        width="40"
        v-if="isCheck"
      ></vxe-table-column>
      <!-- 单选框 -->
      <vxe-table-column
        fiexd="check"
        type="radio"
        align="center"
        width="40"
        v-if="isRadio"
      ></vxe-table-column>
      <vxe-table-column
        type="seq"
        align="center"
        title="序号"
        width="50"
      ></vxe-table-column>
      <vxe-table-column
        v-for="(item, index) in tableHeader"
        :key="index"
        :field="item.name"
        :fixed="item.fixed || ''"
        :title="item.title === '税率' ? '税率(%)' : item.title"
        :width="item.width || '120'"
      >
     

        <template v-if="item.name === 'image'" v-slot="{ row }">
          <el-image
            style="width: 50px; height: 50px"
            :src="row.image"
            :preview-src-list="[row.image]"
          >
          </el-image>
        </template>
        <template v-else-if="item.name === 'getDetail'" v-slot="{ row }">
          <el-button type="text" @click="getDetail(row)">查询详情</el-button>
        </template>

        <template v-else-if="item.name === 'moreActions'" v-slot="{ row }">
          <el-button type="text" @click="getDetail(row)" v-show="isEdit"
            >修改</el-button
          >
           <el-button type="text" @click="getDetail(row)" v-show="isDetail"
            >详情</el-button
          >
          
          <el-button
            type="text"
            style="color:red"
            @click="delTableRow(row)"
            v-show="isDel"
            >删除</el-button
          >
        </template>

        <template v-else v-slot="{ row }">
          <span
            >{{
              fieldArray.indexOf(item.name) !== -1 ||
              item.name.indexOf("Amount") !== -1 ||
              item.name.indexOf("Money") !== -1 ||
              item.name.indexOf("Price") !== -1
                ? getCellFixedDemcial(item.name, row[item.name])
                : row[item.name]
            }}
          </span>
        </template>
        
      </vxe-table-column>
      <slot></slot>
    </vxe-table>
  </div>
</template>

<script>
// import { getFixedDemical } from "@/common/trade_erp/common";
import { sortTable } from "@/common/global/vxeTableDrag.js";


// import XEClipboard from 'xe-clipboard'
// @getDetail  详情
// @remove 删除
export default {
  props: {
    isCheck: {
      type: Boolean,
      default: false,
    },
    isRadio: {
      type: Boolean,
      default: false,
    },
    tableHeader: {
      type: Array,
      default() {
        return [];
      },
    },
    tableData: {
      type: Array,
      default() {
        return [];
      },
    },
    tableHeight: {
      /* 跟随父盒子高度 */
      default: "100%",
    },
    customColumns: {
      type: Array,
      default() {
        return [];
      },
    },
    clearSelect: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    tableId: {
      //表格实例标识,用来匹配自动保存的隐藏字段
      type: String,
      default: "1",
    },
    djType: {
      type: String,
      default: "",
    },
    rowClick: {
      //单击行是否改变选择状态
      type: Boolean,
      default: true,
    },
    isFromProduct: {
      //是否是来自商品选择
      type: Boolean,
      default: false,
    },
    //下面三个为显示更多操作
    isEdit: {
      type: Boolean,
      default: false,
    },
    isDetail: {
      type: Boolean,
      default: false,
    },
    isDel: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      records: {},
      fieldArray: [
        "bfCpmj",
        "bPrice",
        "bprice",
        "bfWsdj",
        "bfWsje",
        "bNumb",
        "gNumb",
        "bfDdzk",
        "kyNum",
        "kcNum",
      ],
      selectRecords: [],
      sortable: "",
      bodyMenus: [
        [
          {
            code: "copy",
            name: "复制当前字段",
            visible: true,
            disabled: false,
          },
        ],
      ],
    };
  },
  mounted() {
    this.columnDrop();
  },
  beforeDestroy() {
    if (this.sortable) {
      this.sortable.destroy();
    }
  },
  activated() {
    //缓存钩子，进入缓存页面时使用
  },
  methods: {
    //列拖拽
    columnDrop() {
      this.$nextTick(() => {
        let xTable = this.$refs.xTable;
        this.sortable = sortTable(xTable);
      });
    },
    //右键菜单
    contextMenuClickEvent({ menu }) {
      //      :context-menu="{ body: { options: bodyMenus } }"
      switch (menu.code) {
        case "copy":
          // 示例
          //  if (XEClipboard.copy(row[column.property])) {
          //             this.success('已复制到剪贴板！')
          //           }

          break;
      }
      this.$nextTick(() => {
        this.$refs.xTable.refreshColumn();
      });
    },
    getSelectRecords(selectRecord = {}) {
      //当前改变的row  row.row
      if (selectRecord.checked && this.isFromProduct && selectRecord.row) {
        selectRecord.row.checked = true;
        this.$emit("click", selectRecord.row);
      }

      //获取多选框选中的数据getSelectRecords
      let records = [];
      records = this.$refs.xTable.getSelectRecords(); /* 用于多选行，获取已选中的行数据 */
      this.$emit("getSelectRecords", records);
    },
    getRadioRow() {
      //获取单选框选中的数据
      this.selectRecords = this.$refs.xTable.getRadioRow();

      this.$emit("getSelectRecords", this.selectRecords);
    },
    getCellFixedDemcial(field, value) {
      // return getFixedDemical(field, value);
      return value;
    },
    selectCheck({ row, column }) {
      // @cell-click="selectCheck"  头部加这个生效
      // this.$emit("click", row);
      if (column.type == "checkbox") return;

      if (column.property === "state" || column.property === "state_type")
        return;
      if (this.isRadio && this.rowClick) {
        this.$refs.xTable.setRadioRow(row);
        this.getRadioRow();
      }
      if (this.isCheck && this.rowClick) {
        this.$refs.xTable.toggleRowSelection(row);
        this.getSelectRecords();
      }
    },
    checkMethod({ row }) {
      return !row.checked;
    },

    GetFullTableData() {
      return this.$refs.xTable.getTableData().fullData;
    },

    dblclick({ row }) {
      //单元格双击事件
      this.$emit("dblclick", row);
    },
    setRadioRow(row){
      if(this.isRadio)
      this.$refs.xTable.setRadioRow(row)
    },
    ClearData() {
      this.selectRecords = [];
      this.$refs.xTable.clearSelection();
    },
    //点击查看详情触发，把事件处理逻辑抛到父组件
    getDetail(row = {}) {
      this.$emit("getDetail", row);
    },
    delTableRow(row) {
      this.$emit("remove", row);
    },
  },

  watch: {
    clearSelect(val) {
      //监控删除选中状态
      if (val) {
        this.$refs.xTable.clearSelection();
        this.$emit("clearSelected");
      }
    },
  },
};
</script>

<style scoped lang="scss">
.tableContainer {
  width: 100%;
  height: 100%;
  position: relative;
  // user-select: none;
  // overflow: auto;
  .hiddenButton {
    position: absolute;
    top: -35px;
    right: 0;
    // width: 32px;
    height: 32px;

    &.vxe-toolbar {
      padding: 0;
    }

    .vxe-button--wrapper {
      display: none;
    }
  }
}
</style>
