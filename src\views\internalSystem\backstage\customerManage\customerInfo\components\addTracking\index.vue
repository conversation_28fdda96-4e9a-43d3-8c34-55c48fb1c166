<template>
  <div v-if="dialogVisible">
    <el-button @click="dialogCancel">返 回</el-button>
    <el-button type="primary" @click="submitForm('ruleForm')">保 存</el-button>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="mt20">
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户阶段" prop="customer_track_stage">
            <el-select v-model="ruleForm.customer_track_stage" placeholder="请选择客户阶段" disabled filterable clearable>
              <el-option v-for="item in customerStageList" :key="item.sysValue" :label="item.sysName"
                :value="item.sysValue">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input v-model="ruleForm.customer_name" disabled clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系人" prop="link_man">
            <el-input v-model="ruleForm.link_man" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="ruleForm.phone"  clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="使用软件" prop="use_software">
            <el-input v-model="ruleForm.use_software" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="16">
          <el-form-item label="跟进内容" prop="customer_tracking_content">
            <el-input type="textarea" v-model="ruleForm.customer_tracking_content" placeholder="请输入跟进内容" clearable>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="16">
          <el-form-item label="客户痛点" prop="customer_points">
            <el-input type="textarea" v-model="ruleForm.customer_points" placeholder="请输入客户痛点" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-button type="primary" @click="addBrand">调入</el-button>
      <el-row :gutter="20">
        <el-col :xl="16">
          <el-table :data="tableData" border class="mt20" max-height="360"
            :header-cell-style="{ 'font-weight': 'bold', color: '#333333' }">
            <el-table-column prop="brand_type" label="客户兴趣产品服务">
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button @click="del(scope.row)" class="danger" type="text" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-form>
    <brand-list ref="brandList" brandClassify="1" @getInfo="getInfo" />
  </div>
</template>
<script src="./index.js">

</script>