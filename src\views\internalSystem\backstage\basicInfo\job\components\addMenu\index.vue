<template>
  <div>
    <el-dialog :title="title" :visible.sync="dialogVisible" append-to-body @close="closeDialog" width="660px"
      :close-on-click-modal="false" v-dialogDrag>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="菜单名称" prop="menuName">
          <el-input v-model="ruleForm.menuName" clearable></el-input>
        </el-form-item>
        <el-form-item label="菜单路径" prop="menuUrl">
          <el-input v-model="ruleForm.menuUrl" clearable></el-input>
        </el-form-item>
        <el-form-item label="菜单图标" prop="iconUrl">
          <el-input v-model="ruleForm.iconUrl" clearable></el-input>
        </el-form-item>
        <el-form-item label="菜单排序" prop="orderNum">
          <el-tooltip class="item" effect="dark" content="数值越小，排序越靠前" placement="right">
            <el-input  v-model.number="ruleForm.orderNum" clearable></el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="上级菜单" prop="parentName">
          <el-input v-model="ruleForm.parentName" autocomplete="off" disabled></el-input>
        </el-form-item>
        <el-form-item label="菜单类型" prop="type">
          <el-select v-model="ruleForm.type" placeholder="请选择类型">
            <el-option v-for="item in params_constant_menu_type" :key="item.id" :label="item.label" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="菜单编码" prop="menuDescribe">
          <el-input v-model="ruleForm.menuDescribe" clearable></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('ruleForm')">保 存</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script src="./index.js"></script>