<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
  >
    <div>
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
      >
        <el-form-item label="培训人" prop="train_id">
          <el-select
            v-model="ruleForm.train_id"
            clearable
            placeholder="请选择实施人"
          >
            <el-option
              v-for="item in propleList"
              :key="item.employeeId"
              :label="item.employee_number + '-' + item.employee_name"
              :value="item.employeeId"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submit()">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import API from "@/api/internalSystem/contractManage/index.js";
import {cloneDeep} from "lodash"
export default {
  data() {
    return {
      title: "更换实施人",
      dialogVisible: false,
      ruleForm: {
        train_id: "",
      },
      propleList: [],
      rules: {
        train_id: [{ required: true, message: "请选择", trigger: "blur" }],
      },
      selectObj: {},
    };
  },
  methods: {
    Show(selectObj) {
      this.dialogVisible = true;
      this.selectObj = cloneDeep(selectObj) ;
      this.$store.dispatch("getEmployee").then((res) => {
        this.propleList = res;
      });
   
      this.ruleForm.train_id=this.selectObj.train_id
      this.$nextTick(() => {
        this.$refs.ruleForm.resetFields();
      });
    },
    handleClose() {
      Object.assign(this.$data, this.$options.data());
      this.selectObj={}
    },
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) return false;
        this.save();
      });
    },
    save() {
      let params = {
        train_state: 0,
        customer_contract_id: this.selectObj.customer_contract_id,
        train_id: this.ruleForm.train_id,
      };
      API.setTrainContract(params).then(() => {
        this.success("保存成功");
        this.handleClose();
        this.$emit("success");
      });
    },
  },
};
</script>
