function exec (cmd) {
  return require('child_process').execSync(cmd).toString().trim()
}
let moment = require('moment')

let _date = moment().format('YYYY-MM-DD-HH-mm-ss')
let name = 'internalSystem'
let _file = `${name}_${_date}.tar.gz`
exec('tar czvf ' + _file + ' dist')
// const 内部系统 = `root@114.215.110.90:/root/back_end/内部系统/`
const 内部系统改版_20210901上线 = `root@114.215.110.90:/root/front_end/内部系统改版_20210901上线/`
const 内部系统_新 = `root@114.215.110.90:/root/front_end/内部系统_新/`
// const 内部系统新 = `root@114.215.110.90:/root/front_end/内部系统_新/`
setTimeout(() => {
  console.log(`
    压缩完成：${_file}
    上传命令：
      scp -P 22 ${_file} ${内部系统改版_20210901上线}
      scp -P 22 ${_file} ${内部系统_新}
    服务器密码：
    
    解压命令：
      tar -xvf ${_file} -C ./
`)
}, 200)
