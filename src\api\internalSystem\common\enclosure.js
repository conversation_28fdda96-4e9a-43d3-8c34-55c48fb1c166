import Axios from "@/api";
import environment from "@/api/environment";

export default {
  // 实施单附件列表
  impleFileList: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/fileList`, params)
  },
  // 实施单上传附件
  impleAdd: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/addFile`, params)
  },
  // 实施单删除附件
  impleDel: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/delFile`, params)
  },
  // 合同单附件列表
  contractFileList: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/fileList`, params)
  },
  // 合同单上传附件
  contractAdd: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/addFile`, params)
  },
  // 合同单删除附件
  contractDel: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/delFile`, params)
  },
  // 开票单附件列表richTextPrintIndex
  openTicketFileList: params => {
    return Axios.post(`${environment.internalSystemAPI}openTicket/fileList`, params)
  },
  // 开票单上传附件
  openTicketAdd: params => {
    return Axios.post(`${environment.internalSystemAPI}openTicket/addFile`, params)
  },
  // 开票单删除附件
  openTicketDel: params => {
    return Axios.post(`${environment.internalSystemAPI}openTicket/delFile`, params)
  },
  // 产品信息附件列表
  brandFileList: params => {
    return Axios.post(`${environment.internalSystemAPI}brand/fileList`, params)
  },
  // 产品信息上传附件
  brandAdd: params => {
    return Axios.post(`${environment.internalSystemAPI}brand/addFile`, params)
  },
  // 产品信息删除附件
  brandDel: params => {
    return Axios.post(`${environment.internalSystemAPI}brand/delFile`, params)
  },
}