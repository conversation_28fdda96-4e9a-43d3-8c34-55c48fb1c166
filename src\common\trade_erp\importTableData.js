/**
 * 表格最后新增根据导入字段添加数据
 * @param {Object} refTable vxe-table获取表格数据对象
 * @param {Object} records vxe-table预先设置表格新增行初始值字段
 * @param {array} data 传入数据
 * @param {number} ImportField 需要改变的数据字段
 */
function importHistoryTableData(
  records = [],
  data = [],
  ImportField = [],
  type = "purchase",
  depotFlag = false
) {
  if (!data.length) return

  /* 表格数据是异步渲染 */
  let promise = new Promise(function (resolve) {
    let tableData = []
    for (let i = 0; i < data.length; i++) {
      /* 表格创建行 */
      let row = Object.assign({}, records)

      let lineCalc = {};

      if (
        ImportField.includes("bNumb") &&
        ImportField.includes("bfWsdj") &&
        ImportField.includes("bPrice")
      ) {
        if (type === "purchase") {
          /* bNumb = whxsl 未核销数量 */
          lineCalc = tablePurchaseLineCount(
            data[i].whxsl,
            data[i].bfWsdj,
            data[i].bPrice
          );
        } else if (type === "order") {
          /* bNumb = whxsl 未核销数量 */
          lineCalc = tableOrderLineCount(
            data[i].whxsl,
            data[i].bfWsdj,
            data[i].bPrice,
            data[i].gPrice
          );
        }
      }

      ImportField.forEach(item => {
        row[item] = data[i][item]; /* 根据许需要导入字段进行赋值 */
        if ((item === "depotName" || item === "depotId") && depotFlag) {
          //采购订单的默认仓库需要设置
          row[item] = "";
        }
        switch (item) {
          case "bNumb":
            row.bNumb = data[i].whxsl;
            break;
          case "gNumb":
            row.gNumb = data[i].whxsl;
            break;
          case "bfWsje":
            row.bfWsje = lineCalc.bfWsje;
            break;
          case "bfCalje":
            row.bfCalje = lineCalc.bfCalje;
            break;
          case "bfSe":
            row.bfSe = lineCalc.bfSe;
            break;
          case "bfCalml":
            row.bfCalml = lineCalc.bfCalml;
            break;
        }
      });

      tableData.push(row)
      resolve(tableData);
    }
    serialRefreshs(tableData)
  });
  return promise;
}


/**
 * 表格行计算
 * @param {number} bNumb 数量
 * @param {number} bfWsdj 未税单价
 * @param {number} bPrice 含税单价
 */
function tablePurchaseLineCount(bNumb = 0, bfWsdj = 0, bPrice = 0) {
  let bfWsje = parseFloat(bfWsdj) * parseFloat(bNumb) || 0; //未税金额
  let bfCalje = parseFloat(bPrice) * parseFloat(bNumb) || 0; //价税合计
  let bfSe = parseFloat(bfCalje) - parseFloat(bfWsje) || 0; //税额

  return {
    bfWsje,
    bfCalje,
    bfSe
  };
}

/**
 * 表格行计算
 * @param {number} bNumb 数量
 * @param {number} bfWsdj 未税单价
 * @param {number} bPrice 含税单价
 */
function tableOrderLineCount(bNumb = 0, bfWsdj = 0, bPrice = 0, gPrice = 0) {
  let bfWsje = parseFloat(bfWsdj) * parseFloat(bNumb) || 0; //未税金额
  let bfCalje = parseFloat(bPrice) * parseFloat(bNumb) || 0; //价税合计
  let bfSe = parseFloat(bfCalje) - parseFloat(bfWsje) || 0; //税额
  let bfCalml = bfWsje - parseFloat(bNumb) * parseFloat(gPrice) || 0; //库存总成本

  return {
    bfWsje,
    bfCalje,
    bfSe,
    bfCalml
  };
}

/**
 * 刷新表格序号
 * @param {array} tableData
 */
function serialRefreshs(tableData = []) {
  tableData.forEach((item, index) => {
    item.num = index + 1;
  });
}

export {
  importHistoryTableData,
  tableOrderLineCount,
  tablePurchaseLineCount
}