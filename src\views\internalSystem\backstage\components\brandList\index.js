import API from '@/api/internalSystem/basicManage/brand'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
export default {
  name: "brandList",
  components: {
    TableView,
    Pagination
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        brand_type: ""
      },
      tableList: [{
          name: "产品服务",
          value: "brand_type"
        },
        {
          name: "软件证书编号",
          value: "brand_no"
        },
        {
          name: "成本价（元）",
          value: "brand_cost_price",
          width: 120
        },
        {
          name: "建议售价（元）",
          value: "brand_sell_price",
          width: 124
        },
        {
          name: "品牌分类",
          value: "brand_classify_name",
          width: 120
        }
      ]
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "产品列表"
    },
    brandClassify: {
      type: Number
    }
  },
  methods: {
    Show() {
      this.dialogVisible = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          this.getList();
        });
      // }, 300);
    },
    getList(f = false) {
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.brand_pagination.obtain());
      if (f)
        param.pageNum = 1;
      if (this.brandClassify)
        param.brand_classify = this.brandClassify;
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.brand_pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    //提交
    submitForm() {
      if (this.selectRecords.length == 0)
        return this.error("请至少选择一条记录");
      this.$emit("getInfo", this.selectRecords);
      this.dialogCancel();
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.selectRecords = [];
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
  }
};