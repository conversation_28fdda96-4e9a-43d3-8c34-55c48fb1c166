<template>
  <div class="congested">
    <loginView :rememb="false" title="内部系统" @userLogin="userLogin" :isinternal="true" />
  </div>
</template>

<script>
import loginView from "@/components/login/index.vue";
import routerList from "@/router/internalSystem/backstage/index";
import { forEachRouterPath } from "@/utils/router.js";
export default {
  data() {
    return {
      routerMenu: [],
      pathsList: [],
      jumpPathList: [],
      allRouterList: [] //权限的所有可跳转路由菜单
    };
  },
  components: {
    loginView
  },
  mounted() {
    this.routerMenu = forEachRouterPath(
      routerList[0].children,
      routerList[0].path
    );
    this.getJumpListPath(this.routerMenu)
    sessionStorage.removeItem('tabs')
  },
  methods: {
    userLogin() {
      this.$router.push({name:"statisticsPage"})

    },
    //登录路由跳转逻辑
    currJumpPath(body) {
      this.allRouterList = []
      this.judgeAllRouterList(body) //遍历取得权限菜单所有的可跳转权限
      let callBackPath = this.$route.query['callBackPath'] || ''
      if (!this.allRouterList.length) {
        this.warning(
          "系统检测当前账号没有可登录本系统具体单据的权限，请联系管理员在吉勤云平台权限管理当中配置。"
        )
        return
      }
      if (callBackPath && this.allRouterList.indexOf(callBackPath) !== -1) {
        //如果上次跳转的地址存在权限可跳转列表中就跳转到那个路由路径
        this.$router.replace(callBackPath);
        return;
      }

      this.$router.replace(this.allRouterList[0]);
    },
    getJumpListPath(data) {
      //递归传递所有路由路径
      data.forEach(item => {
        if (item.path && !item.redirect) this.jumpPathList.push(item.prift);
        if (item.children && item.children.length)
          this.getJumpListPath(item.children);
      });
    },
    judgeFirstRouter(menuData = []) {
      //递归查询第一个可跳转路由
      for (let i = 0; i < menuData.length; i++) {
        let item = menuData[i];
        if (this.jumpPathList.indexOf(item.path) !== -1) {
          this.flag = true;
          return this.$router.replace(item.path);
        }
        if (item.children && item.children.length) {
          this.judgeFirstRouter(item.children);
        }
      }
    },
    judgeAllRouterList(menuData = []) {
      //递归查询权限菜单所有可跳转路由
      for (let i = 0; i < menuData.length; i++) {
        let item = menuData[i];
        if (this.existence(this.jumpPathList, item.path)) {
          this.allRouterList.push(item.path)
        }
        if (item.children && item.children.length) {
          this.judgeAllRouterList(item.children)
        }
      }
    }
  }
};
</script>
