<template>
  <div class="detailContainer">
    <div class="head">
      <div class="left">
        <span class="tip">{{ head.problem_id }}</span>
        <span class="title">{{ head.problem_title }}</span>
      </div>
      <ul class="right" v-if="!outside">
        <li @click="assign"><i class="el-icon-thumb rotate90"></i> 指派</li>

        <li @click="activation" v-if="head.problem_state === 3">
          <i class="el-icon-magic-stick"></i> 激活
        </li>
        <li @click="solve" v-if="head.problem_state === 1">
          <i class="el-icon-check"></i> 解决
        </li>
        <li @click="close" v-if="head.problem_state === 2">
          <i class="el-icon-close"></i> 关闭
        </li>
        <li @click="edit"><i class="el-icon-edit"></i> 编辑</li>
        <li @click="copy"><i class="el-icon-document-copy"></i> 复制</li>
        <li @click="share"><i class="el-icon-share"></i> 分享</li>

        <!-- <li>
          <i class="el-icon-edit icon" @click="edit" title="编辑"></i>
          <i
            class="el-icon-document-copy icon"
            @click="copy"
            title="复制Bug"
          ></i>

          <i class="el-icon-close icon" @click="close" title="关闭"></i>
         
        </li> -->
      </ul>
      <!-- <ul class="right">
        <li></li>
      </ul> -->
    </div>

    <div class="main">
      <div class="left">
        <fieldset class="content">
          <legend class="content-title">重现步骤</legend>
          <div class="content-body">
            <div v-html="head.problem_content"></div>
          </div>
        </fieldset>
        <fieldset class="content">
          <legend class="content-title">附件</legend>
          <div class="content-body">
            <div
              class="content-body-item"
              v-for="(fileItem, fIndex) in head.file || []"
              :key="fIndex"
            >
              <i class="el-icon-document"></i>
              <span
                class="fileName"
                :title="fileItem.file_url"
                @click="downFile(fileItem.file_url)"
                >{{
                  fileItem.problem_file_name
                    ? `${fileItem.problem_file_name}.${fileItem.file_suffix}`
                    : fileItem.file_name
                }}</span
              >
              <i class="ml5 el-icon-close hide"></i>
            </div>
          </div>
        </fieldset>

        <HisRecord :recordList="head.problem_record" />
      </div>
      <div class="right">
        <RightBox title="基本信息" :outside="outside" :infoData="head" :infoList="infoList" />

        <RightBox
          class="mt10"
          title="bug的一生"
          :infoData="head"
          :infoList="bugInfoList"
        />
      </div>
    </div>
    <Dialog ref="dialog" :type="stateType" @success="dialogSuccess" />
  </div>
</template>

<script>
import API from "@/api/internalSystem/bugManage/bugContent/index.js";
import RightBox from "./components/RightBox/index.vue";
import HisRecord from "../components/HisRecord/index.vue";
import Dialog from "../components/dialog/index.vue";
export default {
  name: "bugDetail",
  props: {
    outside: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      head: {
        title: "bug标题",
        problem_content: ``,
      },
      infoList: [
        { label: "所属产品", field: "brand_name" },
        { label: "所属模块", field: "module_name" },

        { label: "Bug类型", field: "problem_type_name" },
        { label: "严重程度", field: "problem_severity" },
        { label: "优先级", field: "problem_priority" },
        { label: "Bug状态", field: "problem_state_name" },
        // { label: "激活次数", field: "" },
        // { label: "激活日期", field: "" },
        { label: "是否确认", field: "is_confirm_name" },
        { label: "当前指派", field: "designated_person_name" },
        { label: "截止日期", field: "deadline" },
        { label: "操作系统", field: "operating_system_name" },
        { label: "浏览器", field: "problem_browser_name" },
      ],
      bugInfoList: [
        { label: "由谁创建", field: "add_user_name" },
        { label: "由谁解决", field: "solve_person_name" },
        { label: "解决方案", field: "solution_name" },
        // { label: "由谁关闭", field: "youxian" },
        { label: "最后修改", field: "update_user_name" },
      ],
      problem_id: "",
      stateType: "",
    };
  },
  activated() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      let params = {};
      if (!this.$route.query.problem_id) return;
      this.problem_id = this.$route.query.problem_id;
      params.problem_id = this.$route.query.problem_id;
      API.getBugInfo(params).then((res) => {
        this.head = res.data;
      });
    },

    assign() {
      this.stateType = 1;
      this.$refs.dialog.Show(this.head.problem_id);
    },
    activation() {
      if (this.head.problem_state === 2)
        return this.$error("非关闭状态不可激活");

      this.stateType = 5;
      this.$refs.dialog.Show(this.head.problem_id);
    },
    close() {
      if (this.head.problem_state !== 2)
        return this.$error("非解决完成状态不可关闭");

      this.stateType = 4;
      this.$refs.dialog.Show(this.head.problem_id);
    },
    edit() {
      this.$router.push({
        path: "/backstage/bugManage/bugContent/addBug",
        query: {
          problem_id: this.head.problem_id,
          type: "edit",
          returnRoutePath: "/backstage/bugManage/bugContent/bugDetail",
        },
      });
    },
    copy() {
      this.$router.push({
        path: "/backstage/bugManage/bugContent/addBug",
        query: {
          problem_id: this.head.problem_id,
          type: "copy",
          returnRoutePath: "/backstage/bugManage/bugContent/bugDetail",
        },
      });
    },
    solve() {
      this.stateType = 3;
      this.$refs.dialog.Show(this.head.problem_id);
    },
    dialogSuccess() {
      let params = {};
      params.problem_id = this.problem_id;
      API.getBugInfo(params).then((res) => {
        this.head = res.data;
      });
    },
    downFile(url) {
      window.open(url);
    },
    copyText(e) {
    },

    share() {
      let origin = window.location.origin;
      this.$copyText(
        `${origin}/#/outside/outsideBugDetail?problem_id=${this.problem_id}`
      ).then(
        (res) => {
          this.success("链接已复制到剪切板，快去分享吧");
        },
        (err) => {
          console.log(err); //链接复制失败
        }
      );
    },
  },
  components: {
    RightBox,
    HisRecord,
    Dialog,
  },
};
</script>

<style lang="scss" scoped>
.detailContainer {
  width: 100%;
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
  .head {
    display: flex;
    justify-content: space-between;
    padding: 5px;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
    .left {
      .tip {
        border: 1px solid #777;
        color: #777;
        padding: 0 4px;
        font-size: 12px;
      }
      .title {
        font-weight: bold;
        padding-left: 10px;
        font-size: 13px;
        color: #333;
      }
    }
    .right {
      float: right;
      li {
        display: inline-block;
        padding: 0 10px;
        color: #036;
        cursor: pointer;
        font-size: 14px;
        .icon {
          padding: 0 5px;
        }
      }
    }
  }
  .content {
    margin-bottom: 15px;
    border: 1px solid #e5e5e5;
    padding: 10px 15px 15px;
    .content-title {
      color: #333;
      border: 0;
      width: auto;
      margin: 0 0 0 -5px;
      font-size: 13px;
      font-weight: bold;
      border-bottom: 0;
      padding: 0 5px;
    }
    .content-body {
      .content-body-item {
        cursor: pointer;
        .hide {
          display: none;
        }
        .fileName {
          font-size: 13px;
        }
      }
      .content-body-item-his {
        font-size: 14px;
      }
      .content-body-item:hover {
        .hide {
          display: inline-block;
        }
      }
    }
  }

  .main {
    display: flex;
    .left {
      flex: 1;
    }
    .right {
      flex: 0 0 300px;
      margin-left: 15px;
    }
  }
}
</style>
