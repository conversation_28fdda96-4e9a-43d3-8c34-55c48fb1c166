import API from '@/api/internalSystem/basicManage/costDetails'
import projectAPI from '@/api/internalSystem/basicManage/costProject'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddCostDetails from "./components/addCostDetails/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import {
  mapGetters
} from "vuex";
export default {
  name: "costDetails",
  data() {
    return {
      title: "费用明细",
      loading: false,
      tableData: [],
      formSearch: {
        fk_financial_cost_project_id: ""
      },
      tableList: [{
          name: "费用明细编码",
          value: "cost_detail_no"
        },
        {
          name: "费用明细名称",
          value: "cost_detail_name"
        },
        {
          name: "费用项目编码",
          value: "project_no"
        },
        {
          name: "费用项目名称",
          value: "project_name"
        },
        {
          name: "费用明细说明",
          value: "cost_description"
        }
      ],
      costProjectList: []
    };
  },

  mounted() {
    projectAPI.query({}).then(res => {
      this.costProjectList = res.data;
    }).finally(() => {});
    this.getList();
  },
  methods: {
    getList(f = false) {
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
      if (f)
        param.pageNum = 1;
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    add() {
      this.$refs.AddCostDetails.Show();
    },
    //打开修改费用明细会话框
    modify(item) {
      let params = {
        financial_cost_details_id: item.financial_cost_details_id
      };
      API.getInfo(params)
        .then(data => {
          this.$refs.AddCostDetails.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      let params = {
        financial_cost_details_id: item.financial_cost_details_id
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    }
  },

  components: {
    AddCostDetails,
    Pagination,
    TableView
  },
  computed: {
    ...mapGetters(["buttonPermissions"])
  }
};