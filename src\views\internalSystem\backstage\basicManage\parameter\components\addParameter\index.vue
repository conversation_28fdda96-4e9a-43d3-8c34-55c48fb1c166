<template>
	<el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="660px"
	           :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
		<el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
			<el-form-item label="参数类型" prop="parameter_type">
				<el-select v-model="ruleForm.parameter_type" placeholder="请选择" class="w100" filterable
				           clearable>
					<el-option v-for="item in params_constant_parameter_type" :key="item.id" :label="item.label"
					           :value="item.id">
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="税率比例" prop="content">
				<el-input v-model="ruleForm.content" />
			</el-form-item>
		</el-form>
		<span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm('ruleForm')">保 存</el-button>
      <el-button @click="dialogCancel">取 消</el-button>
    </span>
	</el-dialog>
</template>
<script src="./index.js">

</script>
