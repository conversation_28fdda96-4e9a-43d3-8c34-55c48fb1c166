import API from '@/api/internalSystem/customerManage/article'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  getOptions,
} from "@/common/internalSystem/common.js"
import {
  mapGetters
} from "vuex";
export default {
  name: "contractList",
  components: {
    TableView,
    Pagination,
    MyDate
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        publish_man: "",
        copy_state: "",
        keywords:"",
        title:"",
        type:""
      },
      tableList: [],
      sellTypeList: [],
      employeeList: [],
      typeList:[]
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "合同列表"
    },
    copyState: {
      type: Number,
      default: null
    },
    

  },
  methods: {
    Show() {
      this.tableList = [{
        name: "状态",
        value: "copy_state_name",
        width: 120
      },
      {
        name: "主题",
        value: "title"
      },
      {
        name: "二级关键词",
        value: "keywords"
      },
      {
        name: "发布人",
        value: "publish_man_name",
        width: 120
      },
      {
        name: "类型",
        value: "type_name",
        width: 120
      },
      {
        name: "访问量",
        value: "traffic",
        width: 100
      },
      {
        name: "部门",
        value: "department_name",
        width: 120
      },
      {
        name: "审核备注",
        value: "audit_remark",
        width: 200
      },
      {
        name: "创建时间",
        value: "add_time",
        width: 120
      }
    ]
      this.dialogVisible = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          this.getList();
          this.$store.dispatch('getEmployee').then(res => {
            this.employeeList = res;
          });
          this.typeList = getOptions('t_copy', 'type');
        });
      // }, 300);
    },
    getList(f = false) {
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.con_pagination.obtain());
      if (f)
      param.pageNum = 1;
      if(this.copyState){
        param.copy_state = this.copyState
      }
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.con_pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    //提交
    submitForm() {
        if (this.selectRecords.length !== 1){
          return this.error("请选择一条文案进行推送");
        }
        // if (this.selectRecords.length !== 1){
        //   return this.error("只能选择一条文案进行推送");
        // }

        this.$emit("getInfo", this.selectRecords[0]);
    
      this.dialogCancel();
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.selectRecords = [];
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
    rowDblclick(row, column, event) {
      this.selectRecords = []
      this.selectRecords.push(row)
      this.submitForm()
    }
  },
  computed: {
    ...mapGetters(["buttonPermissions", "userInfo", "sell_type"])
  }
};