<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small" v-if="!isAdd">
      <el-form-item label="查询条件">
        <el-input
          v-model="formSearch.customer_name"
          placeholder="请输入客户名称"
          clearable
          style="width: 150px"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.fk_sell_employee_id"
          placeholder="请选择销售员"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
          :disabled="!['总经理'].includes(cookiesUserInfo.role_name)"
    
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number + '-' + item.employee_name"
            :value="item.employeeId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.startTime"
          hint="请选择开始时间"
          style="width: 160px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.endTime"
          hint="请选择结束时间"
          style="width: 160px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
        <el-button type="primary" @click="add" v-permit="'ADD_OUTBOUND_NEW'"
          >制单</el-button
        >
      </el-form-item>
    </el-form>
    <table-view
      :tableList="tableList"
      :tableData="tableData"
      v-if="!isAdd"
      @del="del"
      @modify="modify"
      isEdit="permanent_button"
      :isDel="'DEL_OUTBOUND_NEW'"
    >
    </table-view>
    <div class="mt10 moneyTitle" v-if="!isAdd">
      <el-row :gutter="20">
        <el-col :span="6">出库总金额：{{ allList.amount }}元</el-col>
      </el-row>
    </div>
    <Pagination ref="pagination" @success="getList" v-show="!isAdd" />
    <!-- 新增销售出库单 -->
    <AddOutbound ref="addOutbound" @selectData="getList" />
  </div>
</template>

<script src="./index.js">
</script>
<style lang="scss" scoped>
.moneyTitle {
  font-size: 14px;
  font-weight: bold;
}
</style>