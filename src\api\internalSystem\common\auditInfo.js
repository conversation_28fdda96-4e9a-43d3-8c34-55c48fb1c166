import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 文案获取单条信息
  articleInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}copy/getInfo`, params)
  },
  // 意向单获取单条信息
  intentionInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}customerIntention/getInfo`, params)
  },
  // 实施单获取单条信息
  impleInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/getInfo`, params)
  },
  // 回访单获取单条信息
  visitingInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}salesVisiting/getInfo`, params)
  },
  // 修改单获取单条信息
  updateInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}customer/customerTempInfo`, params)
  },
  // 报价单获取单条信息
  quotationInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}quotation/getInfo`, params)
  },
  // 合同单获取单条信息
  contractInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/getInfo`, params)
  },
  // 开票单获取单条信息
  ticketInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}openTicket/getInfo`, params)
  },
  // 其他收入单获取单条信息
  incomeInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}income/getInfo`, params)
  },
  // 报销单获取单条信息
  invoiceInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}invoice/getInfo`, params)
  },
  // 发票进项单获取单条信息
  invoiceIncomeInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}invoiceIncome/getInfo`, params)
  },
  // 收款单获取单条信息
  receivablesInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}receivables/getInfo`, params)
  },
};