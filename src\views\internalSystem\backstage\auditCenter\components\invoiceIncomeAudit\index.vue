<template>
  <div v-if="dialogVisible">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="openAudit">审 核</el-button>
    </div>
    <el-form :model="ruleForm" ref="ruleForm" label-width="100px" class="mt10">
      <el-row>
        <el-col :span="8">
          <el-form-item label="单据编号">
            <el-input v-model="ruleForm.invoice_income_no" disabled clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="开票单位" prop="invoice_unit">
            <el-input disabled v-model="ruleForm.invoice_unit" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="发票明细" prop="invoice_detail">
            <el-input disabled v-model="ruleForm.invoice_detail" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="发票号码" prop="invoice_number">
            <el-input disabled v-model="ruleForm.invoice_number" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="收票金额" prop="invoice_money">
            <el-input disabled v-model="ruleForm.invoice_money"  clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="发票时间" prop="invoice_time">
            <my-date disabled v-model="ruleForm.invoice_time" hint="请选择发票时间" :isAllDate="false"></my-date>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog title="审核" :visible.sync="dialogVisibleAudit" append-to-body @close="dialogCancelAudit" width="660px"
      :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
      <el-form :model="auditForm" :rules="rules" ref="auditForm" label-width="100px">
        <!-- <el-form-item label="审核状态" prop="auditState">
          <el-select v-model="auditForm.auditState" placeholder="请选择审核状态" filterable clearable>
            <template v-for="item in contract_auditStateList">
              <el-option v-if="item.id!=ruleForm.audit_state&&item.id!=3" :key="item.id"
                :label="item.label" :value="item.id">
              </el-option>
            </template>
          </el-select>
        </el-form-item> -->
        <el-form-item label="审核状态" prop="auditState">
          <el-radio-group v-model="auditForm.auditState">
            <template v-for="item in contract_auditStateList">
              <el-radio
              v-if="item.id!=ruleForm.audit_state&&item.id!=3"
                :key="item.id"
                :label="item.value"
                :value="item.value"
                border
                style="height: 30px;"
                ><span style="font-size:16px;">{{ item.label }}</span></el-radio
              >
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" clearable></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('auditForm')" :loading="loading">保 存</el-button>
        <el-button @click="dialogCancelAudit">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script src="./index.js">

</script>