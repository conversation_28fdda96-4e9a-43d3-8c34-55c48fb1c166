import API from "@/api/internalSystem/basicManage/brand";

export default {
  data() {
    return {
      totalCount: 0, //总条数
      pageSize: 5, //每页几条
      pageNum: 1, //当前页数
      dataList: [],
      keywords: "",
      showDetail: false, // 是否显示详情
      noticeList: [],
      detail: "", // 详情内容
      copyId: "",
      userId: "",
      phone: "",
      fkCopyPushLogId: "",
      tempKeywords: "", //存放上一次
    };
  },
  created() {
    /*
     *http://jiqin.jiqinyun.com/#/?coypId=46&userId=2026&phone=18650230177&fkCopyPushLogId=40
     */
    //点击详情后，传递这四个值
    this.copyId = this.getQuery("copyId");
    this.userId = this.getQuery("userId");
    this.phone = this.getQuery("phone");

    //做一个判断，有值直接查详情，显示详情; 没有值，显示搜索页
    this.fkCopyPushLogId = this.getQuery("fkCopyPushLogId");
    if (this.fkCopyPushLogId !== null) {
      this.$nextTick(function() {
        this.getDetail({
          copyId: this.copyId,
          userId: this.userId,
          phone: this.phone,
          fkCopyPushLogId: this.fkCopyPushLogId,
        });
      });
    }
  },

  mounted() {},
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    // 获取url参数
    getQuery(variable) {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
          return pair[1];
        }
      }
      return null;
    },
    //换一波
    next(flag, index) {
      let item = this.noticeList[index];
      if (item["totalCount"] === 0) {
        return;
      }
      if (item["pageNum"] === Math.ceil(item["totalCount"] / this.pageSize)) {
        alert("暂时没有更多结果，请换一个关键词查询");
        return;
      }

      this.query(item["title"], flag, item["pageNum"] + 1, index);
    },
    /* 提交内容 */
    onSubmit(flag) {

      this.query(this.keywords, flag, 1, null);
    },

    /* 点击详情 */
    onDetail(item) {
      this.getDetail({
        copyId: item.id,
      });
      // this.showDetail = true;
    },
    /* 返回 */
    onBack() {
      this.detail = "";
      this.showDetail = false;
    },

    /* 获取服务器数据 */
    getNotice() {
      // let result = this.getDetail()
      let result = {
        code: 1,
        data: [
          {
            id: 47,
            title: "吉勤软件里面正常的销售退货和换货几种特殊情况判断的操作文案",
            traffic: 3,
            keywords: null,
          },
          {
            id: 48,
            title: "11111111",
            traffic: 3,
            keywords: null,
          },
        ],
        msg: "查询成功",
        totalCount: null,
      };
      return result;
    },

    //点击详情
    getDetail(item = {}) {
      axios
        .post("/internalSystemAPI/wechat/question/getInfo", {
          copyId: item.copyId || null,
          userId: item.userId || null,
          phone: item.phone || null,
          fkCopyPushLogId: item.fkCopyPushLogId || null,
        })
        .then((res) => {
          this.detail = res.data.data.copy_content || "";
          this.showDetail = true;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    //用户搜索
    query(value, flag, pageNum = 1, index = null) {
      if (!value) {
        return;
      }
      this.getSearchInfo(value, flag, pageNum, index);
    },
    /* ----------------------------------------网络请求--------------------------------------- */
    /* 获取用户查询结果 */
    getSearchInfo(value, flag, pageNum = 1, index = null) {
      axios
        .post("http://localhost:4016/internalSystemAPI/wechat/question/query", {
          questionTitle: value,
          pageNum: pageNum,
          pageSize: this.pageSize,
        })
        .then((res) => {
          //01=发送  02=换一波
          let notice = {};

          notice.data = res.data.data;
          notice.time = this.filterDateEn(Date.now());
          notice.type = 0;
          if (flag === "01") {
            this.noticeList.push({
              type: 1,
              pageNum: 1, //每次查询的当前页
              totalCount: res.data.totalCount, //每次查询的总数
              title: this.keywords, //每次查询的关键词
              time: this.filterDateEn(Date.now()), //每次查询的时间
            });
            // notice.data = Math.ceil(Math.random() * 10) > 5 ? this.getNotice().data : []
            this.noticeList.push(notice);
            this.tempKeywords = this.keywords;
            this.keywords = "";
          } else {
            this.noticeList[index]["pageNum"] = pageNum; //替换新的当前页
            this.noticeList[index + 1] = notice; //替换查询结果
            this.$forceUpdate();
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    /* -----------------------------------------方法----------------------------------------- */
    filterDateEn(timestamp, format, divider) {
      let date = new Date(timestamp);
      format = format || "YYYY-MM-DD hh:mm";
      let dateObj = {
        YYYY: date.getFullYear(),
        MM: date.getMonth() + 1,
        DD: date.getDate(),
        hh: date.getHours(),
        mm: date.getMinutes(),
        ss: date.getSeconds(),
      };
      dateObj.MM = this.formatNumber(dateObj.MM);
      dateObj.DD = this.formatNumber(dateObj.DD);

      let dateArr = format.split(" ");
      let dateArrl = dateArr[0].split("-").map(function(item) {
        return dateObj[item];
      });
      let dateStr = dateArrl.map(this.formatNumber).join(divider || "-");
      if (dateArr[1]) {
        let dateArrR = dateArr[1].split(":").map(function(item) {
          return dateObj[item];
        });
        dateStr = dateStr + " " + dateArrR.map(this.formatNumber).join(":");
      }

      let today = new Date();
      let todayMD =
        this.formatNumber(today.getMonth() + 1) +
        this.formatNumber(today.getDate());
      if (todayMD === dateObj.MM + dateObj.DD) {
        return dateStr.split(" ")[1];
      } else if (this.getDay(-1).split(" ")[0] === dateStr.split(" ")[0]) {
        return "昨天 " + dateStr.split(" ")[1];
      } else {
        return dateStr;
      }
    },
    getDay(day, format) {
      let today = new Date();
      let targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
      today.setTime(targetday_milliseconds); //注意，这行是关键代码
      let tYear = today.getFullYear();
      let tMonth = today.getMonth();
      let tDate = today.getDate();
      let tHour = today.getHours();
      let tMinutes = today.getMinutes();
      let tSecend = today.getSeconds();
      tMonth = this.formatNumber(tMonth + 1);
      tDate = this.formatNumber(tDate);
      tHour = this.formatNumber(tHour);
      tMinutes = this.formatNumber(tMinutes);
      tSecend = this.formatNumber(tSecend);
      switch (format) {
        case "YYYY-MM-DD":
          return tYear + "-" + tMonth + "-" + tDate;
        case "YYYY-MM-DD hh:mm:ss":
          return (
            tYear +
            "-" +
            tMonth +
            "-" +
            tDate +
            " " +
            tHour +
            ":" +
            tMinutes +
            ":" +
            tSecend
          );
        default:
          return (
            tYear +
            "-" +
            tMonth +
            "-" +
            tDate +
            " " +
            tHour +
            ":" +
            tMinutes +
            ":" +
            tSecend
          );
      }
    },
    formatNumber(n) {
      n = n.toString();
      return n[1] ? n : "0" + n;
    },
  },

  components: {},
};
