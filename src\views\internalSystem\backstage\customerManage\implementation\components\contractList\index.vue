<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="60%"
    :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
    <el-form :inline="true" :model="formSearch" size="small">
      <el-form-item label="查询条件">
        <el-input v-model="formSearch.customer_name" placeholder="请输入客户名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="formSearch.fk_sell_employee_id" placeholder="请选择销售员" class="inputBox" filterable clearable>
          <el-option v-for="item in employeeList" :key="item.employeeId" :label="item.employee_number+'-'+item.employee_name"
            :value="item.employeeId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList" :loading="loading">查询</el-button>
             <el-button type="primary" @click="submitForm">选择</el-button>
             <el-button @click="dialogCancel">取 消</el-button>

      </el-form-item>
    </el-form>
    <table-view :tableList="tableList" :tableData="tableData" :tableHeight="520"
      @getSelectRecords="getSelectRecords" :isSel="true" :isDblclick="true" @rowDblclick="rowDblclick"></table-view>
    <Pagination ref="cus_pagination" @success="getList" />
    <span slot="footer" class="dialog-footer">
      <!-- <el-button type="primary" @click="submitForm">选择</el-button>
      <el-button @click="dialogCancel">取 消</el-button> -->
    </span>
  </el-dialog>
</template>
<script src="./index.js">

</script>