import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: (params) => {
    return Axios.post(`${environment.internalSystemAPI}copy/query`, params);
  },
  // 新增
  add: (params) => {
    return Axios.post(`${environment.internalSystemAPI}copy/add`, params);
  },
  // 删除
  remove: (params) => {
    return Axios.post(`${environment.internalSystemAPI}copy/remove`, params);
  },
  // 编辑
  update: (params) => {
    return Axios.post(`${environment.internalSystemAPI}copy/update`, params);
  },
  // 获取单条信息
  getInfo: (params) => {
    return Axios.post(`${environment.internalSystemAPI}copy/getInfo`, params);
  },
  // 审核
  updateAudit: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}copy/updateAudit`,
      params
    );
  },
  // 修改默认推送标志
  updateDefault: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}copy/updateDefault`,
      params
    );
  },
  // 推送文案
  copy_push: (params) => {
    return Axios.post(`${environment.internalSystemAPI}copy/push`, params);
  },
};
