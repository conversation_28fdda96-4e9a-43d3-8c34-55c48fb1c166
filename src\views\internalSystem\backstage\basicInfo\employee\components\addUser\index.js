import API from '@/api/internalSystem/basicInfo/employee/employeeApi.js'
import departmentAPI from '@/api/internalSystem/basicInfo/department/departmentApi.js'
import roleAPI from '@/api/internalSystem/basicInfo/role/roleApi.js'
import comAPI from '@/api/internalSystem/common/index.js'
import validationRules from "@/common/internalSystem/validationRules.js"
import {
  mapGetters
} from "vuex";
import {
  getBirthday
} from "@/common/internalSystem/common.js"
export default {
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [{
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            }
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            }
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            }
          }
        ]
      },
      dialogTitle: "新增员工",
      dialogVisible: false,
      ruleForm: {
        employee_number: "",
        telephone: "",
        employee_name: "",
        education: "",
        id_card: "",
        graduate_school: "",
        gender: "",
        fk_dept_id: "",
        birthday: "",
        role_id: "",
        province: "",
        superior_id: "",
        city: "",
        entry_time: "",
        county: "",
        departure_time: "",
        origin_place: "",
        now_address: "",
        email: "",
        remarks: "",
        card_time:"",
        professional:"",
        emergency_call:"",
        native_place:"",
        positive_time:"",
        contract_expires:"",
        annual_leave:"",
        construction_bank:"",
        in_position:""
      },
      rules: {
        employee_number: [{
          required: true,
          message: "请输入员工工号",
          trigger: "blur"
        }],
        telephone: [{
          required: true,
          validator: validationRules.checkPhone,
          trigger: "blur"
        }],
        employee_name: [{
          required: true,
          message: "请输入员工名称",
          trigger: "blur"
        }],
        id_card: [{
          required: true,
          message: "请输入身份证号",
          trigger: "blur"
        }],
        gender: [{
          required: true,
          message: "请选择性别",
          trigger: "change"
        }],
        fk_dept_id: [{
          required: true,
          message: "请选择所属部门",
          trigger: "change"
        }],
        birthday: [{
          required: true,
          message: "请选择出生日期",
          trigger: "blur"
        }],
        role_id: [{
          required: true,
          message: "请选择岗位",
          trigger: "change"
        }],
        province: [{
          required: true,
          message: "请选择所在省份",
          trigger: "change"
        }],
        city: [{
          required: true,
          message: "请选择所在城市",
          trigger: "change"
        }],
        entry_time: [{
          required: true,
          message: "请选择入职时间",
          trigger: "blur"
        }],
        email: [{
          type: "email",
          message: "请输入正确的邮箱格式",
          trigger: "blur"
        }],
        native_place: [{
          required: true,
          message: "请填写籍贯",
          trigger: "blur"
        }],
        card_time: [{
          required: true,
          message: "请选择身份证期限",
          trigger: "blur"
        }],
        emergency_call: [{
          required: true,
          message: "请填写紧急联系电话",
          trigger: "blur"
        }]
      },
      departmentList: [], //部门下拉框选择
      jobList: [], //岗位
      sexList: [], //性别
      superiorList: [], //上级
      provinceList: [], //省
      cityList: [], //市
      countyList: [], //区
      loading: false,
      isEdit: false
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      
      this.getDepartmentList();
      this.getProvinceList();
      
      if (!data) {
        this.isEdit=true;
        this.dialogTitle = "新增员工"
      } else {
        this.isEdit = this.curRoutePermissions.some(item => item.menuDescribe === 'UPDATE_EMPLOYEE_NEW') || false
        this.dialogTitle = "修改员工";
        this.ruleForm = data;
        this.getJobList();
        this.getSuperiorList();
        this.getCityList();
        this.getCountyList();
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    save() {
      let params = this.ruleForm;
      params.id = params.employeeId
      this.loading = true;

      if (params.employeeId) {
        API.update(params)
          .then(() => {
            this.dialogCancel();
            this.$store.commit('GET_EMPLOYEE', [])
          })
          .catch(() => {}).finally(() => {
            this.loading = false;
          });
      } else {
        API.add(params)
          .then(() => {
            this.dialogCancel();
            this.$store.commit('GET_EMPLOYEE', [])
          })
          .catch(() => {}).finally(() => {
            this.loading = false;
          });
      }
    },
    //获取部门列表
    getDepartmentList() {
      departmentAPI
        .query({})
        .then(data => {
          this.departmentList = data.data;
        })
        .catch(() => {});
    },
    //更改部门
    changeDepartment() {
      this.jobList = [];
      this.ruleForm.role_id = "";
      this.superiorList = [];
      this.ruleForm.superior_id = "";
      this.getJobList();
      this.getSuperiorList();
    },
    //获取岗位列表
    getJobList() {
      if (!this.ruleForm.fk_dept_id) return;
      roleAPI
        .query({
          departmentId: this.ruleForm.fk_dept_id
        })
        .then(data => {
          this.jobList = data.data;
        })
        .catch(() => {});
    },
    //获取上级列表
    getSuperiorList() {
      if (!this.ruleForm.fk_dept_id) return;
      API
        .query({
          fk_dept_id: this.ruleForm.fk_dept_id
        })
        .then(data => {
          let list = [];
          if (this.ruleForm.employeeId) {
            data.data.forEach(item => {
              if (item.employeeId != this.ruleForm.employeeId)
                list.push(item);
            })
          } else {
            list = data.data;
          }
          this.superiorList = list;
        })
        .catch(() => {});
    },
    //获取省列表
    getProvinceList() {
      comAPI
        .queryAreaCode({
          level: 1
        })
        .then(data => {
          this.provinceList = data.data;
        })
        .catch(() => {});
    },
    //更换省
    changeProvince() {
      this.cityList = [];
      this.ruleForm.city = "";
      this.getCityList();
    },
    //获取市列表
    getCityList() {
      if (!this.ruleForm.province) return;
      comAPI
        .queryAreaCode({
          level: 2,
          province: this.ruleForm.province
        })
        .then(data => {
          this.cityList = data.data;
        })
        .catch(() => {});
    },
    //更换市
    changeCity() {
      this.countyList = [];
      this.ruleForm.county = "";
      this.getCountyList();
    },
    //获取区列表
    getCountyList() {
      if (!this.ruleForm.city || !this.ruleForm.province) return;
      comAPI
        .queryAreaCode({
          level: 3,
          province: this.ruleForm.province,
          city: this.ruleForm.city
        })
        .then(data => {
          this.countyList = data.data;
        })
        .catch(() => {});
    },
    //获取生日
    getBirthday() {
      this.ruleForm.birthday = getBirthday(this.ruleForm.id_card);
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        employee_number: "",
        telephone: "",
        employee_name: "",
        education: "",
        id_card: "",
        graduate_school: "",
        gender: "",
        fk_dept_id: "",
        birthday: "",
        role_id: "",
        province: "",
        superior_id: "",
        city: "",
        entry_time: "",
        county: "",
        departure_time: "",
        origin_place: "",
        now_address: "",
        email: "",
        remarks: "",
        card_time:"",
        professional:"",
        emergency_call:"",
        native_place:"",
        positive_time:"",
        contract_expires:"",
        annual_leave:"",
        construction_bank:"",
        in_position:""
      }
    }
  },
  computed: {
    ...mapGetters(['curRoutePermissions', 'params_constant_gender', 'in_position'])
  }
};
