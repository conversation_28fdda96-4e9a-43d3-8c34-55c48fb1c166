<template>
	<div class="body-p10">
		<el-form :inline="true" :model="formSearch" size="small">
			<el-form-item label="公司名称">
				<el-input v-model="formSearch.company_name" placeholder="请输入公司名称"></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
				<el-button v-permit="'add'" @click="$refs.update.show()" type="success">添加
				</el-button>
			</el-form-item>
		</el-form>
		<table-view
			:tableList="tableList"
			:tableData="tableData"
			@del="del"
			@modify="row => $refs.update.show(row)"
			isEdit='update'
			isDel='del'
		/>
		<Pagination ref="pagination" @success="getList"/>
		<update ref="update" @success="getList" />
	</div>
</template>

<script src="./index.js"></script>
