<template>
  <div class="treeContainer2">
    <div class="head">
      <AddTrainingVideo :directory_id="directory_id" @getList="getList" />
      <div style="margin-top: 30px" />
      <vxe-table
        :id="'trainingFile'"
        column-key
        border
        resizable
        highlight-hover-row
        highlight-current-row
        auto-resize
        ref="xTable"
        :height="600"
        :loading="false"
        header-row-class-name="head-row-class"
        align="center"
        @sort-change="sortChange"
        :checkbox-config="{
          reserve: true,
          showHeader: true,
          trigger: 'row',
        }"
        :custom-config="{
          storage: {
            visible: true,
            resizable: true,
          },
        }"
        :data="tableData"
        size="small"
        :keyboard-config="{ isArrow: true }"
        :edit-config="{ trigger: 'click', mode: 'cell' }"
      >
        <vxe-table-column prop="file_name" label="视频名称"> </vxe-table-column>
        <vxe-table-column prop="enable_flag" label="状态" min-width="">
          <template slot-scope="scope">
            {{ scope.row.enable_flag === 1 ? "启用" : "禁用" }}
          </template>
        </vxe-table-column>

        <vxe-table-column prop="file_number" label="附件数量" min-width="60">
        </vxe-table-column>
        <vxe-table-column prop="traffic" label="访问量" min-width="60">
        </vxe-table-column>
        <vxe-table-column fixed="right" label="操作" min-width="160">
          <template slot-scope="scope">
            <el-button
              @click="addFile(scope.row, scope.$index)"
              type="text"
              size="small"
              >上传文档</el-button
            >
            <el-button
              @click="changeEnableFlag(scope.row, scope.$index)"
              type="text"
              size="small"
              >{{ scope.row.enable_flag === 1 ? "禁用" : "启用" }}</el-button
            >

            <el-button @click="downFile(scope.row)" type="text" size="small"
              >下载</el-button
            >
            <el-button
              @click="removeFile(scope.row, scope.$index)"
              type="text"
              size="small"
              style="color: red"
              >删除</el-button
            >
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <div class="main-tree"></div>

    <AddTrainingFile ref="addTrainingFile" @getList="getList" />
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import "../index.scss";
@import "@/assets/css/element/font-color.scss";
</style>