import API from '@/api/internalSystem/customerManage/salesVisiting'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddVisiting from "./components/addVisiting/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import AuditDetail from '@/mixins/auditDetail.js'
import {
  mapGetters
} from "vuex";
export default {
  name: "salesVisiting",
  mixins: [AuditDetail],
  data() {
    return {
      title: "客户回访单",
      loading: false,
      tableData: [],
      formSearch: {
        customer_name: "",
        fk_sale_employee_id: "",
        auditState: "",
        startTime: "",
        endTime: ""
      },
      tableList: [{
          name: "审核状态",
          value: "auditStateFormat",
          width: 120
        },
        {
          name: "编号",
          value: "sales_visiting_no",
          width: 160
        },
        {
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "联系人",
          value: "link_man",
          width: 120
        },
        {
          name: "联系电话",
          value: "phone",
          width: 120
        },
        {
          name: "联系时间",
          value: "contact_time",
          width: 120
        },
        {
          name: "回访方式",
          value: "visiting_form_format",
          width: 120
        },
        {
          name: "销售员",
          value: "fk_sale_employee_name",
          width: 120
        },
        {
          name: "录入日期",
          value: "updateTime",
          width: 120
        }
      ],
      employeeList: [],
      isAdd: false
    };
  },

  mounted() {
    this.getList();
    this.$store.dispatch('getEmployee').then(res => {
      this.employeeList = res;
    });
  },
  methods: {
    getList(f = false) {
      this.isAdd = false;
      this.isAudit = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
          if (f)
            param.pageNum = 1;
          API.query(param).then(res => {
            this.tableData = res.data;
            this.$refs.pagination.setTotal(res.totalCount);
          }).finally(() => {
            this.loading = false;
          });
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addVisiting.Show();
    },
    modify(item) {
      let params = {
        sales_visiting_id: item.sales_visiting_id
      };
      API.getInfo(params)
        .then(data => {
          this.isAdd = true;
          this.$refs.addVisiting.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      if (item.auditState == 1)
        return this.error("该单据已审核，不允许删除");
      let params = {
        sales_visiting_id: item.sales_visiting_id
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    }
  },

  components: {
    AddVisiting,
    Pagination,
    TableView,
    MyDate
  },
  computed: {
    ...mapGetters(["customer_sales_visiting_audit"])
  }
};