<template>
  <div
    class="body-p10 flex1 orderH100"
    style="overflow-y: auto; overflow-x: hidden;"
    v-if="dialogVisible"
  >
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="openAudit">审 核</el-button>
    </div>
    <el-form :model="ruleForm" ref="ruleForm" label-width="160px" class="mt10 flexAndFlexColumn">
      <el-row :gutter="20">
        <el-col :span="8" class="formItem2" v-if="ruleForm.ticket_id">
          <el-form-item label="单据编号" prop="ticket_no">
            <el-input
              v-model="ruleForm.ticket_no"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2" v-if="ruleForm.ticket_id">
          <el-form-item label="单据日期" prop="ticket_date">
            <el-input
              v-model="ruleForm.ticket_date"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2" v-if="ruleForm.ticket_id">
          <el-form-item label="操作员" prop="update_user_name">
            <el-input
              v-model="ruleForm.update_user_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input disabled v-model="ruleForm.customer_name" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户税号" prop="customer_tax_number">
            <el-input
              v-model="ruleForm.customer_tax_number"
              disabled
              clearable
                      onkeyup="this.value=this.value.replace(/[, ]/g,'')" 
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="销售员" prop="fk_sale_employee_name">
            <el-input
              v-model="ruleForm.fk_sale_employee_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户编号" prop="customer_no">
            <el-input
              v-model="ruleForm.customer_no"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="收票人" prop="receive_ticket_person">
            <el-input
              v-model="ruleForm.receive_ticket_person"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="开户行" prop="open_account_bank">
            <el-input
              v-model="ruleForm.open_account_bank"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="开票地址" prop="open_ticket_address">
            <el-input
              disabled
              v-model="ruleForm.open_ticket_address"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="收票地址" prop="receive_ticket_address">
            <el-input
              disabled
              v-model="ruleForm.receive_ticket_address"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户账号" prop="customer_account">
            <el-input
              disabled
              v-model="ruleForm.customer_account"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="开票电话" prop="open_ticket_phone">
            <el-input
              disabled
              v-model="ruleForm.open_ticket_phone"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="收票电话" prop="receive_ticket_phone">
            <el-input
              disabled
              v-model="ruleForm.receive_ticket_phone"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="销货单位" prop="sales_unit_id_format">
            <el-input
              disabled
              v-model="ruleForm.sales_unit_id_format"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="单据备注" prop="ticket_remark">
            <el-input
              disabled
              v-model="ruleForm.ticket_remark"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2" v-if="ruleForm.ticket_id">
          <el-form-item label="快递单号" prop="express_number">
            <el-input
              disabled
              v-model="ruleForm.express_number"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2" v-if="ruleForm.ticket_id">
          <el-form-item label="发票号码" prop="invoice_number">
            <el-input
              disabled
              v-model="ruleForm.invoice_number"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
              <el-col :span="8" class="formItem2">
          <el-form-item label="剩余可开票总金额" prop="totalOpenMoney">
            <el-input
              disabled
              v-model="totalOpenMoney"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <div  class="flexAndFlexColumn">
      <table-custom
        class="tableContent"
        ref="tableCustom"
        :obj="obj"
        tableHeight="99%"
        :tableCol="tableCol"
        :isDel="false"
      />
      </div>
          <el-dialog
      title="审核"
      :visible.sync="dialogVisibleAudit"
      append-to-body
      @close="dialogCancelAudit"
      width="660px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <el-form
        :model="auditForm"
        :rules="rules"
        ref="auditForm"
        label-width="100px"
      >
        <!-- <el-form-item label="审核状态" prop="auditState">
          <el-select
            v-model="auditForm.auditState"
            placeholder="请选择审核状态"
            filterable
            clearable
          >
            <template v-for="item in contract_auditStateList">
              <el-option
                v-if="
                  item.sysValue != ruleForm.audit_state &&
                  item.sysValue != 4 &&
                  item.sysValue != 3
                "
                :key="item.sysValue"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </template>
          </el-select>
        </el-form-item> -->
        <el-form-item label="审核状态" prop="auditState">
          <el-radio-group v-model="auditForm.auditState">
            <template v-for="item in contract_auditStateList">
              <el-radio
              v-if="
                  item.sysValue != ruleForm.audit_state &&
                  item.sysValue != 4 &&
                  item.sysValue != 3
                "
                :key="item.id"
                :label="item.value"
                :value="item.value"
                border
                style="height: 30px;"
                ><span style="font-size:16px;">{{ item.label }}</span></el-radio
              >
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="发票号码"
          prop="invoice_number"
          v-if="auditForm.auditState == 1"
        >
          <el-input v-model="auditForm.invoice_number" clearable></el-input>
        </el-form-item>
        <el-form-item
          label="快递单号"
          prop="express_number"
          v-if="auditForm.auditState == 1"
        >
          <el-input v-model="auditForm.express_number" clearable></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="auditRemark">
          <el-input
            v-model="auditForm.auditRemark"
            type="textarea"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm('auditForm')"
          :loading="loading"
          >保 存</el-button
        >
        <el-button @click="dialogCancelAudit">取 消</el-button>
      </span>
    </el-dialog>
    </el-form>

  </div>
</template>
<script src="./index.js">
</script>

<style lang="scss" scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown + .el-dropdown {
  margin-left: 15px;
}
@import "@/assets/css/element/font-color.scss";
</style>