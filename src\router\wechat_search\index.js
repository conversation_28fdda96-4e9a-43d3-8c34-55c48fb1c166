import Router from 'vue-router'

const router = new Router({
  routes: [
    {
      path: '/index',
      name: 'search_index',
      component: () => import('@/views/wechat_search/index/index.vue'),
      meta: {
        keepAlive: true, // 缓存
        scrollTop: 0
      }
    },
    {
      path: '/detail',
      name: 'detail',
      component: () => import('@/views/wechat_search/detail/index.vue')
    },
    {
      path: '/',
      redirect: '/index'
    }
  ]
})

router.beforeEach((to, from, next) => {  
  if (from.meta.keepAlive) {
  const $content = document.querySelector('#scroll-container');
  const scrollTop = $content ? $content.scrollTop : 0;
  from.meta.scrollTop = scrollTop;
 }
 next();
});

export default router
