import API from '@/api/internalSystem/basicManage/brand'
import AddJob from "../addJob/index.vue";
export default {
  data() {
    return {
      filterText: ``,
      jobTreeList: [],
      checkedKey: [],
      defaultProps: {
        children: "children",
        label: "brand_type"
      },
      brand_id:""
    };
  },

  mounted() {
    this.getJobTree();
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    filterNode(value, data) {
      // 筛选查询
      if (!value) return true;
      return data.roleName.indexOf(value) !== -1;
    },
    //打开添加岗位会话框
    addJob() {
      let jobObject = {
        module_id: ""
      };
      this.$refs.addJob.Show(jobObject);
    },
    //打开编辑岗位会话框
    editJob(params) {
      let jobObject = {
        module_id: params.module_id
      };
      this.$refs.addJob.Show(jobObject);
    },
    //删除
    delJob(params) {
      this.$confirm("此操作将删除该岗位, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let delIds = [];
          delIds.push(params.module_id);
          API.remove({
            module_id: params.module_id
          })
            .then(() => {
              this.getJobTree();
            })
            .catch(() => {})
            .finally(() => {});
        })
        .catch(() => {
        });
    },
    //获取岗位树
    getJobTree() {
      API.query2({
        brand_classify:1
      })
        .then(data => {
          this.jobTreeList = data.data;
        })
        .catch(() => {})
        .finally(() => {});
    },
    setJob(data) {
     this.brand_id=data.brand_id
      let params = {
        brand_id: data.brand_id,
        brand_type: data.brand_type
      };
      this.$emit("setJob", params);
    }
  },
  components: {
    AddJob
  }
};
