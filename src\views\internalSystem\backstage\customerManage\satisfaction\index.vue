<template>
  <div style="overflow-y:auto;overflow-x: hidden;" class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small" v-if="!isAdd">
      <el-form-item label="查询条件">
        <el-input v-model="formSearch.customer_name" placeholder="请输入客户名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
      </el-form-item>
    </el-form>
    <table-view :tableList="tableList" :tableData="tableData" v-if="!isAdd" isEdit="permanent_button" @modify="modify" isDel="false">
    </table-view>
    <Pagination ref="pagination" @success="getList" v-show="!isAdd" />
    <!-- 满意度 -->
    <satisfaction-info ref="satisfactionInfo" @selectData="getList" />
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
  .moneyTitle {
    font-size: 14px;
    font-weight: bold;
  }
</style>