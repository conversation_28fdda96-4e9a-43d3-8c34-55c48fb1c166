import API from "@/api/internalSystem/contractManage/index.js";

import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";

import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";

import contractManageMixns from "@/mixins/contractManage.js";
import { mapGetters } from "vuex";


import Dialog from "./components/dialog/index.vue"

export default {
  name: "dueSoon",
  mixins: [contractManageMixns],
  data() {
    return {
      title: "快到期合同",
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        employee_number: "",
        employee_name: "",
        in_position: "",
      },
      tableListCop: [
        {
          name: "合同编号",
          value: "employee_number",
          width: 120,
        },
        {
          name: "客户名称",
          value: "employee_name",
        },
        {
          name: "软件名称",
          value: "gender_name",
          width: 80,
        },
        {
          name: "版本号",
          value: "telephone",
          width: 150,
        },
        {
          name: "维护起始时间",
          value: "birthday",
          width: 130,
        },
        {
          name: "维护结束时间",
          value: "id_card",
          width: 180,
        },
        {
          name: "是否回访",
          value: "department_name",
        },
        {
          name: "合作情况",
          value: "department_name2",
        },
      ],
      tableList: [
        {
          name: "完成情况",
          value: "state_name",
          width: 70,
        },
        {
          name: "实施状态",
          value: "work_state_name",
          width: 70,
        },
        {
          name: "回访状态",
          value: "is_return_visit_name",
          width: 70,
        },
        {
          name: "续约状态",
          value: "renew_state_name",
          width: 70,
        },
        
        {
          name: "审核状态",
          value: "audit_state_name",
          width: 82,
        },
        {
          name: "编号",
          value: "contract_no",
          width: 116,
        },
        {
          name: "客户名称",
          value: "customer_name",
          width: 200,
        },
        {
          name: "销货单位",
          value: "sales_unit_id_format",
        },
        {
          name: "维护起始时间",
          value: "maintain_start_time",
          width: 96,
        },
        {
          name: "维护结束时间",
          value: "maintain_stop_time",
          width: 96,
        },
        {
          name: "合同金额",
          value: "contract_amount",
          width: 72,
        },
        {
          name: "已收款金额",
          value: "receivables_amount",
          width: 82,
        },
        {
          name: "未收款金额",
          value: "not_receivables_amount",
          width: 82,
        },
        {
          name: "已开票金额",
          value: "open_ticket_amount",
          width: 82,
        },
        {
          name: "未开票金额",
          value: "not_open_ticket_amount",
          width: 82,
        },
        {
          name: "单据备注",
          value: "remark",
        },
        {
          name: "付款方式",
          value: "pay_type_name",
          width: 106,
        },
        {
          name: "培训方式",
          value: "train_type_name",
          width: 70,
        },
        {
          name: "销售类型",
          value: "sell_type_name",
          width: 82,
        },
        {
          name: "推荐员工",
          value: "fk_recommend_employee_name",
          width: 70,
        },
        {
          name: "操作员工",
          value: "add_user_name",
          width: 70,
        },
        {
          name: "操作部门",
          value: "fk_operator_department_name",
          width: 70,
        },
        {
          name: "手机",
          value: "phone",
          width: 100,
        },
        {
          name: "客户传真",
          value: "fax",
          width: 100,
        },
        {
          name: "联系人",
          value: "link_man",
          width: 70,
        },
        {
          name: "介绍人",
          value: "introducer_format",
        },
        {
          name: "介绍合同",
          value: "introducer_contract_format",
        },
        {
          name: "销售员",
          value: "fk_sell_employee_name",
          width: 70,
        },
        {
          name: "销售部门",
          value: "fk_sell_department_name",
          width: 70,
        },
        {
          name: "单据日期",
          value: "add_time",
          width: 90,
        },
        {
          name: "客户地址",
          value: "address",
        },
        {
          name: "联系人QQ",
          value: "link_qq",
          width: 110,
        },
        {
          name: "软件序列号",
          value: "software_no",
          width: 100,
        },
      ],
      employeeList: [],
    };
  },

  mounted() {
    this.getList();
    this.$store.dispatch("getEmployee").then((res) => {
      this.employeeList = res;
    });
  },
  methods: {
    async getList(f = false) {
      this.isShowDetail = false;
      await this.$nextTick(() => {});
      this.loading = true;
     
      let param = Object.assign(
        this.formSearch,
        this.$refs.pagination.obtain()
      );
      if (f) param.pageNum = 1;
      API.nearExpiredContractList(param)
        .then((res) => {
          this.tableData = res.data;
          this.$refs.pagination.setTotal(res.totalCount);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    canUse() {
      if (!this.selectRecords.length) {
        this.warning("请先勾选1条数据");
        return false;
      }
      if (this.selectRecords.length > 1) {
        this.warning("请只勾选1条数据");
        return false;
      }

      return true;
    },
    confirm() {
      // this.canUse() 
      if(this.canUse()){
        this.customizePrompt("请填写回访结果",/^[\s\S]*.*[^\s][\s\S]*$/,"回访结果不能为空",(value)=>{
          let params={
           customer_contract_id:this.selectRecords[0].customer_contract_id,
           content:value
          }
          this.loading=true;
           API.returnVisit(params).then(()=>{
             this.getList(true)
             this.success("操作成功")
           }).catch(()=>{}).finally(()=>{
             this.loading=false;
           })
           
         })
      }

    },
    
    confirm2(){
      this.canUse()&&this.customizeConfirm("是否确认续约？",()=>{
       API.renewContract({customer_contract_id:this.selectRecords[0].customer_contract_id}).then(()=>{
         this.success("操作成功")
         this.getList()
       })
      })
    },
    thrid(row){
      if(row.is_return_visit!==1) return this.warning("当前记录还未回访，请先回访再查看回访记录。")
      this.success("回访列表")
      this.$refs.dialog.Show(row)
    },

    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
  },

  components: {
    Pagination,
    TableView,
    MyDate,
    Dialog
  },
  computed: {
    ...mapGetters(["in_position","sale_type","customer_contract_audit"]),
  },
};
