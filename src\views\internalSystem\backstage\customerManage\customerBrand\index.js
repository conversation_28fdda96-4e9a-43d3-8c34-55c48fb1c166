import API from "@/api/internalSystem/customerManage/customerBrand";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import AddCustomerBrand from "./components/addCustomerBrand/index.vue";
// import CustomerDetail from "./components/customerDetail/index.vue";
// import CustomerProduct from "./components/customerProduct/index.vue";
// import CustomerTracking from "./components/customerTracking/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import { getOptions, dateFormat } from "@/common/internalSystem/common.js";
import { mapGetters } from "vuex";
import brandAPI from "@/api/internalSystem/basicManage/brand";
export default {
  name: "customerBrand",
  data() {
    return {
      title: "客户基础资料",
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        customerName: "",
        fk_sale_employee_id: "",
        softwareNo:"",
        maintainStartTime: "",
        newMaintainStopTime: "",
        brandId:""
      },
      tableList: [
        {
          name: "产品服务",
          value: "brand_name",
          // width: 150,
        },
        {
          name: "编码",
          value: "customer_brand_no",
          // width: 150,
        },
        {
          name: "客户名称",
          value: "customer_name",
          // width: 150,
        },
        {
          name: "软件序列号",
          value: "software_no",
          width: 110,
        },
        {
          name: "合同金额",
          value: "contract_amount",
          width: 100,
        },

        {
          name: "确认端口数量",
          value: "original_port_count",
          width: 100,
        },
        {
          name: "年维护费比例",
          value: "year_maintain_cost",
          width: 100,
        },

        {
          name: "维护费",
          value: "maintenance_fee",
          width: 100,
        },
        // {
        //   name: "维护开始时间",
        //   value: "maintain_start_time",
        //   width: 110,
        // },
        {
          name: "维护结束时间",
          value: "new_maintain_stop_time",
          width: 110,
        },
        {
          name: "销售员",
          value: "fk_sale_employee_name",
          width: 110,
        },
        {
          name: "产品备注",
          value: "remark",
          width: 110,
        },
        {
          name: "最后更新时间",
          value: "update_time",
          width: 110,
        },
      ],
      customerStageList: [],
      employeeList: [],
      isAdd: false,
      isDetail: false,
      isTracking: false,
      isDblclick: false,
      exLoading: false,
      brandList: [],
    };
  },

  mounted() {
    this.getList();
    this.getBrandList()
    this.$store.dispatch("getEmployee").then((res) => {
      this.employeeList = res;
    });
  },
  methods: {
    getBrandList() {
      brandAPI['query2']()
        .then((data) => {
          data.data.forEach((item) => {
            // if (item.brand_classify == 1)
            this.brandList.push({
              label: item.brand_type,
              value: item.brand_id

            });
          });

        })
    },
    getList(f = false) {
      this.customerStageList = getOptions("t_customer", "customer_stage");
      this.isAdd = false;
      this.isDetail = false;
      this.isTracking = false;
      this.isDblclick = false;
      this.loading = true;
      let param = Object.assign(
        this.formSearch,
        this.$refs.pagination.obtain()
      );
      if (f) param.pageNum = 1;
      param.isJurisdiction = this.permissionToCheck("all") ? 1 : 0;
      API.query(param)
        .then((res) => {
          this.tableData = res.data;
          // if (this.tableData.length > 0) {
          //   this.tableData.map((item) => {
          //     //计算维护费
          //     item['year_maintain_cost']=      item['year_maintain_cost']?      item['year_maintain_cost'] + '%' : 0
          //   });
          // }
          this.$refs.pagination.setTotal(res.totalCount);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    add() {
      this.isAdd = true;
      this.$refs["addCustomerBrand"].getBrand();
    },
    //打开详情
    modify(item) {
      this.isAdd = true;

      this.$refs["addCustomerBrand"].getBrand();
      this.$refs["addCustomerBrand"].Show(item);
    },
    del(item) {
      // console.log(item);
      if (item.customer_stage === "2")
        return this.error("不允许删除已成交客户");
      let params = {
        customer_brand_id: item.customer_brand_id,
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
    //客户跟踪
    tracking(item) {
      this.isTracking = true;
      this.$refs.CustomerTracking.Show(item);
    },
    //推送
    push(item) {
      if (!item.open_id) return this.error("该客户未绑定公众号，推送失败");
      this.$confirm("确定给该客户推送回访单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            customer_id: item.customer_id,
            open_id: item.open_id,
            first: "吉勤软件客户回访",
            keyword1: "吉勤-" + item.fk_sale_employee_id_name,
            keyword2: dateFormat("yyyy-MM-dd"),
            remark: "感谢您的参与，祝您生活愉快",
          };
          API.sales_push(params)
            .then(() => {
              this.getList();
            })
            .catch(() => {});
        })
        .catch(() => {});
    },
    //导出客户
    exportCustomer() {
      this.exLoading = true;
      let param = this.formSearch;
      param.userId = this.userInfo.userId;
      let isJurisdiction = this.buttonPermissions.length
        ? this.existence(this.buttonPermissions, "ALL_CUSTOMER_INFO_NEW")
        : false;
      param.isJurisdiction = isJurisdiction ? 1 : 0;
      let data = {
        url: "excel/customerExport",
        data: param,
      };

      this.ws.send(JSON.stringify(data));
      this.ws.onmessage = (e) => {
        let res = JSON.parse(e.data);

        if (res.code === 1) {
          this.success(res.message);
          this.Download(res.data);
        } else this.error(res.message);
        this.exLoading = false;
      };
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
    /**
     * 双击
     */
    rowDblclick(row, column, event) {
      this.$refs.CustomerAndProduct.Show(row);
      this.isDblclick = true;
    },
    /**
     * 排序
     * @param {*} param0
     */
    sortChange({ column, prop, order }) {
      const dic = {
        ascending: "asc",
        descending: "desc",
      };
      // this.formSearch.oFields = (prop && [prop]) || ["update_time"];
      // this.formSearch.oTypes = (order && [dic[order]]) || ["desc"];
      this.getList();
    },
  },

  components: {
    AddCustomerBrand,

    Pagination,
    TableView,
    MyDate,
  },
  computed: {
    ...mapGetters(["buttonPermissions", "customer_stage", "ws", "userInfo"]),
  },
};
