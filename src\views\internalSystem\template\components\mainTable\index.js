
import {tableValid} from "@/common/trade_erp/vxeTable.js";
// 这边的表格ref引用默认是 xTable，需要方法就往里加就行了
export default {
  props: {
    isEdit: {
      required: true,
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
        noEdit:[],
        tableData:[],
        delLineIds:[],
        
    };
  },
  methods: {
    //获取表格数据
    GetTableFullData() {
      return this.$refs.xTable.getTableData().fullData;
    },
    //设置表格数据
    SetTableData(data = []) {
      this.tableData = data;
      this.delLineIds = [];
    },
    //重置表格数据
    Reset() {
      this.tableData = [];
      this.delLineIds = [];
      this.$refs.xTable.clearAll().then(() => this.insertEvent(-1));
    },
    //如果修改状态isEdit为真说明可以修改，否则给提示，点击修改按钮修改
    activeRowMethod() {
      if (!this.isEdit) {
        return false;
      } else {
        return true;
      }
    },
    //触发不能编辑时的回调函数
    editDisabledEvent() {
      this.$message({
        type: "info",
        message: "现在处于查看单据状态，如需修改请点击修改按钮!"
      });
    },
    cellClassName({ column }) {
        //给未需要操作的列加样式
        if (this.noEdit.includes(column.property)) {
          return "col-noedit";
        }
      },
    //获取列的序号
    indexMethod({ rowIndex, row }) {
      row.num = rowIndex + 1;
      return rowIndex + 1;
    },
    //表格新增行
    addRow() {
      this.$refs.xTable.insertAt(this.records, -1).then(() => {
        this.tableData = this.$refs.xTable.getTableData().fullData;
      });
    },
    //当前行插入
    insertRow() {
        let selectRecords = this.$refs.xTable.getSelectRecords(); //getSelectRecords():获取勾选那一行的数据
        if (selectRecords.length == 0 || selectRecords.length == null) {
          return this.$message("请选择一行数据");
        } else if (selectRecords.length > 1) {
          return this.$message("请只选择一行数据");
        }
        let record = this.records;  //这里的records是默认赋值的字段
        let rowId = this.$refs.xTable.getRowById(selectRecords[0]._XID);
        this.$refs.xTable.insertAt(record, rowId);
        this.tableData = this.$refs.xTable.getTableData().fullData;
      },
    //删除行
    deleteRow() {
      let selectRecords = this.$refs.xTable.getSelectRecords();
      if (selectRecords.length) {
        selectRecords.forEach(el => {
          if (el.id) this.delLineIds.push(el.id);
        });
        this.$refs.xTable.removeSelecteds();
        this.tableData = this.$refs.xTable.getTableData().fullData;
        this.sumMoney();
      } else {
        this.$message("请至少选择一条数据！");
      }
    },
    TableFullValid() {
      //表格效验
      let data = this.$refs.xTable.getTableData().fullData;
      if (data.length == 0) {
        this.$message("表格没有添加数据");
        return false;
      }
      if (!this.isEdit) {
        this.$message("未修改单据");
        return false;
      }
      tableValid(this.$refs.xTable).then(()=>{
        this.$emit("saveData");
      })
    }
  }
};
