import API from "@/api/internalSystem/customerManage/customerPushLog/index.js";
import ContractList from "@/views/internalSystem/backstage/components/contractList/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import MyDate2 from "@/views/internalSystem/backstage/components/myDate/index2.vue";
import CustomerList from "@/views/internalSystem/backstage/components/customerList/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import { concat } from "lodash";
import { mapGetters } from "vuex";
export default {
  name: "addCustomerPush",
  components: {
    ContractList,
    MyDate,
    MyDate2,
    TableView,
    CustomerList,
  },
  props: {
    templateOption: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {},
      loading: false,
      tableData: [],
      tableList: [
        {
          name: "客户编码",
          value: "customer_no",
        },
        {
          name: "客户名称",
          value: "customer_name",
        },
        {
          name: "销售员姓名",
          value: "employee_name",
        },
        {
          name: "提交时间",
          value: "visit_time",
        },
        {
          name: "电话",
          value: "phone",
        },
        {
          name: "是否知道客户姓名",
          value: "know_send_user_id_label",
        },
        {
          name: "客服是否主动回访过",
          value: "solve_problems_in_time_label",
        },
        {
          name: "及时解决问题打分",
          value: "solve_problems_in_time_level",
        },
        {
          name: "客服技能打分",
          value: "service_skill_level",
        },
        {
          name: "客服服务效率打分",
          value: "service_efficiency_level",
        },

      ],
      proObj: {},
      isDel: true,
      rules: {
        fk_template_id: [
          {
            required: true,
            message: "请选择推送模板",
            trigger: "change",
          },
        ],
        plan_send_time: [
          {
            required: true,
            message: "请选择推送模板",
            trigger: "change",
          },
        ],
      },
    };
  },
  mounted: {},
  methods: {
    chooseCustomer() {
      this.$refs.customerAllList.Show();
    },
    //客户列表选择
    // async getCustomerInfo(info = []) {
    //   this.tableData = concat(this.tableData, info);
    //   let setList = [];
    //   let tmpList = [];
    //   if (this.tableData && this.tableData.length > 0) {
    //     this.tableData.map((item) => {
    //       if (!setList.includes(item["customer_id"])) {
    //         setList.push(item["customer_id"]);
    //         tmpList.push(item);
    //       }
    //     });
    //     this.tableData = tmpList;
    //   }
    // },
    /**
     *
     */
    async Show(data = null) {
      this.dialogVisible = true;
      this.isDel = true;
      if (data) {
        this.ruleForm = data;
        let res = await API.customerPushVisitLog({
          fk_customer_push_log_id: data.customer_push_log_id,
        });
        if (res.data && res.data.length > 0) {
          this.tableData = res.data;
          this.tableData.map(item=>{
            item['solve_problems_in_time_label'] = item['solve_problems_in_time_flag'] === 1?'是':'否'
            item['know_send_user_id_label'] = item['know_send_user_id_flag'] === 1?'是':'否'
          })
        }

        this.isDel = false;
      }
    },
    //提交
    // submitForm(formName) {
    //   this.$refs[formName].validate((valid) => {
    //     if (valid) {
    //       this.save();
    //     } else {
    //       return false;
    //     }
    //   });
    // },
    dialogCancel(flag = true) {
      //保存之后，不退出当前页面
      if (flag) {
        this.dialogVisible = false;
        this.$emit("selectData");
      }
      this.resetForm("ruleForm");
      this.clearData();
    },
    // async save() {
    //   let params = this.ruleForm;

    //   if (!this.tableData || this.tableData.length === 0) {
    //     return this.error("请至少选择一个推送客户");
    //   }

    //   if (
    //     new Date(params.plan_send_time).getTime() - new Date().getTime() <
    //     1000 * 60 * 10
    //   ) {
    //     return this.error("计划推送时间至少要大于当前5分钟");
    //   }
    //   console.log(
    //     this.templateOption.filter(
    //       (item) => item.value === params.fk_template_id
    //     )[0]["code"]
    //   );
    //   params.fk_template_code = this.templateOption.filter(
    //     (item) => item.value === params.fk_template_id
    //   )[0]["code"];

    //   // this.loading = true;
    //   let userList = [];
    //   this.tableData.map((item) => {
    //     userList.push({
    //       user_id: item["customer_id"],
    //       phone: item["telephone"],
    //     });
    //   });
    //   params.detailList = userList;
    //   params.customer_number = userList.length;
    //   params.send_type = 3;
    //   API.save(params)
    //     .then((res) => {
    //       this.dialogCancel(false);
    //     })
    //     .catch(() => {})
    //     .finally(() => {
    //       this.loading = false;
    //     });
    // },

    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {};
      this.tableData = [];

    },
    del(item) {
      let index = 0;
      for (let i = 0; i < this.tableData.length; i++) {
        if (item["customer_id"] === this.tableData[i]["customer_id"]) {
          index = i;
        }
      }
      this.tableData = this.tableData.splice(index, 0);
    },
  },
  computed: {
    ...mapGetters(["buttonPermissions", "userInfo"]),
  },
};
