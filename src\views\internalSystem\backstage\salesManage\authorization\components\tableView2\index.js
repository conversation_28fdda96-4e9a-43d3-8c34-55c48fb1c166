import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import brandApi from "@/api/internalSystem/basicManage/brand";
import { mapGetters } from "vuex";
import { sortTable } from "@/common/global/vxeTableDrag.js";
import { dateFormat } from "@/common/internalSystem/common.js";

import { checkMaintenanceFee } from "@/utils/calculate.js";
export default {
  name: "newTableView",
  components: {
    MyDate,
  },
  props: {
    tableId: {
      default: "tableCustomBrand",
    },
    ruleForm: {
      type: Object,
      default() {
        return null;
      },
    },
    tableData: {
      default() {
        return [];
      },
      type: Array,
    },
    tableList: {
      default() {
        return [];
      },
      type: Array,
    },
    proList: {
      default() {
        return [];
      },
      type: Array,
    },
    yearsFeeList: {
      default() {
        return [];
      },
      type: Array,
    },
    isEdit: {
      default: "",
      type: String,
    },
    isDel: {
      default: false,
      type: Boolean,
    },
    isSel: {
      default: false,
      type: Boolean,
    },
    tableHeight: {
      type: Number,
    },
    isThrid: {
      default: "",
      type: String,
    },
    thridTitle: {
      type: String,
    },
    isFour: {
      default: "",
      type: String,
    },
    fourTitle: {
      type: String,
    },
    handleWidth: {
      type: Number,
      default: 120,
    },
    isOperation: {
      //是否显示操作
      type: Boolean,
      default: true,
    },
    //是否双击
    isDblclick: {
      type: Boolean,
      default: false,
    },
    isOrder: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      sortTable: "",
      editRules: {},
      headerMenus: [
        [
          {
            code: "fixedLeft",
            name: "固定左边",
            visible: true,
            disabled: false,
          },
          {
            code: "fixedRight",
            name: "固定右边",
            visible: true,
            disabled: false,
          },
          {
            code: "unfixed",
            name: "取消固定",
            visible: true,
            disabled: false,
          },
        ],
      ],
      isFixed: {},
    };
  },
  mounted() {
    this.columnDrop();
    let internalSystemFixed = JSON.parse(
      localStorage.getItem("internalSystemFixed")
    );
    if (internalSystemFixed && internalSystemFixed[this.$route.name])
      this.isFixed = internalSystemFixed[this.$route.name];
    this.$nextTick(() => {
      this.$refs.xTable.refreshColumn();
    });
  },
  beforeDestroy() {
    if (this.sortable) {
      this.sortable.destroy();
    }
  },
  methods: {
    getData() {
      this.$refs.xTable.clearActived();
      return this.$refs.xTable.getTableData().fullData;
    },
    checkExist() {
      let f = this.existence(
        this.buttonPermissions,
        "ADD_CUSTOMER_BRAND_INFO_NEW"
      );
      return f;
    },
    columnDrop() {
      this.$nextTick(() => {
        let xTable = this.$refs.xTable;
        this.sortable = sortTable(xTable);
      });
    },

    what() {
      this.$forceUpdate();
    },

    //回填
    add2(obj) {
      this.tableData.push(JSON.parse(JSON.stringify(obj)));
    },
    //新增
    add() {
      this.tableData.push(JSON.parse(JSON.stringify(this.obj)));
    },
    //删除
    del(row, index) {
      let tabIndex = null;
      for (let index = 0; index < this.tableData.length; index++) {
        if (this.tableData[index]["_XID"] === row["_XID"]) {
          tabIndex = index;
        }
      }

      this.tableData.splice(tabIndex, 1);
    },

    // brandChangeEvent({ row }) {
    //   this.updateRoleList();
    // },
    keyDownMethods(data) {
      const keyCodes = ["Tab", "Enter", "NumpadEnter"];
      if (keyCodes.includes(data.$event.code)) {
        this.$nextTick(() => {
          setTimeout(() => {
            if (keyCodes.includes(data.$event.code)) {
              let cellInfo = this.$refs.xTable.getSelectedCell();

              if (!cellInfo || !cellInfo.row || !cellInfo.column) {
                return;
              }
              let { row, column } = cellInfo;
              this.$refs.xTable.setActiveCell(row, column.property);
            }
          }, 100);
        });
      }
    },
    contextMenuClickEvent({ menu, column }) {
      let value = null;
      switch (menu.code) {
        case "fixedLeft":
          // 示例
          value = "left";
          break;
        case "fixedRight":
          // 示例
          value = "right";
          break;
        case "unfixed":
          // 示例
          value = null;
          break;
      }
      this.$set(this.isFixed, column.property, value);
      let internalSystemFixed =
        JSON.parse(localStorage.getItem("internalSystemFixed")) || {};
      internalSystemFixed[this.$route.name] = this.isFixed;
      localStorage.setItem(
        "internalSystemFixed",
        JSON.stringify(internalSystemFixed)
      );
      this.$nextTick(() => {
        this.$refs.xTable.refreshColumn();
      });
    },
    // getSelectShowData(arr, value) {
    //   if (!Array.isArray(arr) || !arr.length) return value;
    //   let name = "value";
    //   if (arr[0].value === undefined) {
    //     name = "id";
    //   }
    //   let selectObj = arr.find((item) => item[name] === value);
    //   if (!selectObj) return value;
    //   return selectObj.label;
    // },
    // getSelectShowData2(arr, value) {
    //   if (!Array.isArray(arr) || !arr.length) return value;
    //   let name = 'value'
    //   if (arr[0].value === undefined) {
    //     name = 'id'
    //   }
    //   let selectObj = arr.find(item => item[name] === value);
    //   if (!selectObj) return value;
    //   return selectObj.label
    // },

    // async changeSelect(arr, value, row) {
    //   let brandData = null;
    //   // 01=产品信息  02=没有父产品  03=有父产品
    //   if (value) {
    //     brandData = await brandApi.getInfo({
    //       brand_id: value,
    //       customer_id: this.ruleForm ? this.ruleForm.customer_id : "",
    //     });
    //     let brandFlag = brandData.data["flag"];

    //     let brandObj = brandData.data;
    //     if (brandFlag === "01") {
    //       row.contract_amount_new = brandObj.brand_sell_price;
    //     } else if (["02"].includes(brandFlag)) {
    //       row.maintain_start_time_new = dateFormat(
    //         "yyyy-MM-dd",
    //         new Date(brandObj.maintain_start_time)
    //       );
    //       row.new_maintain_stop_time_new = dateFormat(
    //         "yyyy-MM-dd",
    //         new Date(brandObj.new_maintain_stop_time)
    //       );
    //       row.contract_amount_new =
    //         brandObj.contract_amount || brandObj.brand_sell_price;
    //     } else if (["03"].includes(brandFlag)) {
    //       row.maintain_start_time_new = dateFormat(
    //         "yyyy-MM-dd",
    //         new Date(brandObj.maintain_start_time)
    //       );
    //       row.new_maintain_stop_time_new = dateFormat(
    //         "yyyy-MM-dd",
    //         new Date(brandObj.new_maintain_stop_time)
    //       );
    //       row.contract_amount_new =
    //         brandObj.contract_amount || brandObj.brand_sell_price;
    //     }

    //     row.maintenance_fee_new = this.checkMaintenanceFee(row);
    //   }
    // },
    /**
     * 计算年维护费
     */
    // checkMaintenanceFee(row) {
    //   return checkMaintenanceFee(row);
    // },
    // changeAmount(row) {
    //   row.maintenance_fee_new = this.maintenanceFee(row);
    // },
    // changeYearMaintailCost(value, row) {
    //   row.maintenance_fee_new = this.maintenanceFee(row);
    // },

    selectValue(obj) {
      obj.currentTarget.select();
    },
    // maintenanceFee(row) {
    //   let money = 0;
    //   if (
    //     Number(row.contract_amount_new) &&
    //     Number(row.year_maintain_cost_new)
    //   ) {
    //     money = (
    //       (Number(row.contract_amount_new) *
    //         Number(row.year_maintain_cost_new)) /
    //       100
    //     ).toFixed(2);
    //   }
    //   return money;
    // },
  },
  computed: {
    ...mapGetters(["buttonPermissions"]),
  },
};
