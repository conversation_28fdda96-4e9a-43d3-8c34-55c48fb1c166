<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small">
      <el-form-item label="查询条件">
        <el-input v-model="formSearch.tbName" placeholder="请输入表名"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="formSearch.tbCode" placeholder="请输入字段代码"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="formSearch.sysName" placeholder="请输入字段名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
        <el-button type="primary" @click="add"
          v-permit="'ADD_DATA_DICTIONARY_NEW'">添加</el-button>
      </el-form-item>
    </el-form>
    <!-- 新增修改数据字典信息 -->
    <AddDataDictionary ref="AddDataDictionary" @selectData="getList" />
    <table-view :tableList="tableList" :tableData="tableData"
      :isEdit="'UPDATE_DATA_DICTIONARY_NEW'"
      :isDel="'DEL_DATA_DICTIONARY_NEW'"
      @getSelectRecords="getSelectRecords" @modify="modify" @del="del"></table-view>
    <Pagination ref="pagination" @success="getList" />
  </div>
</template>

<script src="./index.js"></script>