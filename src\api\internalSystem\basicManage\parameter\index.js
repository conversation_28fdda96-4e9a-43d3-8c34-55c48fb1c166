import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: params => {
    return Axios.post(`${environment.internalSystemAPI}parameter/query`, params)
  },
  // 新增
  add: params => {
    return Axios.post(`${environment.internalSystemAPI}parameter/add`, params)
  },
  // 删除
  remove: params => {
    return Axios.post(`${environment.internalSystemAPI}parameter/remove`, params)
  },
  // 编辑
  update: params => {
    return Axios.post(`${environment.internalSystemAPI}parameter/update`, params)
  },
  // 获取单条信息
  getInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}parameter/getInfo`, params)
  }
};