<template>
  <div class="treeContainer">
    <div class="head">
      <el-button type="text" size="large" @click="addDirectory()"
        >添加目录</el-button
      >
      <el-input
        placeholder="输入关键字进行过滤"
        v-model="filterText"
        style="margin-top: 10px"
      />
    </div>
    <div class="main-tree">
      <el-tree
        ref="tree"
        class="filter-tree"
        :data="directoryTreeList"
        node-key="directory_id"
        highlight-current
        :expand-on-click-node="false"
        :show-checkbox="false"
        :default-checked-keys="checkedKey"
        :props="defaultProps"
        default-expand-all
        check-strictly
        :filter-node-method="filterNode"
        @check-change="clickDeal"
        @node-click="cilckDirectoryNode"
      >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <span>{{ node.label }}</span>
          <span>
            <el-button
              type="text"
              class="success"
              size="mini"
              @click="addDirectory(data)"
            >
              添加子目录
            </el-button>
            <el-button
              type="text"
              class="primary"
              size="mini"
              @click="changeEnableFlag(data)"
            >
              {{ data.enable_flag === 1 ? "禁用" : "启用" }}
            </el-button>
            <el-button
              type="text"
              class="primary"
              size="mini"
              @click="editDirectory(data)"
            >
              编辑
            </el-button>
            <el-button
              type="text"
              class="danger"
              style="color: red"
              size="mini"
              @click="delDirectory(data)"
            >
              删除
            </el-button>
          </span>
        </span>
      </el-tree>
    </div>

    <AddDirectory
      ref="AddDirectory"
      :directory_id="directory_id"
      :parentId="parentId"
      :parentName="parentName"
      @getList="getList"
    />
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
@import "../index.scss";
</style>