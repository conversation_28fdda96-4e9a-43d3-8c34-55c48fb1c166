<template>
  <div class="body-p10" v-if="dialogVisible">

    <el-form
      v-if="!addDialogVisible"
      :model="ruleForm"
      ref="ruleForm"
      label-width="100px"
      class="mt10"
    >
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input disabled v-model="ruleForm.customer_name" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户阶段" prop="customer_stage">
            <el-select
              disabled
              v-model="ruleForm.customer_stage"
              placeholder="请选择客户阶段"
              filterable
              clearable
              style="width:100%"
            >
            {{customerStageList}}
              <el-option
                v-for="item in customerStageList"
                :key="item.sysValue"
                :label="item.sysName"
                :value="item.sysValue"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12" class="formItem2">
          <el-form-item label="联系人" prop="link_man">
            <el-input disabled v-model="ruleForm.link_man" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12" class="formItem2">
          <el-form-item label="手机" prop="telephone">
            <el-input disabled v-model="ruleForm.telephone"  clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12" class="formItem2">
          <el-form-item label="销售员姓名" prop="fk_sale_employee_id_name">
            <el-input disabled v-model="ruleForm.fk_sale_employee_id_name" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12" class="formItem2">
          <el-form-item label="主维护员" prop="fk_maintain_employee_id_str">
            <el-input disabled v-model="ruleForm.fk_maintain_employee_id_str" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12" class="formItem2">
          <el-form-item label="成交日期" prop="deal_time">
            <el-date-picker
              style="width:100%"
              v-model="ruleForm.deal_time"
              align="right"
              type="date"
              disabled
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12" class="formItem2">
          <el-form-item label="到期日期" prop="maintain_stop_time">
            <el-date-picker
              style="width:100%"
              v-model="ruleForm.maintain_stop_time"
              align="right"
              type="date"
              disabled
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="!addDialogVisible">
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="add">新增</el-button>
    </div>
    <table-view
      v-if="!addDialogVisible"
      class="mt20"
      :tableHeight="500"
      :tableList="tableList"
      :tableData="tableData"
      :isEdit="'permanent_button'"
      :isDel="'permanent_button'"
      @modify="modify"
      @del="del"
    ></table-view>
    <add-tracking ref="addTracking" @selectData="getList" />
  </div>
</template>
<script src="./index.js">
</script>
<style lang="scss" scoped>
@import "@/assets/css/element/font-color.scss";
</style>