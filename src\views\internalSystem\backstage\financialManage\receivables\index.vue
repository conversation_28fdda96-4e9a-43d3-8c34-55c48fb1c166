<template>
  <div style="overflow-y: auto; overflow-x: hidden" class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small" v-if="!isAdd">
      <el-form-item label="查询条件">
        <el-input
          v-model="formSearch.contract_no"
          placeholder="请输入合同编号"
          clearable
          style="width: 160px"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="formSearch.customer_name"
          placeholder="请输入客户名称"
          clearable
          style="width: 160px"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.sales_unit_id"
          placeholder="请选择销货单位"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in salesUnits"
            :key="item.sales_unit_id"
            :label="item.company_name"
            :value="item.sales_unit_id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.bear_employee_id"
          placeholder="请选择技术员"
          class="inputBox"
          filterable
          clearable
          style="width: 160px"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number + '-' + item.employee_name"
            :value="item.employeeId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.fk_sell_employee_id"
          placeholder="请选择销售员"
          class="inputBox"
          filterable
          clearable
          style="width: 160px"
          :disabled="!['总经理','财务经理','财务专员'].includes(userInfo.role_name) === true"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number + '-' + item.employee_name"
            :value="item.employeeId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <!-- {{receivables_project}} -->
        <el-select
          v-model="formSearch.project"
          placeholder="请选择收款项目"
          class="inputBox"
          filterable
          clearable
          style="width: 160px"
        >
          <el-option
            v-for="item in receivables_project"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.startTime"
          hint="请选择开始时间"
          style="width: 160px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.endTime"
          hint="请选择结束时间"
          style="width: 160px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.confirmStartTime"
          hint="请选择到款时间"
          style="width: 160px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.confirmEndTime"
          hint="请选择到款时间"
          style="width: 160px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
        <el-button type="primary" @click="add" v-permit="'ADD_RECEIVABLES_NEW'"
          >制单</el-button
        >
        <!-- <el-button
          type="primary"
          @click="exportReceivables"
          :loading="exLoading"
          v-permit="'EXPORT_RECEIVABLES_NEW'"
          >导出
        </el-button> -->
        <el-button
          type="primary"
          @click="handleExcel"
          :loading="exLoading"
          v-permit="'EXPORT_RECEIVABLES_NEW'"
          >导出
        </el-button>
      </el-form-item>
    </el-form>
    <table-view
      :tableList="tableList"
      :tableData="tableData"
      v-if="!isAdd"
      isEdit="permanent_button"
      @modify="modify"
      :isDel="'DEL_RECEIVABLES_NEW'"
      @del="del"
    >
    </table-view>
    <div class="mt10 moneyTitle" v-if="!isAdd">
      <el-row :gutter="20">
        <el-col :span="12"> 总收款金额：{{ amount }}元 </el-col>
      </el-row>
    </div>
    <Pagination ref="pagination" @success="getList" v-show="!isAdd" />
    <!-- 新增收款单 -->
    <AddReceivables ref="addReceivables" @selectData="getList" />
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
.moneyTitle {
  font-size: 14px;
  font-weight: bold;
}

@import "@/assets/css/element/font-color.scss";
</style>