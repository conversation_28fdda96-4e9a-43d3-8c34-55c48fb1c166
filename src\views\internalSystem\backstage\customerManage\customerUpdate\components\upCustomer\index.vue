<template>
  <div>
    <div>
      <el-button @click="dialogCancel(true)">返 回</el-button>
      <el-button
        type="primary"
        @click="submitForm('ruleForm')"
        :loading="loading"
        :disabled="isDisabled"
        >保 存</el-button
      >
    </div>
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="mt10"
      label-position="left"
      style="margin-left: 20px; margin-right: 30px"
      :disabled="isDisabled"
    >
      <el-row :gutter="40">
        <el-col :span="8">
          <el-form-item label="客户编号" prop="customer_no">
            <el-input
              v-model="ruleForm.customer_no"
              @focus="chooseCustomer"
              placeholder="点击选择客户"
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户阶段" prop="customer_stage">
            <el-select
              v-model="ruleForm.customer_stage"
              placeholder="请选择客户阶段"
              disabled
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in params_constant_customer_stage"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售员工号" prop="fk_sale_employee_id_number">
            <el-input
              v-model="fkSaleEmployeeUserInfo"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="formItem2">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input v-model="ruleForm.customer_name" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="法定名称" prop="customer_legal_name">
            <el-input
              v-model="ruleForm.customer_legal_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户法人" prop="customer_legal_person">
            <el-input
              v-model="ruleForm.customer_legal_person"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="联系人" prop="link_man">
            <el-input v-model="ruleForm.link_man" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="ruleForm.email" clearable></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="客户别名" prop="customer_name_alias">
            <el-input v-model="ruleForm.customer_name_alias" clearable></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户简介" prop="customer_synopsis">
            <el-input v-model="ruleForm.customer_synopsis" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="联系人手机" prop="telephone">
            <el-input v-model="ruleForm.telephone" clearable></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="formItem2">
          <el-form-item label="QQ号码" prop="qq">
            <el-input  v-model="ruleForm.qq" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户来源" prop="customer_source">
            <el-select
              v-model="ruleForm.customer_source"
              placeholder="请选择客户来源"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in params_constant_customer_source"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="介绍人/公司" prop="introducerName">
            <el-input v-model="ruleForm.introducerName" placeholder="点击选择介绍人/公司" @focus="chooseIntroducer" readonly clearable></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="详细地址" prop="link_address">
            <el-input v-model="ruleForm.link_address" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="备案日期">
            <el-date-picker
              style="width: 100%"
              v-model="ruleForm.update_time"
              align="right"
              type="date"
              disabled
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="8" class="formItem2">
          <el-form-item label="成交日期">
            <el-date-picker
              style="width: 100%"
              v-model="ruleForm.deal_time"
              align="right"
              type="date"
              disabled
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-checkbox v-model="checked">是否新增客户财务资料</el-checkbox>
    </el-form>
    <el-form
      :model="financeForm"
      :rules="financeRules"
      ref="financeForm"
      label-width="100px"
      class="mt10"
      label-position="left"
      style="margin-left: 20px; margin-right: 30px"
      :disabled="isDisabled"
    >
      <div v-if="checked">
        <div class="add_customer_finance">客户财务资料</div>
        <el-row :gutter="40">
          <el-col :span="8">
            <el-form-item label="客户税号" prop="customer_tax_number">
              <el-input
                v-model="financeForm.customer_tax_number"
                clearable
                onkeyup="this.value=this.value.replace(/[, ]/g,'')" 
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户行" prop="opening_bank">
              <el-input v-model="financeForm.opening_bank" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收票人" prop="receive_ticket_person">
              <el-input
                v-model="financeForm.receive_ticket_person"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="formItem2">
            <el-form-item label="开票地址" prop="open_ticket_address">
              <el-input
                v-model="financeForm.open_ticket_address"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" class="formItem2">
            <el-form-item label="银行账号" prop="customer_account">
              <el-input
                v-model="financeForm.customer_account"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="formItem2">
            <el-form-item label="收票电话" prop="receive_ticket_phone">
              <el-input
                v-model="financeForm.receive_ticket_phone"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="formItem2">
            <el-form-item label="开票电话" prop="open_ticket_phone">
              <el-input
                v-model="financeForm.open_ticket_phone"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" class="formItem2">
            <el-form-item label="收票地址" prop="receive_ticket_address">
              <el-input
                v-model="financeForm.receive_ticket_address"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <customer-list
      ref="dealCustomerList"
      dialogTitle="成交客户列表"
      :customerStage="2"
      @getInfo="getInfo"
    />
    <customer-list
      ref="customerList"
      dialogTitle="客户列表"
      :isJudge="true"
      @getInfo="getCustomerInfo"
    />
  </div>
</template>
<script src="./index.js">
</script>

<style lang="scss" scoped>
.toLabel {
  width: 8%;
  color: black;
  cursor: default;
}

.add_customer_finance {
  text-align: center;
  margin-top: 10px;
  margin-bottom: 10px;
  background: #70676733;
}
@import "@/assets/css/element/font-color.scss";
</style>