<template>
  <el-pagination
    class="mt10 heightAuto"
    background
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
    :current-page.sync="currentPage"
    :page-sizes="pageSizes"
    :page-size="pageSize"
    layout="total, sizes, prev, pager, next, jumper"
    :total="total">
  </el-pagination>
</template>

<script>
export default {
  name: 'Pagination',
  props: {
  },
  data () {
    return {
      currentPage: 1,
      initPage: 1,
      pageSizes: [10, 20, 50, 100, 500],
      pageSize: 50,
      total: 0
    }
  },
  methods: {
    obtain () {
      return {
        pageNum:  this.currentPage,
        pageSize: this.pageSize
      }
    },
    setTotal (val) {
      this.total = val
    },
    handleSizeChange (val) {
      this.currentPage = 1
      this.pageSize = val
      this.$emit('success')
    },
    handleCurrentChange (val) {
      this.currentPage = val
      this.$emit('success')
    }
  }
}
</script>

<style scoped lang="stylus" ref="stylesheet/stylus">

</style>
