import API from '@/api/internalSystem/basicInfo/department/departmentApi.js';
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue';
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import {
  mapGetters
} from "vuex";
export default {
  name: 'department',
  components: {
    Pagination,
    TableView
  },
  data() {
    return {
      formSearch: {
        name: ''
      },
      tableData: [],
      dialogAdd: false, // 弹窗
      dialogTitle: {
        add: '新增',
        edit: '修改'
      },
      titleMap: '',
      form: {},
      tableList: [{
          name: "部门名称",
          value: "departmentName"
        },
        {
          name: "创建时间",
          value: "addTime"
        }
      ]
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 获取列表
    getList(f = false) {
      let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
      if (f)
        param.pageNum = 1;
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.pagination.setTotal(res.totalCount);
      })
    },
    // 新增和编辑确认
    dialogAddPost() {
      if (this.form.id) { // 修改
        API.update(this.form).then(() => {
          this.getList()
          this.dialogAdd = false
        })
      } else { // 新增
        API.add(this.form).then(() => {
          this.getList()
          this.dialogAdd = false
        })
      }
    },
    // 打开新增
    add() {
      this.titleMap = 'add'
      this.dialogAdd = true
    },
    // 打开编辑
    modify(row) {
      this.titleMap = 'edit'
      this.form = {
        id: row.departmentId,
        departmentName: row.departmentName
      }
      this.dialogAdd = true
    },
    // 删除部门
    del(item) {
      API.remove({
          id: item.departmentId
        })
        .then(() => {
          this.getList();
        })
    },
    // 关闭列表弹窗
    dialogAddClose() {
      this.form = {}
    }
  },
  computed: {
    ...mapGetters(["buttonPermissions"])
  }
}