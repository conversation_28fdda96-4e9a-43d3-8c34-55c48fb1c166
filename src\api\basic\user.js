import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  //用户登录验证
  login: params => Axios.post(`${environment.basicAPI}user/login`, params),
  // token免登
  freeLogin: params =>
    // Axios.post(`${environment.tradeAPI}user/checkNoLogin`, params),
    Axios.post(`${environment.basicAPI}user/tokenUserInfo`, params),
// 通用云平台获取用户数据的接口
getUserInfoByToken: () => {
  return Axios.post(`${environment.basicAPI}user/cloudRefreshUserInfo`);
},
  //注册提交
  userResgister: params =>
    Axios.post(`${environment.basicAPI}user/register`, params),
  //获取短信验证码
  getValidateCode: params =>
    Axios.post(`${environment.basicAPI}user/getValidateCode`, params)
};
