import API from '@/api/internalSystem/customerManage/customerInfo'
import auditAPI from '@/api/internalSystem/common/auditInfo.js'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import UpCustomer from "./components/upCustomer/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import AuditDetail from '@/mixins/auditDetail.js'
import {
  getOptions,
} from "@/common/internalSystem/common.js"
import {
  mapGetters
} from "vuex";
export default {
  name: "customerUpdate",
  mixins: [AuditDetail],
  data() {
    return {
      title: "客户修改单",
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        customer_name: "",
        audit_state: ""
      },
      tableList: [{
          name: "审核状态",
          value: "audit_state_name",
          width: 100
        },
        {
          name: "客户编码",
          value: "customer_no",
          width: 100
        },
        {
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "客户法人",
          value: "customer_legal_person",
          width: 100
        },
        {
          name: "客户类型",
          value: "customer_type_format",
          width: 100
        },
        {
          name: "客户阶段",
          value: "customer_stage_format",
          width: 100
        },
        {
          name: "联系人",
          value: "link_man",
          width: 100
        },
        {
          name: "电话",
          value: "phone",
          width: 110
        },
        {
          name: "手机",
          value: "telephone",
          width: 110
        },
        {
          name: "销售员",
          value: "fk_sale_employee_name",
          width: 100
        },
        {
          name: "制单日期",
          value: "update_time",
          width: 110
        }
      ],
      isAdd: false,
      customerTempId: 0
    };
  },

  mounted() {
    this.getList();
  },
  methods: {
    getList(f = false) {
      this.isAdd = false;
      this.isAudit = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
          if (f) param.pageNum = 1;
          param.isJurisdiction = this.permissionToCheck("ALL_CUSTOMER_UPDATE_NEW") ? 1 : 0;
          API.customerTempList(param).then(res => {
            this.tableData = res.data;
            this.$refs.pagination.setTotal(res.totalCount);
          }).finally(() => {
            this.loading = false;
          });
        });
      // }, 300);
      this.customerTempId = null
    },
    add() {
      this.isAdd = true;
    },
    modify(item) {

      this.getInfo(item.customer_temp_id);
    },
    getInfo(customer_temp_id) {
      this.isAdd = true;
      this.customerTempId = customer_temp_id
    },
    del(item) {
      if (item.audit_state == 1)
        return this.error("该单据已审核，不允许删除");
      let params = {
        customer_temp_id: item.customer_temp_id
      };
      API.delCustomerTemp(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
  },

  components: {
    UpCustomer,
    Pagination,
    TableView
  },
  computed: {
    ...mapGetters(["customer_temp_audit"])
  }
};