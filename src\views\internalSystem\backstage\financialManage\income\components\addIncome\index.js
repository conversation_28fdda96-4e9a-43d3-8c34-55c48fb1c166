import API from '@/api/internalSystem/financialManage/income'
import bankAPI from '@/api/internalSystem/financialManage/bankAccout'
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  getOptions,
} from "@/common/internalSystem/common.js"
import {
  mapGetters
} from "vuex";
export default {
  name: "addIncome",
  components: {
    MyDate
  },
  data() {
    return {
      dialogTitle: "新增其他收入单",
      dialogVisible: false,
      ruleForm: {
        income_type: "",
        content: "",
        amount: "",
        income_time: "",
        fk_bank_account_id: ""
      },
      rules: {
        income_type: [{
          required: true,
          message: "请选择收入类型",
          trigger: "change"
        }],
        content: [{
          required: true,
          message: "请输入收入明细",
          trigger: "blur"
        }],
        amount: [{
          required: true,
          message: "请输入收入金额",
          trigger: "blur"
        }],
        income_time: [{
          required: true,
          message: "请选择收入时间",
          trigger: "blur"
        }],
        fk_bank_account_id: [{
          required: true,
          message: "请选择收入银行",
          trigger: "change"
        }]
      },
      incomeTypeList: [],
      bankList: []
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      this.incomeTypeList = getOptions('t_income', 'income_type');
      bankAPI.query()
        .then(data => {
          this.bankList = data.data;
        })
        .catch(() => {});
      if (!data) {
        this.dialogTitle = "新增其他收入单";
      } else {

        this.dialogTitle = "修改其他收入单";
        this.ruleForm = data;
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    save() {
      let params = this.ruleForm;
      if (params.income_id) {
        if (params.audit_state == 1)
          return this.error("该记录已审核通过，不允许修改")
        API.update(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      } else {
        API.add(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      }
    },
    back() {
      this.$confirm('此操作将回退该条记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          income_id: this.ruleForm.income_id,
          auditState:3
        }
        API.updateAudit(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {}).finally(() => {
            this.loading = false;
          });
      }).catch(() => {})
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        income_type: "",
        content: "",
        amount: "",
        income_time: "",
        fk_bank_account_id: ""
      }
    }
  },
  computed: {
    ...mapGetters(["buttonPermissions","income_type"])
  }
};