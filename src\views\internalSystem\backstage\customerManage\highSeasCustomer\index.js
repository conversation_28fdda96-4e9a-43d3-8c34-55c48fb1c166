import API from '@/api/internalSystem/customerManage/customerInfo'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import {
  getOptions,
} from "@/common/internalSystem/common.js"
export default {
  name: "highSeasCustomer",
  data() {
    return {
      title: "公海客户",
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        customerName: "",
        customerStage: "",
        fk_sale_employee_id: ""
      },
      tableList: [{
          name: "客户编码",
          value: "customer_no",
          width: 80
        },
        {
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "主维护员",
          value: "fk_maintain_employee_id_str",
          width: 70
        },
        {
          name: "维护结束时间",
          value: "maintain_stop_time",
          width: 94
        },
        {
          name: "客户阶段",
          value: "customer_stage_format",
          width: 70
        },
        {
          name: "客户类型",
          value: "customer_type_format",
          width: 70
        },
        {
          name: "所属行业",
          value: "belong_industry_format",
          width: 80
        },
        {
          name: "客户来源",
          value: "customer_source_format",
          width: 82
        },
        {
          name: "联系人",
          value: "link_man",
          width: 60
        },
        {
          name: "手机",
          value: "telephone",
          width: 100
        },
        {
          name: "销售员工号",
          value: "fk_sale_employee_id_number",
          width: 82
        },
        {
          name: "销售员姓名",
          value: "fk_sale_employee_id_name",
          width: 82
        },
        {
          name: "销售员部门",
          value: "department_name",
          width: 82
        },
        {
          name: "所在地",
          value: "link_address"
        },
        {
          name: "备案日期",
          value: "update_time",
          width: 90
        },
        {
          name: "成交日期",
          value: "deal_time",
          width: 90
        }
      ],
      customerStageList: [],
      employeeList: [],
    };
  },

  mounted() {
    this.getList();
    this.$store.dispatch('getEmployee').then(res => {
      this.employeeList = res;
    });
  },
  methods: {
    getList(f = false) {
      this.customerStageList = getOptions('t_customer', 'customer_stage');
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
      if (f)
        param.pageNum = 1;
      API.highSeasCustomer(param).then(res => {
        this.tableData = res.data;
        this.$refs.pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    }
  },

  components: {
    Pagination,
    TableView
  }
};