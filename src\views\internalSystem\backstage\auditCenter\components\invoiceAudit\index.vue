<template>
  <div
    class="body-p10 orderH100"
    style="overflow-y: auto; overflow-x: hidden"
    v-if="dialogVisible"
  >
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="openAudit">审 核</el-button>
    </div>
    <el-form
      :model="ruleForm"
      ref="ruleForm"
      label-width="160px"
      class="mt10 flexAndFlexColumn"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单据编号" prop="invoice_no">
            <el-input
              v-model="ruleForm.invoice_no"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据日期" prop="invoice_date">
            <el-input
              v-model="ruleForm.invoice_date"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="操作员" prop="add_user_name">
            <el-input
              v-model="ruleForm.add_user_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="操作部门" prop="department_name">
            <el-input
              v-model="ruleForm.department_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="购销单位" prop="sales_unit_id_format">
            <el-input
              v-model="ruleForm.sales_unit_id_format"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据备注" prop="invoice_remark">
            <el-input disabled v-model="ruleForm.invoice_remark" clearable>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <table-custom
        ref="tableCustom"
        class="mt10 tableContent"
        tableHeight="99%"
        :obj="obj"
        :tableCol="tableCol"
        :isDel="false"
      />
    </el-form>
    <el-dialog
      title="审核"
      :visible.sync="dialogVisibleAudit"
      append-to-body
      @close="dialogCancelAudit"
      width="660px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <el-form
        :model="auditForm"
        :rules="rules"
        ref="auditForm"
        label-width="100px"
      >
        <!-- <el-form-item label="审核状态" prop="auditState">
          <el-select
            v-model="auditForm.auditState"
            placeholder="请选择审核状态"
            filterable
            clearable
          >
            <template v-for="item in contract_auditStateList">
              <el-option
                v-if="
                  item.id != ruleForm.approval_state &&
                  item.id != 3 &&
                  item.id != 4
                "
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </template>
          </el-select>
        </el-form-item> -->
        <el-form-item label="审核状态" prop="auditState">
          <el-radio-group v-model="auditForm.auditState">
            <template v-for="item in contract_auditStateList">
              <el-radio
              v-if="
                  item.id != ruleForm.approval_state &&
                  item.id != 3 &&
                  item.id != 4
                "
                :key="item.id"
                :label="item.value"
                :value="item.value"
                border
                style="height: 30px;"
                ><span style="font-size:16px;">{{ item.label }}</span></el-radio
              >
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="approval">
          <el-input
            v-model="auditForm.approval"
            type="textarea"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm('auditForm')"
          :loading="loading"
          >保 存</el-button
        >
        <el-button @click="dialogCancelAudit">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script src="./index.js">
</script>

<style lang="scss" scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown + .el-dropdown {
  margin-left: 15px;
}

@import "@/assets/css/element/font-color.scss";
</style>