<template>
  <div class="treeContainer">
    <div class="head" title="点击查找系统所有模块">
      <div  @click="allSearch">
<i class="el-icon-box" v-show="!leftIndent"></i> <span v-show="!leftIndent">{{title}}</span> 
      </div>
       
        <i class="el-icon-s-fold foldIcon" :class="[leftIndent?'el-icon-s-unfold':'el-icon-s-fold']" :title="leftIndent?'点击展开模块':'点击缩进模块'" @click.stop="indentClick"></i>
    </div>
    <div class="main-tree">
      <el-tree ref="tree" v-show="!leftIndent" class="filter-tree" :data="menuTreeList" node-key="module_id"   
        :expand-on-click-node="false" :show-checkbox="false" empty-text="请先选择对应的产品"
        :props="defaultProps" default-expand-all 
        
        @node-click="nodeClick"
        >
        <span class="custom-tree-node" slot-scope="{ node, data }">
      
          <span  :style="{fontWeight:data.module_id===currentId?'bold':'normal'}"  >{{ node.label }}</span>

        </span>
      </el-tree>
    </div>

  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
.treeContainer{
    border: 1px solid #ddd;
}
.fw{
    font-weight: bold;
    
}
.custom-tree-node{
    font-size: 13px;
    color: #03c;
}
 .head {
     cursor: pointer;
    font-weight: bold;

    line-height: 30px;
    padding: 5px 10px;
    font-size: 14px;
    background-color: #f0f0f0;
    display: flex;
    justify-content: space-between;
  }

  .foldIcon{
    float: right;
    line-height: 30px;

  }
</style>