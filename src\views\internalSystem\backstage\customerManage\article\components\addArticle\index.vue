<template>
  <div v-if="dialogVisible">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button
        :disabled="!((isEdit && ruleForm.copy_id) || !ruleForm.copy_id)"
        type="primary"
        @click="submitForm('ruleForm')"
        :loading="loading"
        >保 存</el-button
      >
      <el-button
        v-permit="'AUDIT_ARTICLE_SEND_NEW'"
        type="primary"
        @click="send"
        :loading="loading"
        :disabled="ruleForm.copy_state === 1 || !ruleForm.copy_state"
        >发 出</el-button
      >
    </div>
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="mt10"
      style="margin-right: 40px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="主题" prop="title">
            <el-input
              :disabled="!isEdit"
              v-model="ruleForm.title"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="二级关键词" prop="keywords">
            <el-input
              :disabled="!isEdit"
              v-model="ruleForm.keywords"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="类型" prop="type">
        <el-select
          :disabled="!isEdit"
          v-model="ruleForm.type"
          placeholder="请选择类型"
          filterable
          clearable
        >
          <el-option
            v-for="item in typeList"
            :key="item.sysValue"
            :label="item.sysName"
            :value="item.sysValue"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="内容" prop="copy_content">
        <rich-text :disabled="!isEdit" v-model="ruleForm.copy_content" />
      </el-form-item>
    </el-form>
  </div>
</template>
<script src="./index.js">
</script>

<style lang="scss" scoped>
@import "@/assets/css/element/font-color.scss";
</style>