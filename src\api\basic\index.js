import axios from "@/api/index.js";
import environment from "@/api/environment";

export default {
	//查询微信支付状态
	payStatus: (flowNumber = "") => {
		return axios.get(`${environment.basicAPI}wechat/weChatOrderQuery?flowNumber=${flowNumber}`);
	},
	//获取微信付款二维码
	getQrCodeBlob: (systemOrderId, id) => {
		return axios.get(
				`${environment.basicAPI}wechat/weChatOrderCode?systemOrderId=${systemOrderId}&random=${id}`, {
					responseType: "arraybuffer" //响应为二进制数组
				}
		);
	},
	//支付宝支付（2期）
	gainAliPayPage: params => {
		return axios.post(`${environment.basicAPI}aliPay/gainAliPayPage`, params);
	},
	addUserOrder: params => {
		return axios.post(`${environment.basicAPI}order/addUserOrder`, params);
	},
	
	//用户登录验证
	userlogin: params => {
		return axios.post(`${environment.basicAPI}user/login`, params);
	},
	//注册提交
	userResgister: params => {
		return axios.post(`${environment.basicAPI}user/register`, params);
	},
	//获取短信验证码
	getValidateCode: params => {
		return axios.post(`${environment.basicAPI}user/getValidateCode`, params);
	},
	//产品中心或取产品类别
	getProuductClass: () => {
		return axios.get(`${environment.basicAPI}systemType/indexSystemList`);
	},
	//获取子系统列表
	getSubsystemlist: params => {
		return axios.post(`${environment.basicAPI}subsystem/systemListBySystemType`, params);
	},
	//获取已购买子系统(2期)
	gainProductMenusAndPay: params => {
		return axios.post(`${environment.basicAPI}subsystem/gainProductMenusAndPay`, params);
	},
	//获取子系统列表(2期修改)
	payedList: params => {
		return axios.post(`${environment.basicAPI}professionalWork/payedList`, params);
	},
	//获取类别详情(订单详情)
	getSubsystemOrder: params => {
		return axios.post(`${environment.basicAPI}order/buyOrderInfo`, params);
	},
	//获取类别详情(订单详情2期)
	listProductMenusInfo: params => {
		return axios.post(`${environment.basicAPI}productMenus/listProductMenusInfo`, params);
	},
	//增值业务价格计算(2期)
	calculatePrice: params => {
		return axios.post(`${environment.basicAPI}productMenus/calculatePrice`, params);
	},
	//子系统下单
	placeOrder: params => {
		return axios.post(`${environment.basicAPI}order/add`, params);
	},
	//待支付订单
	unpaidOrder: params => {
		return axios.post(`${environment.basicAPI}order/toBePaid`, params);
	},
	//已购买子系统首页
	myIndexList: params => {
		return axios.post(`${environment.basicAPI}subsystem/indexList`, params);
	},
	//订单列表
	orderList: params => {
		return axios.post(`${environment.basicAPI}order/list`, params);
	},
	//系统类别下拉
	systemTypeList: () => {
		return axios.get(`${environment.basicAPI}systemType/pullDownDatas`);
	},
	//部门下拉选
	departmentPullDownList: params => {
		return axios.post(`${environment.basicAPI}department/departmentPullDownList`, params);
	},
	//新增部门
	addDepartment: params => {
		return axios.post(`${environment.basicAPI}department/add`, params);
	},
	//删除部门
	delDepartment: params => {
		return axios.post(`${environment.basicAPI}department/del`, params);
	},
	//部门详情
	detailDepartment: params => {
		return axios.post(`${environment.basicAPI}department/info`, params);
	},
	//部门列表
	departmentList: params => {
		return axios.post(`${environment.basicAPI}department/list`, params);
	},
	
	//新增岗位
	addRole: params => {
		return axios.post(`${environment.basicAPI}role/add`, params);
	},
	//岗位信息
	detailRole: params => {
		return axios.post(`${environment.basicAPI}role/info`, params);
	},
	//删除岗位
	delRole: params => {
		return axios.post(`${environment.basicAPI}role/del`, params);
	},
	//岗位列表
	roleList: params => {
		return axios.post(`${environment.basicAPI}role/list`, params);
	},
	//岗位方向下拉
	roleDirectionPullDown: () => {
		return axios.get(`${environment.basicAPI}role/roleDirection`);
	},
	
	//权限组下拉
	permissionPullDown: () => {
		return axios.get(`${environment.basicAPI}role/permissionGroupList`);
	},
	//部门岗位列表
	roleListByDepartment: params => {
		return axios.post(`${environment.basicAPI}role/roleListByDepartmentId`, params);
	},
	//账号管理列表
	userList: params => {
		return axios.post(`${environment.basicAPI}user/list`, params);
	},
	
	//员工新增
	addUser: params => {
		return axios.post(`${environment.basicAPI}user/saveNew`, params);
	},
	//员工修改/详情
	detailUser: params => {
		return axios.post(`${environment.basicAPI}user/userInfos`, params);
	},
	//员工删除
	delUser: params => {
		return axios.post(`${environment.basicAPI}user/del`, params);
	},
	//密码重置
	resetPassword: params => {
		return axios.post(`${environment.basicAPI}user/resetPass`, params);
	},
	//密码修改
	changePassword: params => {
		return axios.post(`${environment.basicAPI}user/updatePass`, params);
	},
	//用户授权列表数据
	userAuthorizationList: params => {
		return axios.post(`${environment.basicAPI}user/userAuthorizationList`, params);
	},
	// 系统用户授权
	systemAuthorization: params => {
		return axios.post(`${environment.basicAPI}user/accredit`, params);
	},
	// 系统公司授权
	companySystemAuthorization: params => {
		return axios.post(`${environment.basicAPI}order/accredit`, params);
	},
	//企业列表
	companyList: params => {
		return axios.post(`${environment.basicAPI}company/list`, params);
	},
	
	//企业审核
	companyAuhtor: params => {
		return axios.post(`${environment.basicAPI}company/examine`, params);
	},
	
	//企业详情
	companyDetail: params => {
		return axios.post(`${environment.basicAPI}company/info`, params);
	},
	
	//企业修改
	companyRevise: params => {
		return axios.post(`${environment.basicAPI}company/update`, params);
	},
	
	//系统列表
	subSystemList: params => {
		return axios.post(`${environment.basicAPI}subsystem/list`, params);
	},
	
	//系统新增
	addSubSystem: params => {
		return axios.post(`${environment.basicAPI}subsystem/save`, params);
	},
	
	//系统详情
	subSystemDetail: params => {
		return axios.post(`${environment.basicAPI}subsystem/info`, params);
	},
	
	//系统删除
	delSubSystem: params => {
		return axios.post(`${environment.basicAPI}subsystem/del`, params);
	},
	
	//系统价格列表
	systemPriceList: params => {
		return axios.post(`${environment.basicAPI}systemPrice/list`, params);
	},
	
	//新增系统价格
	addSystemPrice: params => {
		return axios.post(`${environment.basicAPI}systemPrice/save`, params);
	},
	
	//系统价格详情
	systemPriceDetail: params => {
		return axios.post(`${environment.basicAPI}systemPrice/info`, params);
	},
	
	//删除系统价格
	delSystemPrice: params => {
		return axios.post(`${environment.basicAPI}systemPrice/del`, params);
	},
	
	//删除系统价格
	subsystempPullDownData: () => {
		return axios.get(`${environment.basicAPI}subsystem/subsystempPullDownData`);
	},
	
	//获取岗位列表(部门改变)
	getRoleListByDepartmentId: params => {
		return axios.post(`${environment.basicAPI}role/roleListByDepartmentIds`, params);
	},
	
	getPushMsgList: params => {
		return axios.post(`${environment.mesosphereAPI}basic/sms/query`, params);
	},
	//重新获取更新用户信息
	refreshUserInfo: params => {
		return axios.post(`${environment.basicAPI}user/homeUserInfo`, params);
	},
	
	//系统菜单树
	getSystemMenuTree: params => {
		return axios.post(
				`${environment.basicAPI}permssionManage/systemMenuTree`,
				params
		);
	},
	//岗位树
	permissionRoleList: params => {
		return axios.post(
				`${environment.basicAPI}permssionManage/permissionRoleList`,
				params
		);
	},
	//权限系统列表  =》就是自己购买的系统
	permissionSystemList: params => {
		return axios.post(
				`${environment.basicAPI}permssionManage/permissionSystemList`,
				params
		);
	},
	//权限保存
	permissionRoleSystemBinding: params => {
		return axios.post(
				`${environment.basicAPI}permssionManage/permissionRoleSystemBinding`,
				params
		);
	},
	//已绑定的权限
	roleMenuBingDingData: params => {
		return axios.post(
				`${environment.basicAPI}permssionManage/roleMenuBingDingData`,
				params
		);
	},
	//系统当前岗位所有菜单
	menuList: params => {
		return axios.post(`${environment.basicAPI}basicDataApi/systemAllMenuByToken`, params);
	},
	//数据字典
	dictDatas: params => {
		return axios.post(`${environment.basicAPI}basicDataApi/dictDatas`, params);
	},
	//已购买系统
	permissionRoleSystemList: params => {
		return axios.post(`${environment.basicAPI}permssionManage/permissionRoleSystemList`, params);
	},
	//已购买套餐详细权限
	gainDetailMenu: params => {
		return axios.post(`${environment.basicAPI}permssionManage/gainDetailMenu`, params);
	},
};
