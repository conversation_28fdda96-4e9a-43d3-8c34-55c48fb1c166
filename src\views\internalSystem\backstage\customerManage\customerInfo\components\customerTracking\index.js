import API from '@/api/internalSystem/customerManage/customerTracking'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import AddTracking from '../addTracking/index.vue'
import {
  getOptions,
} from "@/common/internalSystem/common.js"
import {
  mapGetters
} from "vuex";
export default {
  name: "customerTracking",
  data() {
    return {
      dialogVisible: false,
      ruleForm: {},
      customerStageList: [], //客户阶段列表
      tableData: [],
      tableList: [{
          name: "客户阶段",
          value: "customer_track_stage_format"
        },
        {
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "联系人",
          value: "link_man"
        },
        {
          name: "联系电话",
          value: "phone"
        },
        {
          name: "客户痛点",
          value: "customer_points"
        },
        {
          name: "使用软件",
          value: "use_software"
        }
      ],
      addDialogVisible: false,
    }
  },
  components: {
    TableView,
    AddTracking
  },
  methods: {
    Show(data = {}) {
      this.dialogVisible = true;
      this.ruleForm = data;
      this.ruleForm.customer_stage = this.ruleForm.customer_stage + ''
      this.customerStageList = getOptions('t_customer', 'customer_stage');
      this.getList();
    },
    getList() {
      this.addDialogVisible = false;
      API.query({
        customer_id: this.ruleForm.customer_id
      }).then(res => {
        this.tableData = res.data;
      });
    },
    dialogCancel() {
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    add() {
      this.addDialogVisible = true;
      this.$refs.addTracking.Show(this.ruleForm);
    },
    modify(item) {
      let params = {
        customer_tracking_id: item.customer_tracking_id
      };
      API.getInfo(params)
        .then(res => {
          this.addDialogVisible = true;
          this.$refs.addTracking.Show(res.data);
        })
        .catch(() => {});
    },
    del(item) {
      let params = {
        customer_tracking_id: item.customer_tracking_id
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
  },
  computed: {
    ...mapGetters(["buttonPermissions"])
  }
}