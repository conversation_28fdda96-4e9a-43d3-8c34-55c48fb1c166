<template>
  <div style="flex: 1">
    <vxe-table
      column-key
      border
      resizable
      show-overflow
      highlight-hover-row
      highlight-current-row
      auto-resize
      ref="xTable"
      height="100%"
      align="center"
      :checkbox-config="{
        reserve: true,
        showHeader: true,
        trigger: 'row',
      }"
      :custom-config="{
        storage: {
          visible: true,
        },
      }"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        autoClear: false, //是否立即释放焦点
        showStatus: false,
      }"
      :mouse-config="{ selected: true }"
      :data.sync="tableData"
      :expand-config="{
        expandAll: true,
      }"
      :keyboard-config="{
        isArrow: true,
        isDel: true,
        isEnter: true,
        isTab: true,

        isChecked: true,
        enterToTab: true,
      }"
      @keydown="keyDownMethods"
    >
      <vxe-table-column type="selection" width="40" v-if="isSel">
      </vxe-table-column>
      <vxe-table-column type="index" width="50" title="序号">
      </vxe-table-column>

      <vxe-table-column title="合同单号" field="contract_no">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.contract_no"> </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column title="产品服务" field="brandName">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.brandName"> </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column title="销售类型" field="detail_sell_type">
        <template slot-scope="scope">
          <div>
            {{ getSelectShowData(sell_type, scope.row.detail_sell_type) }}
          </div>
        </template>
      </vxe-table-column>

      <vxe-table-column title="维护开始时间" field="maintain_start_time">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.maintain_start_time"> </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column title="维护结束时间" field="maintain_stop_time">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.maintain_stop_time"> </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column title="原有端口数" field="original_port_count">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.original_port_count"> </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column title="新增端口数" field="add_port_count">
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.add_port_count"> </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column
        title="授权维护结束时间"
        field="authorization_maintain_stop_time"
        :edit-render="{ name: 'input' }"
      >
        <template v-slot:edit="scope">
          <my-date
            v-model="scope.row.authorization_maintain_stop_time"
            hint="请选择授权维护结束时间"
            :disabled="!isOperation"
          ></my-date>
        </template>
      </vxe-table-column>
      <vxe-table-column
        title="授权码"
        field="software_no"
        :edit-render="{ name: 'input' }"
      >
        <template v-slot:edit="scope">
          <el-input v-model="scope.row.software_no" :disabled="!isOperation">
          </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column
        title="授权备注"
        field="authorization_remark"
        :edit-render="{ name: 'input' }"
      >
        <template v-slot:edit="scope">
          <el-input
            v-model="scope.row.authorization_remark"
            :disabled="!isOperation"
          >
          </el-input>
        </template>
      </vxe-table-column>
      <vxe-table-column
        fixed="right"
        title="操作"
        :width="handleWidth"
        v-if="isOperation"
      >
        <template slot-scope="scope">
          <el-button
            @click="del(scope.row)"
            type="text"
            size="small"
            class="danger ml30"
            >删除</el-button
          >
        </template>
      </vxe-table-column>
    </vxe-table>
  </div>
</template>
<script src="./index.js"></script>
<style scoped lang="scss">
.danger {
  color: #f56c6c;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
</style>
