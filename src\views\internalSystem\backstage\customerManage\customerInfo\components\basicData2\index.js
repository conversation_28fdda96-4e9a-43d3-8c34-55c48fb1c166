import API from '@/api/internalSystem/customerManage/customerInfo'
import {
  mapGetters
} from 'vuex'
export default {
  name: "basicData",
  data() {
    return {
      ruleForm: {
        fk_sale_employee_id: "",
        link_man: "",
        fk_sale_employee_id_number: "",
        customer_name: "",
        phone: "",
        fk_sale_employee_id_name: "",
        customer_synopsis: "",
        telephone: "",
        department_name: "",
        customer_legal_name: "",
        email: "",
        company_site: "",
        customer_legal_person: "",
        qq: "",
        personnel_scale_low: "",
        personnel_scale_high: "",
        customer_type: "",
        fax: "",
        company_scale_low: "",
        company_scale_high: "",
        belong_industry: "",
        postal_code: "",
        province: "",
        customer_source: "",
        important_rank: "",
        city: "",
        introducer: "",
        introducerName: "",
        link_address: "",
        customer_stage: "",
        opening_bank: "",
        customer_account: "",
        customer_tax_number: "",
        open_ticket_address: "",
        open_ticket_phone: "",
        receive_ticket_address: "",
        receive_ticket_person: "",
        receive_ticket_phone: "",
        in_customer_add: ""
      },
      checked: true,
      isEdit: true
    };
  },
  mounted() {
    this.getInfo();
  },
  props: {
    customer_id: {
      type: Number
    },
  },
  methods: {
    getInfo() {
      API.getInfo({
        customer_id: this.customer_id
      }).then(res => {
        this.ruleForm = res.data;
        if (!res.data.customer_finance_info_id)
          this.checked = false;
        if (res.data.personnel_scale) {
          this.ruleForm.personnel_scale_low = res.data.personnel_scale.split("-")[0];
          this.ruleForm.personnel_scale_high = res.data.personnel_scale.split("-")[1];
        }
        if (res.data.company_scale) {
          this.ruleForm.company_scale_low = res.data.company_scale.split("-")[0];
          this.ruleForm.company_scale_high = res.data.company_scale.split("-")[1];
        }
      });
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'params_constant_customer_stage',
      'params_constant_customer_type',
      'params_constant_belong_industry',
      'params_constant_customer_source',
      'params_constant_important_rank'
    ]),
    fkSaleEmployeeUserInfo() {

      const name =  this.ruleForm.fk_sale_employee_id_name + '-' +
      this.ruleForm.fk_sale_employee_id_number + '-' + 
       this.ruleForm.department_name 
       return name
      }
  },
};