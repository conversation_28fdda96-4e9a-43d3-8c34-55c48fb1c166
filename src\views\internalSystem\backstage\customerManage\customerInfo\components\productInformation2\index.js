import API from '@/api/internalSystem/customerManage/customerInfo'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'

export default {
  name: "salesTransferList",
  data() {
    return {
      loading: false,
      tableData: [],
      tableList: [{
          name: "客户编号",
          value: "customer_no"
        },
        {
          name: "产品服务",
          value: "brandName"
        },
        {
          name: "软件端口数",
          value: "add_port_count"
        },
        {
          name: "软件授权码",
          value: "software_no"
        },
        {
          name: "成交金额",
          value: "contract_amount"
        },
        {
          name: "维护比例",
          value: "year_maintain_cost"
        },
        {
          name: "维护费",
          value: "maintain_cost"
        },
        {
          name: "维护起始日期",
          value: "maintain_start_time"
        },
        {
          name: "维护结束日期",
          value: "new_maintain_stop_time"
        },
        {
          name: "软件备注",
          value: "remark"
        },
        {
          name: "成交日期",
          value: "deal_time"
        },
        {
          name: "备案日期",
          value: "record_time"
        },
        {
          name: "维护状态",
          value: "maintain_stop_state"
        },
        {
          name: "软件状态",
          value: ""
        }
      ]
    };
  },
  props: {
    customer_id: {
      type: Number
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      let param = Object.assign(this.$refs.sales_pagination.obtain());
      param.customer_id=this.customer_id;
      API.getCustomerBrand(param).then(res => {
        this.tableData = res.data

        this.$refs.sales_pagination.setTotal(res.totalCount)
      }).finally(() => {
        this.loading = false
      })
    }
  },
  components: {
    TableView,
    Pagination
  }
};
