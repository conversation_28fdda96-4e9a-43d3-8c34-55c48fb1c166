<template>
  <div v-if="dialogVisible" style="overflow-y: auto; overflow-x: hidden">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="openAudit">审 核</el-button>
    </div>
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="mt10"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="主题" prop="title">
            <el-input disabled v-model="ruleForm.title" ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="二级关键词" prop="keywords">
            <el-input
              disabled
              v-model="ruleForm.keywords"
              
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="类型" prop="type">
        <el-select
          disabled
          v-model="ruleForm.type"
          placeholder="请选择类型"
          filterable
          
        >
          <el-option
            v-for="item in typeList"
            :key="item.sysValue"
            :label="item.sysName"
            :value="item.sysValue"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="内容" prop="copy_content">
        <RichText v-model="ruleForm.copy_content" :disabled="true"  />
        <!-- <div class="content">
          <div v-html="ruleForm.copy_content"></div>
        </div> -->
      </el-form-item>
    </el-form>
    <el-dialog
      title="审核"
      :visible.sync="dialogVisibleAudit"
      append-to-body
      @close="dialogCancelAudit"
      width="660px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <el-form
        :model="auditForm"
        :rules="rules"
        ref="auditForm"
        label-width="100px"
      >
        <!-- <el-form-item label="审核状态" prop="copy_state">
          <el-select
            v-model="auditForm.copy_state"
            placeholder="请选择审核状态"
            filterable
            clearable
          >
            <template v-for="item in contract_auditStateList">
              <el-option
                v-if="item.id != ruleForm.copy_state && item.id != 0"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </template>
          </el-select>
        </el-form-item> -->
        <el-form-item label="审核状态" prop="auditState">
          <el-radio-group v-model="auditForm.auditState">
            <template v-for="item in contract_auditStateList">
              <el-radio
              v-if="item.id != ruleForm.copy_state && item.id != 0"
                :key="item.id"
                :label="item.value"
                :value="item.value"
                border
                style="height: 30px;"
                ><span style="font-size:16px;">{{ item.label }}</span></el-radio
              >
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="audit_remark">
          <el-input
            v-model="auditForm.audit_remark"
            type="textarea"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm('auditForm')"
          :loading="loading"
          >保 存</el-button
        >
        <el-button @click="dialogCancelAudit">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script src="./index.js">
</script>

<style lang="scss" scoped>
@import "@/assets/css/element/font-color.scss";
</style>