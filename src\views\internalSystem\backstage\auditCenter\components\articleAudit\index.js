import API from '@/api/internalSystem/customerManage/article'
import RichText from '@/components/internalSystem/RichText/RichText.vue'
import {
  getOptions
} from "@/common/internalSystem/common.js"
import {
  mapGetters
} from 'vuex'
export default {
  name: "articleAudit",
  components: {
    RichText
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        title: "",
        copy_content: ""
      },
      loading: false,
      dialogVisibleAudit: false,
      auditForm: {
        copy_state: 2,
        audit_remark: ""
      },
      rules: {
        copy_state: [{
          required: true,
          message: "请选择审核状态",
          trigger: "change"
        }]
      },
      auditStateList: [],
      typeList:[]
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      this.auditStateList = getOptions('t_copy', 'copy_state');
      this.typeList = getOptions('t_copy', 'type');
      if (data) {
        this.ruleForm = data;
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
    },
    openAudit() {
      this.dialogVisibleAudit = true;
    },
    dialogCancelAudit() {
      this.dialogVisibleAudit = false;
      this.resetForm('auditForm');
    },
    save() {
      let params = this.auditForm;
      params.copy_id = this.ruleForm.copy_id;
      this.loading = true;
      API.updateAudit(params)
        .then(() => {
          this.dialogCancelAudit();
          this.dialogCancel();
        })
        .catch(() => {}).finally(() => {
          this.loading = false;
        });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        title: "",
        copy_content: ""
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo',"contract_auditStateList"])
  },
};