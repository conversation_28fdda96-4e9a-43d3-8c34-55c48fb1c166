<template>
  <div ref="d">
    <el-date-picker
      style="width: 100%"
      v-model="dateVal"
      class="myselectdate"
      @change="
        (e) => {
          $emit('input', e);
        }
      "
      @focus="dateFoucus"
      align="right"
      format="yyyy-MM"
      value-format="yyyy-MM"
      type="monthrange"
      :placeholder="hint"
      :picker-options="pickerOptions"
      :disabled="disabled"
      range-separator="至"
      start-placeholder="开始月份"
      end-placeholder="结束月份"
      ref="mydata3"
    >
    </el-date-picker>
  </div>
</template>

<script>
export default {
  name: "MyDate3",
  props: {
    value: {},
    hint: {
      type: String,
      default: "请选择日期",
    },
    isAllDate: {
      type: Boolean,
      default() {
        return true;
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        this.dateVal = newValue;
      },
    },
  },
  data() {
    return {
      dateVal: "",
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: "本月",
            onClick(picker) {
              picker.$emit("pick", [new Date(), new Date()]);
            },
          },
          {
            text: "今年至今",
            onClick(picker) {
              const end = new Date();
              const start = new Date(new Date().getFullYear(), 0);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 6);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  methods: {
    dateFoucus() {
      this.$nextTick(() => {
        // document.querySelectorAll(
        //   ".el-picker-panel__link-btn.el-button--text"
        // )[0].style.display = "none";
      });
    },
  },
};
</script>

<style  lang="scss">
.myselectdate .el-picker-panel__link-btn {
  display: none !important;
}
// .el-picker-panel__footer /deep/ {
//     border-top: 1px solid #e4e4e4;
//     padding: 4px;
//     text-align: right;
//     background-color: #FFF;
//     position: relative;
//     font-size: 0;
//     display:none;
// }
</style>
