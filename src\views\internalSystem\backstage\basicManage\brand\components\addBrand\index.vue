<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="70%"
    :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
    <el-form style="height: 500px; overflow: auto" :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="产品服务" prop="brand_name">
            <el-input v-model="ruleForm.brand_name" clearable :disabled="isEdit"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="软件证书编号" prop="brand_no">
            <el-input v-model="ruleForm.brand_no" clearable :disabled="isEdit"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="软件编码" prop="brand_bill_code">
            <el-input v-model="ruleForm.brand_bill_code" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="成本价" prop="brand_cost_price">
            <el-input v-model="ruleForm.brand_cost_price" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="建议售价" prop="brand_sell_price">
            <el-input v-model="ruleForm.brand_sell_price" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="税率" prop="rate">
            <el-input v-model="ruleForm.rate" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="销售类型" prop="rate">
            <el-select v-model="ruleForm.detail_sell_type" placeholder="请选择销售类型" class="inputBox" filterable clearable>
              <el-option v-for="item in sell_type" :key="item.id" :label="item.label" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否启用" prop="rate">
            <el-switch v-model="ruleForm.disable_state" :active-value="1" :inactive-value="2">
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="开发完成时间" prop="complete_time">
            <el-date-picker v-model="ruleForm.complete_time" class="w100" type="date" placeholder="选择时间"
              value-format="yyyy-MM-dd" format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上级产品">
            <el-select v-model="ruleForm.parent_id" placeholder="请选择上级产品" class="w100" filterable clearable>
              <el-option v-for="item in parentList" :key="item.brand_id" :label="item.brand_type"
                :value="item.brand_id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="是否有授权码" prop="product_is_grant">
            <el-select v-model="ruleForm.product_is_grant" placeholder="请选择是否有授权码" class="inputBox" filterable clearable>
              <el-option v-for="item in product_is_grant" :key="item.id" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="有无维护期" prop="product_is_period">
            <el-select v-model="ruleForm.product_is_period" placeholder="请选择有无维护期" class="inputBox" filterable clearable>
              <el-option v-for="item in product_is_period" :key="item.id" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="是否加入客户产品" prop="product_is_storage">
            <el-select v-model="ruleForm.product_is_storage" placeholder="请选择是否加入客户产品表" class="inputBox" filterable
              clearable>
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否默认产品" prop="product_is_default">
            <el-select v-model="ruleForm.product_is_default" placeholder="请选择是否默认产品" class="inputBox" filterable
              clearable>
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="是否需要端口" prop="product_is_port">
            <el-select v-model="ruleForm.product_is_port" placeholder="请选择是否需要端口" class="inputBox" filterable clearable>
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="产品归属" prop="product_attribution">
            <el-select v-model="ruleForm.product_attribution" placeholder="请选择产品归属" class="inputBox" filterable clearable>
              <el-option label="吉勤企业管理平台" :value="1"></el-option>
              <el-option label="吉勤云相关服务" :value="2"></el-option>
              <el-option label="阿里云服务器" :value="3"></el-option>
              <el-option label="其他未归类" :value="4"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>


      <!-- <el-form-item label="品牌分类" prop="brand_classify">
        <el-select
          v-model="ruleForm.brand_classify"
          placeholder="请选择品牌分类"
          class="inputBox"
          filterable
          clearable
          @change="brandChange"
        >
          <el-option
            v-for="item in brand_classify"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item> -->

      <!-- <el-form-item label="销售方式" prop="sale_pattern">
        <el-select
          v-model="ruleForm.sale_pattern"
          placeholder="请选择销售方式"
          class="inputBox"
          filterable
          clearable
          disabled
        >
          <el-option
            v-for="item in sale_pattern"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="软件模式" prop="product_sale_model">
        <el-select
          v-model="ruleForm.product_sale_model"
          placeholder="请选择软件模式"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in product_sale_model"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item> -->



    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm('ruleForm')">保 存</el-button>
      <el-button @click="dialogCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script src="./index.js">
</script>
