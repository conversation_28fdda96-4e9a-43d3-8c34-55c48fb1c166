import Vue from "vue";
import {GetDownload, Download} from "./download";
import {randomString} from './index'
import store from '@/store/internalSystem/index.js'
import {Message, MessageBox} from "element-ui"


Vue.prototype.permissionToCheck = (binding = '') => {
	if (!binding) return false
	return store.getters.curRoutePermissions.some(item => item.menuDescribe === binding)
}

Vue.prototype.error = (message = "网络异常") => {
	Message({
		showClose: true,
		message: message,
		type: "error"
	})
}

Vue.prototype.success = (message = "操作成功") => {
	Message({
		showClose: true,
		message: message,
		type: "success"
	})
}

Vue.prototype.warning = (message = "操作警告") => {
	Message({
		showClose: true,
		message: message,
		type: "warning"
	})
}

Vue.prototype.customizeConfirm = (
		contentTitle = "确认操作,是否继续",
		callback
) => {
	MessageBox.confirm(contentTitle, "提示", {
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		type: "warning",
		closeOnClickModal: false
	})
			.then(() => {
				callback();
			})
			.catch(() => {
			});
}
/* inputPattern正则校验  inputErrorMessage校验失败信息 */
Vue.prototype.customizePrompt = (
		title,
		inputPattern,
		inputErrorMessage,
		callback
) => {
	MessageBox.prompt(title, "提示", {
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		inputPattern: inputPattern,
		inputErrorMessage: inputErrorMessage,
		closeOnClickModal: false
	}).then(({value}) => {
		callback(value)
	}).catch(() => {
	})
}

Vue.prototype.checkFileCallback = res => {
	let file = ''
	if (!res.status) {
		Message({
			showClose: true,
			message: res.message,
			type: 'error'
		});
	}
	file = res.content;
	return file;
}

Vue.prototype.existence = (Array = [], value = "") => {
	// 值在数组内，返回 true ，不在数组内，返回 false
	// 接收两个参数,数组，值
	return Array.indexOf(value) !== -1;
}

Vue.prototype.ExportFile = params => GetDownload(params)

Vue.prototype.Download = url => Download(url)

Vue.prototype.checkFile = (
		file = {
			name: "",
			type: "",
			size: 0
		},
		Array = ["jpeg", "png", "jpg"], // 文件类型
		size = 3 // 文件大小，M为单位
) => {
	let isJPG = false;
	if (Array.length > 0) {
		const nameType = file.name.substr(file.name.lastIndexOf(".") + 1);
		isJPG = Array.indexOf(nameType) !== -1;
	} else {
		isJPG = true;
	}
	const isLt2M = file.size / 1024 / 1024 < size;
	if (!isJPG) {
		let message = "";
		Array.forEach(text => {
			message += `${text}、`;
		});
		Message({
			showClose: true,
			message: `上传文件只能是 ${message} 格式!`,
			type: "error"
		});
	}
	if (!isLt2M) {
		Message({
			showClose: true,
			message: `上传文件大小不能超过 ${size} MB!`,
			type: "error"
		});
	}
	return isJPG && isLt2M;
};

/* input框实现双击选中 数字前部清零 */
Vue.prototype.inputSelect = e => {
	e.currentTarget.select()
}
/**判断id是否存在在数组中 */
Vue.prototype.idInArray = (value = 0, array = [], field = 'id') => {
	if (value && Array.isArray(array) && array.length) {
		for (let i = 0; i < array.length; i++) {
			if (array[i][field] === value) return true;
		}
	}
	
	return false;
}

// 获取随机字符串
Vue.prototype.randomString = (len = 32) => randomString(len)
