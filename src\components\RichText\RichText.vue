<template>
  <div>
    <!-- <editor id="tinymce" v-model="tinyValue" :init="init"></editor> -->
  </div>
</template>

<script>
// import 'tinymce/skins/ui/oxide/skin.css'
// import oss from '@/api/aliyun/oss'
// import tinymce from 'tinymce'

// import 'tinymce/themes/silver/theme'
// import Editor from '@tinymce/tinymce-vue'

// import zhCN from '@/assets/tinymce/langs/zh_CN'
// // import oxide from '@/assets/tinymce/skins/ui/oxide'

// import 'tinymce/plugins/image'// 插入上传图片插件
// // import 'tinymce/plugins/media'// 插入视频插件
// import 'tinymce/plugins/table'// 插入表格插件
// import 'tinymce/plugins/lists'// 列表插件
// import 'tinymce/plugins/wordcount'// 字数统计插件
// import 'tinymce/plugins/paste' // 粘贴图片
// import 'tinymce/plugins/code' // 代码
// import 'tinymce/plugins/link' // 超链接
// import 'tinymce/plugins/hr' // 分割线
// import 'tinymce/plugins/preview' // 预览
// import 'tinymce/plugins/searchreplace' // 查找替换
// import 'tinymce/icons/default/icons.min.js'
// export default {
//   name: 'RichText',
//   components: {
//     'editor': Editor
//   },
//   props: {
//     value: {
//       type: String
//     },
//     uploadPath: {
//       type: String,
//       require: true
//     }
//   },
//   computed: {
//     tinyValue: {
//       get () {
//         return this.value
//       },
//       set (val) {
//         this.$emit('input', val)
//       }
//     }
//   },
//   data () {
//     return {
//       init: {
//         language_url: zhCN, // 语言包的路径
//         language: 'zh_CN', // 语言
//         skin_url: '../src/assets/tinymce/skins/ui/oxide', // skin路径，这里不起效，手动引入css
//         height: 400, // 编辑器高度
//         branding: false, // 是否禁用“Powered by TinyMCE”
//         menubar: true, // 顶部菜单栏显示
//         plugins: 'lists image table wordcount code link hr preview searchreplace',
//         toolbar: 'undo redo | formatselect | bold italic hr forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | lists image table | code searchreplace link preview | removeformat ',
//         images_upload_handler: (blobInfo, succFun, failFun) => {
//           let file = blobInfo.blob() // 转化为易于理解的file对象
//           // 文件验证 type size
//           if (!this.checkFile(file, [], 2)) return
//           oss.uploadFile(file).then(res => {
//             succFun(res.url)
//           }).catch(err => {
//             failFun(err.message)
//           })
//         }
//       }
//     }
//   },
//   methods: {
//   }
// }
</script>

