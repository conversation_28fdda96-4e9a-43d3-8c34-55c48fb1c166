import API from '@/api/internalSystem/contractManage/index.js'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'

import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'

import contractManageMixns from "@/mixins/contractManage.js"

import {
  mapGetters
} from "vuex";
export default {
  name: "employee",
  mixins:[contractManageMixns],
  data() {
    return {
      title: "授权码",
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        employee_number: "",
        employee_name: "",
        in_position: ''
      },
      tableList2: [{
          name: "合同编号",
          value: "employee_number",
          width: 120
        },
        {
          name: "客户名称",
          value: "employee_name"
        },
        {
          name: "软件名称",
          value: "gender_name",
          width: 80
        },
        {
          name: "版本号",
          value: "telephone",
          width: 150
        },
        {
          name: "维护起始时间",
          value: "birthday",
          width: 130
        },
        {
          name: "维护结束时间",
          value: "id_card",
          width: 180
        },
        {
          name: "授权码",
          value: "department_name"
        },
      ],
      tableList: [
        {
          name: "完成情况",
          value: "state_name",
          width: 70,
        },
      
        {
          name: "审核状态",
          value: "audit_state_name",
          width: 82,
        },
        {
          name: "编号",
          value: "contract_no",
          width: 116,
        },
        {
          name: "客户名称",
          value: "customer_name",
          width: 200,
        },
        {
          name: "销货单位",
          value: "sales_unit_id_format",
        },
        {
          name: "维护起始时间",
          value: "maintain_start_time",
          width: 96,
        },
        {
          name: "维护结束时间",
          value: "maintain_stop_time",
          width: 96,
        },
        {
          name: "合同金额",
          value: "contract_amount",
          width: 72,
        },
        {
          name: "已收款金额",
          value: "receivables_amount",
          width: 82,
        },
        {
          name: "未收款金额",
          value: "not_receivables_amount",
          width: 82,
        },
        {
          name: "已开票金额",
          value: "open_ticket_amount",
          width: 82,
        },
        {
          name: "未开票金额",
          value: "not_open_ticket_amount",
          width: 82,
        },
        {
          name: "单据备注",
          value: "remark",
        },
        {
          name: "付款方式",
          value: "pay_type_name",
          width: 106,
        },
        {
          name: "培训方式",
          value: "train_type_name",
          width: 70,
        },
        {
          name: "销售类型",
          value: "sell_type_name",
          width: 82,
        },
        {
          name: "推荐员工",
          value: "fk_recommend_employee_name",
          width: 70,
        },
        {
          name: "操作员工",
          value: "add_user_name",
          width: 70,
        },
        {
          name: "操作部门",
          value: "fk_operator_department_name",
          width: 70,
        },
        {
          name: "手机",
          value: "phone",
          width: 100,
        },
        {
          name: "客户传真",
          value: "fax",
          width: 100,
        },
        {
          name: "联系人",
          value: "link_man",
          width: 70,
        },
        {
          name: "介绍人",
          value: "introducer_format",
        },
        {
          name: "介绍合同",
          value: "introducer_contract_format",
        },
        {
          name: "销售员",
          value: "fk_sell_employee_name",
          width: 70,
        },
        {
          name: "销售部门",
          value: "fk_sell_department_name",
          width: 70,
        },
        {
          name: "单据日期",
          value: "add_time",
          width: 90,
        },
        {
          name: "客户地址",
          value: "address",
        },
        {
          name: "联系人QQ",
          value: "link_qq",
          width: 110,
        },
        {
          name: "软件序列号",
          value: "software_no",
          width: 100,
        },
        {
          name: "产品服务",
          value: "brand_name",
          width: 100,
        },
        {
          name: "软件版本",
          value: "software_version",
          width: 100,
        },
        
      ],
      employeeList:[]
    };
  },

  mounted() {
    this.getList();
    this.$store.dispatch('getEmployee').then(res => {
      this.employeeList = res;
    });
  },
  methods: {
  async getList(f = false) {
    this.isShowDetail=false;
      await this.$nextTick(()=>{})
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
      if (f)
        param.pageNum = 1;
      API.unSetCodeContractList(param).then(res => {
        this.tableData = res.data;
        this.$refs.pagination.setTotal(res.totalCount);
        
      }).finally(() => {
        this.loading = false;
      });
    },
    canUse() {
      if (!this.selectRecords.length) {
        this.warning("请先勾选1条数据");
        return false;
      }
      if (this.selectRecords.length > 1) {
        this.warning("请只勾选1条数据");
        return false;
      }
      return true;
    },
    add() {
    
      if(!this.canUse()) return 

      this.customizePrompt("填写授权码",/^[\s\S]*.*[^\s][\s\S]*$/,"授权码不能为空",(value)=>{
       let params={
        contract_detail_id:this.selectRecords[0].contract_detail_id,
        customer_contract_id:this.selectRecords[0].customer_contract_id,
        software_no:value
       }
       this.loading=true;
        API.setSoftwareNo(params).then(()=>{
          this.getList(true)
          this.success("操作成功")
        }).catch(()=>{}).finally(()=>{
          this.loading=false;
        })
        
      })
    },
 
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    }
  },

  components: {
 
    Pagination,
    TableView,
    MyDate,
  
  },
  computed: {
    ...mapGetters(["in_position"])
  }
};
