import API from '@/api/internalSystem/financialManage/invoiceIncome'
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  mapGetters
} from "vuex";
export default {
  name: "addInvoiceIncome",
  components: {
    MyDate
  },
  data() {
    return {
      dialogTitle: "新增发票进项单",
      dialogVisible: false,
      ruleForm: {
        invoice_unit: "",
        invoice_detail: "",
        invoice_number: "",
        invoice_money: "",
        invoice_time: ""
      },
      rules: {
        invoice_unit: [{
          required: true,
          message: "请输入开票单位",
          trigger: "blur"
        }],
        invoice_detail: [{
          required: true,
          message: "请输入发票明细",
          trigger: "blur"
        }],
        invoice_number: [{
          required: true,
          message: "请输入发票号码",
          trigger: "blur"
        }],
        invoice_money: [{
          required: true,
          message: "请输入收票金额",
          trigger: "blur"
        }],
        invoice_time: [{
          required: true,
          message: "请选择发票时间",
          trigger: "blur"
        }]
      }
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      if (!data) {
        this.dialogTitle = "新增发票进项单";
      } else {

        this.dialogTitle = "修改发票进项单";
        this.ruleForm = data;
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    save() {
      let params = this.ruleForm;
      if (params.invoice_income_id) {
        if (params.audit_state == 1)
          return this.error("该记录已审核通过，不允许修改")
        API.update(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      } else {
        API.add(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      }
    },
    back() {
      this.$confirm('此操作将回退该条记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          invoice_income_id: this.ruleForm.invoice_income_id,
          auditState: 3
        }
        API.updateAudit(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {}).finally(() => {
            this.loading = false;
          });
      }).catch(() => {})
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        invoice_unit: "",
        invoice_detail: "",
        invoice_number: "",
        invoice_money: "",
        invoice_time: ""
      }
    }
  },
  computed: {
    ...mapGetters(["buttonPermissions"])
  }
};