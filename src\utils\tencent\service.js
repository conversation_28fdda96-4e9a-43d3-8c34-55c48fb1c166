import environment from '@/store/environment.js'
// 腾讯智能云客服
export function service () {
	const defaultConfig = environment.state.defaultConfig[environment.state.env]
	let sign = defaultConfig.tencent_service_sign || ''
	if (!sign) return
	//参数说明
	//sign：公司渠道唯一标识，复制即可，无需改动
	//uid：用户唯一标识，如果没有则不填写，默认为空
	//data：用于传递用户信息，最多支持5个，参数名分别为c1,c2,c3,c4,c5；默认为空
	//selector：css选择器(document.querySelector, 如#btnid .chat-btn等)，用于替换默认的常驻客服入口
	//callback(type, data): 回调函数,type表示事件类型， data表示事件相关数据
	//type支持的类型：newmsg有新消息，error云智服页面发生错误， close聊天窗口关闭
	window.yzf && window.yzf.init({
		sign: sign,
		uid: '',
		data: {
			c1: '',
			c2: '',
			c3: '',
			c4: '',
			c5: ''
		},
		selector: '',
		callback: function (type, data) {
		}
	})
	//window.yzf.close() 关闭1已打开的回话窗口
	
}


