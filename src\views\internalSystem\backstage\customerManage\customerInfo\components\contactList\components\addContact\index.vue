<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="660px"
    :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="linkman_name">
            <el-input v-model="ruleForm.linkman_name" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职位" prop="linkman_position">
            <el-input v-model="ruleForm.linkman_position" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人性别" prop="linkman_gender">
            <el-select v-model="ruleForm.linkman_gender" placeholder="请选择性别" class="inputBox" filterable clearable>
              <el-option v-for="item in params_constant_gender" :key="item.id" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="ruleForm.email" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="phone">
            <el-input v-model="ruleForm.phone"  clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="传真" prop="fax">
            <el-input v-model="ruleForm.fax" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="QQ号码" prop="qq">
            <el-input v-model="ruleForm.qq"  clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="linkman_type">
            <el-select v-model="ruleForm.linkman_type" placeholder="请选择联系人类型" class="inputBox" filterable clearable>
              <el-option v-for="item in linkmanTypeList" :key="item.sysValue" :label="item.sysName" :value="item.sysValue">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="微信" prop="we_chat">
            <el-input v-model="ruleForm.we_chat" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地址" prop="link_address">
            <el-input v-model="ruleForm.link_address" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm('ruleForm')">保 存</el-button>
      <el-button @click="dialogCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script src="./index.js">

</script>