<template>
  <div v-if="dialogVisible" style="overflow-y: auto; overflow-x: hidden">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="openAudit">审 核</el-button>
    </div>
    <el-form :model="ruleForm" ref="ruleForm" label-width="160px" class="mt10">
      <el-row :gutter="20">
        <el-col :span="6" class="formItem">
          <el-form-item label="单据编号" prop="implementation_no">
            <el-input
              disabled
              v-model="ruleForm.implementation_no"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="formItem">
          <el-form-item label="单据日期" prop="update_time">
            <el-input
              disabled
              v-model="ruleForm.update_time"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="formItem">
          <el-form-item label="制单人" prop="voucher_name">
            <el-input
              disabled
              v-model="ruleForm.voucher_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="formItem">
          <el-form-item label="销售员" prop="fk_sell_employee_name">
            <el-input
              v-model="ruleForm.fk_sell_employee_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6" class="formItem2">
          <el-form-item label="销售客户" prop="customer_name">
            <el-input disabled v-model="ruleForm.customer_name" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="formItem2">
          <el-form-item label="联系人" prop="link_man">
            <el-input disabled v-model="ruleForm.link_man" clearable></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6" class="formItem2">
          <el-form-item label="手机" prop="phone">
            <el-input
              disabled
              v-model="ruleForm.phone"
              
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <!-- <el-col :span="6" class="formItem2">
          <el-form-item label="版本号" prop="version_no">
            <el-input disabled v-model="ruleForm.version_no" clearable></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="6">
          <el-form-item label="客户类型" prop="customer_type">
            <el-select disabled v-model="ruleForm.customer_type" placeholder="请选择客户类型" filterable clearable>
              <el-option v-for="item in customerTypeList" :key="item.sysValue" :label="item.sysName"
                :value="item.sysValue">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="6" class="formItem2">
          <el-form-item label="销售合同" prop="contract_no">
            <el-input
              disabled
              v-model="ruleForm.contract_no"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="formItem2">
          <el-form-item label="客户地址" prop="address">
            <el-input disabled v-model="ruleForm.address" clearable></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6" class="formItem2">
          <el-form-item label="软件版本" prop="software_version">
            <el-select
              disabled
              v-model="ruleForm.software_version"
              placeholder="请选择软件版本"
              filterable
              clearable
            >
              <el-option
                v-for="item in software_version"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="6" class="formItem2">
          <el-form-item label="培训方式" prop="train_type">
            <el-select
              disabled
              v-model="ruleForm.train_type"
              placeholder="请选择培训方式"
              filterable
              clearable
            >
              <el-option
                v-for="item in contract_train_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="formItem2">
          <el-form-item
            label="培训天数"
            prop="train_days"
            v-if="ruleForm.train_type === 1"
          >
            <el-input
              :disabled="!isEdit"
              v-model="ruleForm.train_days"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6" class="formItem2">
          <el-form-item label="实施人员" prop="implementation_user_id">
            <el-select
              :disabled="!isEdit"
              v-model="ruleForm.implementation_user_id"
              placeholder="请选择实施人员"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in implementationUserList"
                :key="item.employeeId"
                :label="item.employee_number + '-' + item.employee_name"
                :value="item.employeeId"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" class="formItem2">
          <el-form-item label="实施时间" prop="implementation_time">
            <el-input
              disabled
              v-model="ruleForm.implementation_time"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6" class="formItem2">
          <el-form-item label="原有端口数" prop="original_port_count">
            <el-input
              disabled
              v-model="ruleForm.original_port_count"
              
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="6" class="formItem2">
          <el-form-item label="销售类型" prop="sell_type">
            <el-select
              disabled
              v-model="ruleForm.sell_type"
              placeholder="请选择销售类型"
              filterable
              clearable
            >
              <el-option
                v-for="item in sell_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="6" class="formItem2">
          <el-form-item label="结束时间" prop="over_time">
            <el-input
              disabled
              v-model="ruleForm.over_time"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6" class="formItem2">
          <el-form-item label="现有端口数" prop="add_port_count">
            <el-input
              disabled
              v-model="ruleForm.add_port_count"
              
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12" class="formItem2">
          <el-form-item label="单据备注" prop="remark">
            <el-input
              disabled
              type="textarea"
              placeholder="请输入单据备注"
              v-model="ruleForm.remark"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="formItem2">
          <el-form-item label="注意事项" prop="attention_matters">
            <el-input
              disabled
              type="textarea"
              placeholder="请输入注意事项"
              v-model="ruleForm.attention_matters"
              clearable
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="formItem2">
          <el-form-item label="实施总结" prop="implementation_conclusion">
            <el-input
              disabled
              type="textarea"
              placeholder="请输入实施总结"
              v-model="ruleForm.implementation_conclusion"
              clearable
              :rows="3"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-divider></el-divider>
    <table-view
      :tableList="tableList"
      :tableData="tableData"
      :tableHeight="400"
      :isTableIndex="true"
    >
    </table-view>
    <el-dialog
      title="审核"
      :visible.sync="dialogVisibleAudit"
      append-to-body
      @close="dialogCancelAudit"
      width="660px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <el-form
        :model="auditForm"
        :rules="rules"
        ref="auditForm"
        label-width="100px"
      >
        <!-- <el-form-item label="审核状态" prop="auditState">
          <el-select
            v-model="auditForm.auditState"
            placeholder="请选择审核状态"
            filterable
            clearable
          >
            <template v-for="item in contract_auditStateList">
              <el-option
                v-if="item.id != ruleForm.auditState"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </template>
          </el-select>
        </el-form-item> -->
        <el-form-item label="审核状态" prop="auditState">
          <el-radio-group v-model="auditForm.auditState">
            <template v-for="item in contract_auditStateList">
              <el-radio
              v-if="item.id != ruleForm.auditState"
                :key="item.id"
                :label="item.value"
                :value="item.value"
                border
                style="height: 30px;"
                ><span style="font-size:16px;">{{ item.label }}</span></el-radio
              >
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="auditRemark">
          <el-input
            v-model="auditForm.auditRemark"
            type="textarea"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm('auditForm')"
          :loading="loading"
          >保 存</el-button
        >
        <el-button @click="dialogCancelAudit">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script src="./index.js">
</script>

<style lang="scss" scoped>
@import "@/assets/css/element/font-color.scss";
</style>