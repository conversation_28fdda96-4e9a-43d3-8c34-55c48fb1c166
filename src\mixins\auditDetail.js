
import auditAPI from '@/api/internalSystem/common/auditInfo.js'

import IntentionAudit from "@/views/internalSystem/backstage/auditCenter/components/intentionAudit/index.vue"

import VisitingAudit from "@/views/internalSystem/backstage/auditCenter/components/visitingAudit/index.vue"
import UpdateAudit from "@/views/internalSystem/backstage/auditCenter/components/updateAudit/index.vue"
import ImpleAudit from "@/views/internalSystem/backstage/auditCenter/components/impleAudit/index.vue"
import ArticleAudit from "@/views/internalSystem/backstage/auditCenter/components/articleAudit/index.vue"
import QuotationAudit from "@/views/internalSystem/backstage/auditCenter/components/quotationAudit/index.vue"
import ContractAudit from "@/views/internalSystem/backstage/auditCenter/components/contractAudit/index.vue"
import NewContractAudit from "@/views/internalSystem/backstage/auditCenter/components/newContractAudit/index.vue"
import TicketAudit from "@/views/internalSystem/backstage/auditCenter/components/ticketAudit/index.vue"
import IncomeAudit from "@/views/internalSystem/backstage/auditCenter/components/incomeAudit/index.vue"
import InvoiceIncomeAudit from "@/views/internalSystem/backstage/auditCenter/components/invoiceIncomeAudit/index.vue"
import InvoiceAudit from "@/views/internalSystem/backstage/auditCenter/components/invoiceAudit/index.vue"
import { checkDataState} from "@/utils/validate.js"
export default {
  data () {
    return {
      isAudit: false
    }
  },
  methods: {
    toAuditDet(item,tareas_type = '',filed='') {
      if(item.data_state ===2){
        return this.error('该记录是旧数据，请上旧系统继续操作')
      }

      if(filed&&item[filed]!==3) return this.error("不是待审核状态不可跳转")
      let type = '';
      let name = '';
      if (tareas_type === '意向报告单审核') {
        type = 'intention';
        name = 'customer_intention_id';
      } else if (tareas_type === '销售客户回访单审核') {
        type = 'visiting';
        name = 'sales_visiting_id';
      } else if (tareas_type === '客户修改审核') {
        type = 'update';
        name = 'customer_temp_id';
      } else if (tareas_type === '实施单审核') {
        type = 'imple';
        name = 'implementation_id';
      } else if (tareas_type === '文案审核') {
        type = 'article';
        name = 'copy_id';
      } else if (tareas_type === '报价单审核') {
        type = 'quotation';
        name = 'quotation_id';
      } else if (tareas_type === '合同单审核') {
        type = 'contract';
        name = 'customer_contract_id';
      } else if (tareas_type === '开票单审核') {
        type = 'ticket';
        name = 'ticket_id';
      } else if (tareas_type === '收入单审核') {
        type = 'income';
        name = 'income_id';
      } else if (tareas_type === '发票进项单审核') {
        type = 'invoiceIncome';
        name = 'invoice_income_id';
      } else if (tareas_type === '报销单审核') {
        type = 'invoice';
        name = 'financial_cost_invoice_id';
      }
      
      let params = {
        [`${name}`]: item[`${name}`]
      };
      auditAPI[`${type}Info`](params)
        .then(data => {
          this.isAudit = true;
          this.$refs[`${type}Audit`].Show(data.data);
        })
        .catch(() => {});
    }
  },
  components: {
    IntentionAudit,
    VisitingAudit,
    UpdateAudit,
    ImpleAudit,
    ArticleAudit,
    QuotationAudit,
    ContractAudit,
    TicketAudit,
    IncomeAudit,
    InvoiceIncomeAudit,
    InvoiceAudit,
    NewContractAudit
  }
}