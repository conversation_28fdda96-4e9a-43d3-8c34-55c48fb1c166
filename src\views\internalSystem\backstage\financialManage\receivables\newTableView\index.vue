<template>
  <div style="flex: 1">
    <vxe-table
      id="tableId1"
      column-key
      border
      resizable
      show-overflow
      show-header-overflow
      highlight-hover-row
      highlight-current-row
      auto-resize
      ref="xTable"
      height="100%"
      :loading="loading"
      :default-expand-all="true"
      align="center"
      :checkbox-config="{
        reserve: true,
        showHeader: true,
        trigger: 'row',
      }"
      :custom-config="{
        storage: {
          visible: true,
        },
      }"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        autoClear: false, //是否立即释放焦点
        showStatus: false,
      }"
      :mouse-config="{ selected: true }"
      :edit-rules="editRules"
      :data="tableData"
      :expand-config="{
        expandAll: true,
      }"
      :keyboard-config="{
        isArrow: true,
        isDel: true,
        isEnter: true,
        isTab: true,

        isChecked: true,
        enterToTab: true,
      }"
      @keydown="keyDownMethods"
    >
      <vxe-table-column type="expand" width="60">
        <template v-slot:content="props">
          <vxe-table
   
            :data="props.row.children"
            style="width: 100%"
            border
            id="tableId2"
            column-key
            resizable
            show-overflow
            show-header-overflow
            highlight-hover-row
            highlight-current-row
            auto-resize
            ref="xTable"

            :loading="loading"
            :default-expand-all="true"
            align="center"
            :checkbox-config="{
              reserve: true,
              showHeader: true,
              trigger: 'row',
            }"
            :custom-config="{
              storage: {
                visible: true,
              },
            }"
            :edit-config="{
              trigger: 'click',
              mode: 'cell',
              autoClear: false, 
              showStatus: false,
            }"
            :mouse-config="{ selected: true }"
            :edit-rules="editRules"
            :expand-config="{
              expandAll: true,
            }"
            :keyboard-config="{
              isArrow: true,
              isDel: true,
              isEnter: true,
              isTab: true,
              isChecked: true,
              enterToTab: true,
            }"

          >
            <vxe-table-column prop="brand_name" label="产品" width="400">
            </vxe-table-column>
            <vxe-table-column
              prop="sell_type_name"
              label="销售类型"
              width="180"
            >
            </vxe-table-column>
            <vxe-table-column
              prop="contract_amount"
              label="合同金额"
              width="100"
            >
            </vxe-table-column>
            <!-- <vxe-table-column
              prop="outbound_money"
              label="已出库金额"
              width="100"
            >
            </vxe-table-column> -->
            <vxe-table-column
              prop="receivables_money"
              label="已收款金额"
              width="120"
            >
            </vxe-table-column>
            <vxe-table-column
              title="本次分配金额"
              width="120"
              field="new_money"
              :edit-render="{ name: 'input', enabled: true }"
            >
              <template v-slot:edit="scope">
                <el-input
                  v-model="scope.row.new_money"
                  :disabled="!isDel"
                  @change="
                    changeNewAmount(
                      scope.row,
                      props.row,
                      props.row.children
                    )
                  "
                >
                </el-input>
              </template>
            </vxe-table-column>
            <vxe-table-column label="操作" width="120" align="center">
              <template slot-scope="scope">
                <el-button
                :disabled="!isDel"
                  @click="
                    handleClick(
                      scope.row,
                      props.row,
                      props.row.children
                    )
                  "
                  type="text"
                  size="small"
                  class="danger ml30"
                  >删除</el-button
                >
              </template>
            </vxe-table-column>
          </vxe-table>
        </template>
      </vxe-table-column>

      <!-- 列表前面 -->
      <template v-for="item in tableList">
        <vxe-table-column
          v-if="item.layout === '01'"
          :key="item.value"
          :title="item.name"
          :field="item.value"
          :width="item.width ? item.width : '120'"
          :sortable="item.sortable ? item.sortable : false"
          :edit-render="
            item.value === 'sell_type'
              ? { name: 'input', enabled: item.value === 'sell_type' }
              : undefined
          "
        >
          <template slot-scope="scope">
            <el-popover
              placement="bottom"
              title=""
              width="200"
              :open-delay="300"
              trigger="hover"
              :content="
                item.value !== 'sell_type' ? scope.row[item.value] + '' : ''
              "
            >
              <div
                slot="reference"
                class="ellipsis"
                v-if="item.value !== 'sell_type'"
              >
                {{ scope.row[item.value] }}
              </div>

              <div v-else slot="reference" class="ellipsis">
                {{ getSelectShowData(sell_type, scope.row.sell_type) }}
              </div>
            </el-popover>
          </template>
          <template v-slot:edit="scope" v-if="item.value === 'sell_type'">
            <el-select
              v-model="scope.row[item.value]"
              placeholder="请选择销售类型"
              filterable
              clearable
              :disabled="!isDel"
            >
              <el-option
                v-for="item in sell_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </template>
        </vxe-table-column>
      </template>
      <vxe-table-column
        title="收款项目"
        width="180"
        field="project"
        :edit-render="{ name: 'input' }"
      >
        <template slot-scope="scope">
          <div>
            {{ getSelectShowData(receivables_project, scope.row.project) }}
          </div>
        </template>
        <template v-slot:edit="scope">
          <el-select
            v-model="scope.row.project"
            placeholder="请选择收款项目"
            filterable
            clearable
            :disabled="!isDel"
          >
            <el-option
              v-for="item in receivables_project"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </template>
      </vxe-table-column>

      <!-- 列表中间 -->
      <template v-for="item in tableList">
        <vxe-table-column
          v-if="item.layout === '03'"
          :key="item.value"
          :title="item.name"
          :field="item.value"
          :width="item.width ? item.width : ''"
          :sortable="item.sortable ? item.sortable : false"
        >
          <template slot-scope="scope">
            <el-popover
              placement="bottom"
              title=""
              width="200"
              :open-delay="300"
              trigger="hover"
              :content="scope.row[item.value] + ''"
            >
              <div slot="reference" class="ellipsis">
                {{ scope.row[item.value] }}
              </div>
            </el-popover>
          </template>
        </vxe-table-column>
      </template>
      <!--         :edit-render="{ name: 'input' }" -->
      <vxe-table-column
        title="收款金额"
        width="180"
        field="amount"

      >
        <template v-slot:edit="scope">
          <!--        @change="changeAmount(scope.row)" -->
          <el-input v-model="scope.row.amount" :disabled="!isDel"> </el-input>
        </template>
      </vxe-table-column>

      <vxe-table-column
        title="到款日期"
        width="180"
        field="confirm_money_time"
        :edit-render="{ name: 'input' }"
      >
        <template v-slot:edit="scope">
          <my-date
            v-model="scope.row.confirm_money_time"
            :isAllDate="false"
            hint="请选择到款日期"
            :disabled="!isDel"
          >
          </my-date>
        </template>
      </vxe-table-column>

      <!-- 列表后面 -->
      <template v-for="item in tableList">
        <vxe-table-column
          v-if="item.layout === '02'"
          :key="item.value"
          :title="item.name"
          :field="item.value"
          :width="item.width ? item.width : ''"
          :sortable="item.sortable ? item.sortable : false"
        >
          <template v-slot:edit="scope">
            <el-popover
              placement="bottom"
              title=""
              width="200"
              :open-delay="300"
              trigger="hover"
              :content="scope.row[item.value] + ''"
            >
              <div
                slot="reference"
                class="ellipsis"
                v-if="item.value !== 'sell_type'"
              >
                {{ scope.row[item.value] }}
              </div>
            </el-popover>
          </template>
        </vxe-table-column>
      </template>
      <vxe-table-column
        title="收款备注"
        width="180"
        field="confirm_money_remarks"
        :edit-render="{ name: 'input' }"
      >
        <template v-slot:edit="scope">
          <el-input
            v-model="scope.row.confirm_money_remarks"
            :disabled="!isDel"
          >
          </el-input>
        </template>
      </vxe-table-column>
      
      <!-- 技术员 -->
      <vxe-table-column
        title="技术员"
        width="180"
        field="bear_employee_id"
        :edit-render="{ name: 'input' }"
      >
        <template slot-scope="scope">
          <div>
            {{ getEmployeeName(scope.row.bear_employee_id) }}
          </div>
        </template>
        <template v-slot:edit="scope">
          <el-select
            v-model="scope.row.bear_employee_id"
            placeholder="请选择技术员"
            filterable
            clearable
            :disabled="!isDel"
          >
            <el-option
              v-for="item in employeeList"
              :key="item.employeeId"
            :label="item.employee_number + '-' + item.employee_name"
              :value="item.employeeId"
            >
            </el-option>
          </el-select>
        </template>
      </vxe-table-column>
      <!-- <vxe-table-column label="单据备注" width="180" prop="receivables_remarks">
      <template slot-scope="scope">
        <el-input v-model="scope.row.receivables_remarks"> </el-input>
      </template>
    </vxe-table-column> -->
      <!-- <vxe-table-column label="凭证号码" width="180" prop="voucher_num">
      <template slot-scope="scope">
        <el-input v-model="scope.row.voucher_num"> </el-input>
      </template>
    </vxe-table-column> -->


    </vxe-table>
  </div>
</template>
<script src="./index.js"></script>
<style scoped lang="scss">
.danger {
  color: #f56c6c;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
@import "@/assets/css/element/font-color.scss";
</style>
