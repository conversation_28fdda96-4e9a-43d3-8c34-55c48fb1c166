import axios from "axios";
import nprogress from "nprogress";
import {Message} from "element-ui";
import {errorLogSendEmail} from "./errorLogSendEmail.js";
import environment from "@/store/environment.js";
import Cookies from "js-cookie";

const service = axios.create({
	timeout: 60 * 6000, // 响应时间
	withCredentials: true
});

service.interceptors.request.use(config => {
	config.headers["Content-Type"] =
			config.data && config.data.requestType == "file"
					? "multipart/form-data"
					: "application/json"
	nprogress.start()
	const defaultConfig = environment.state.defaultConfig[environment.state.env]
	const token = Cookies.get(defaultConfig.cookiesKey);

	config.headers.token = token;
	if(environment.state.userInfo.userId) config.headers.nowLoginUserId = environment.state.userInfo.userId;
	if (!config.data) config.data = {}
	return config
});

service.interceptors.response.use(
		res => {
			if (res.status === 200) {
				res = res.data;
				nprogress.done();
				const defaultConfig = environment.state.defaultConfig[environment.state.env];
				switch (res.code) {
					case -2:
						Cookies.remove(defaultConfig.cookiesKey)
						Message.error(res.msg)
						if (window.location.href.indexOf('login') !== -1) {
							//已在登录页就不处理
							return Promise.reject(res)
						}
						var callBackPath = window.location.hash.replace('#', '')
						window.location.replace(`/${environment.state.env}/#/login?callBackPath=${callBackPath}`)
						return Promise.reject(res)
					case -1:
					case 0:
						res.msg
								? Message.error(res.msg)
								: Message.error('接口异常,请联系管理员。')
						return Promise.reject(res)
					case 1:
						if (!res.data) {
							Message.success(res.msg)
						}
						if (res.hasOwnProperty('totalCount')) return res
						
						return res.data;
					
					case 'SUCCESS' || 'REMAINING_TIME' || 'OUT_TIME': // 微信支付查询返回的状态码，需要判断所以得返回res
						return res
					default:
						return res
				}
			} else {
				Message.error('接口返回识别码异常,请联系管理员')
				errorLogSendEmail(res)
				return Promise.reject(res)
			}
		},
		err => {
			// 判断请求异常信息中是否含有超时timeout字符串
			if (err.response) errorLogSendEmail(err.response)
			
			Message.error('服务器开小差了,请稍后再试。')
			return Promise.reject(err)
		}
);

export default service
