<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small">
      <el-form-item label="查询条件">
        <el-select
          v-model="formSearch.employee_number"
          placeholder="请选择销售员"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employee_number"
            :label="item.employee_number + '-' + item.employee_name"
            :value="item.employee_number"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.startTime"
          hint="请选择开始时间"
          style="width: 160px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <my-date
          v-model="formSearch.endTime"
          hint="请选择结束时间"
          style="width: 160px"
        ></my-date>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
      </el-form-item>
    </el-form>
    <table-view
      :tableList="tableList"
      :tableData="tableData"
      :isOperation="false"
    >
    </table-view>
    <Pagination ref="pagination" @success="getList" />
  </div>
</template>

<script src="./index.js"></script>