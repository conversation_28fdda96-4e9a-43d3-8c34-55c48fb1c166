import API from '@/api/internalSystem/basicManage/parameter'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddParameter from "./components/addParameter/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import { mapGetters } from "vuex"
export default {
  name: "parameter",
  data() {
    return {
      title: "税率比例参数",
      loading: false,
      tableData: [],
      formSearch: {
        content: "",
        parameter_type: ``
      },
      tableList: [{
          name: "参数类型",
          value: "parameter_type_name"
        },
        {
          name: "税率 / 比例(%)",
          value: "content"
        },
        {
          name: "修改人",
          value: "add_user_name"
        },
        {
          name: "修改时间",
          value: "update_time"
        }
      ],
      costProjectList: []
    };
  },

  mounted() {
    this.getList();
  },
  methods: {
    getList(f = false) {
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
      if (f)
        param.pageNum = 1;
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    add() {
      this.$refs.AddParameter.Show();
    },
    //打开修改税率比例参数会话框
    modify(item) {
      let params = {
        parameter_id: item.parameter_id
      };
      API.getInfo(params)
        .then(data => {
          this.$refs.AddParameter.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      let params = {
        parameter_id: item.parameter_id
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    }
  },

  components: {
    AddParameter,
    Pagination,
    TableView
  },
  computed: {
    ...mapGetters(["params_constant_parameter_type"])
  }
};
