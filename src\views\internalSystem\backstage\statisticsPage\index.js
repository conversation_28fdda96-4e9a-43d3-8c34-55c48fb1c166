import Img from "@/assets/images/mall_admin/index";
import API from "@/api/internalSystem/common/index.js";
import auditAPI from "@/api/internalSystem/common/auditInfo.js";
import HeadBox from "./components/headBox/index.vue";
import ShowTable from "./components/ShowTable/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
// import Echarts from "vue-echarts";
// 引入折线图等组件
// import "echarts/lib/chart/line";
// 引入提示框和title组件
// import "echarts/lib/component/title";
// import "echarts/lib/component/tooltip";
// import "echarts/lib/component/legend";

import moment from "moment";
//审核组件
// import AuditCenter from "@/views/internalSystem/backstage/auditCenter/index.vue"
// import IntentionAudit from "@/views/internalSystem/backstage/auditCenter/components/intentionAudit/index.vue"
// import VisitingAudit from "@/views/internalSystem/backstage/auditCenter/components/visitingAudit/index.vue"
// import UpdateAudit from "@/views/internalSystem/backstage/auditCenter/components/updateAudit/index.vue"
// import ImpleAudit from "@/views/internalSystem/backstage/auditCenter/components/impleAudit/index.vue"
// import ArticleAudit from "@/views/internalSystem/backstage/auditCenter/components/articleAudit/index.vue"
// import QuotationAudit from "@/views/internalSystem/backstage/auditCenter/components/quotationAudit/index.vue"
// import ContractAudit from "@/views/internalSystem/backstage/auditCenter/components/contractAudit/index.vue"
// import NewContractAudit from "@/views/internalSystem/backstage/auditCenter/components/newContractAudit/index.vue"
// import TicketAudit from "@/views/internalSystem/backstage/auditCenter/components/ticketAudit/index.vue"
// import IncomeAudit from "@/views/internalSystem/backstage/auditCenter/components/incomeAudit/index.vue"
// import InvoiceIncomeAudit from "@/views/internalSystem/backstage/auditCenter/components/invoiceIncomeAudit/index.vue"
// import InvoiceAudit from "@/views/internalSystem/backstage/auditCenter/components/invoiceAudit/index.vue"
import { concat } from "lodash";
import { mapGetters, mapMutations } from "vuex";
export default {
  name: "statisticsPage",
  components: {
    // "vue-charts": Echarts,
    HeadBox,
    ShowTable,
    MyDate,
    // AuditCenter,
    // IntentionAudit,
    // VisitingAudit,
    // UpdateAudit,
    // ImpleAudit,
    // ArticleAudit,
    // QuotationAudit,
    // ContractAudit,
    // NewContractAudit,
    // TicketAudit,
    // IncomeAudit,
    // InvoiceIncomeAudit,
    // InvoiceAudit,
  },
  data() {
    return {
      Img,
      msgList: [],
      userCount: "",
      proSearchList: [],
      proBrowseList: [],
      addUserCount: "",
      noOverdueList: [], //即将到期客户
      expired: [], //将到期客户
      businessData: {}, //经营情况
      customerData: {}, //我的客户情况
      options: {
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          type: "category",
          data: [],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            label: {
              normal: {
                show: true,
                position: "top",
              },
            },
            data: [],
            type: "line",
          },
        ],
      },
      auditNum: "",
      tableHeader: [
        {
          label: "单据编号",
          prop: "tareas_no",
          width: "126",
        },
        // {
        //   label: "审核单据类型",
        //   prop: "tareas_type",
        //   width:'110'
        // },
        {
          label: "客户名称",
          prop: "customer_name",
        },
        // {
        //   label: "员工名称",
        //   prop: "add_user_name",
        //   width:'140'
        // },
        // {
        //   label: "发起审核时间",
        //   prop: "tareas_time",
        //   width:'140'
        // }
      ],
      tableHeader2: [
        {
          label: "客户名称",
          prop: "customer_name",
          // width:220
        },
        {
          label: "合同到期天数",
          prop: "expire_time",
          width:120
        },
        // {
        //   label: "剩余天数",
        //   prop: "remaining_days",
        // },
      ],
      tableData: [],
      tableData2: [],
      bottomHeader: [
        {
          label: "客户名称",
          prop: "customerName",
          width: "200",
        },
        {
          label: "产品服务",
          prop: "brandName",
        },
        {
          label: "业务员",
          prop: "employeeName",
          width: "80",
        },
        {
          label: "服务类型",
          prop: "sellTypeName",
          width: "90",
        },
        {
          label: "是否过期",
          prop: "overdueFlag",
          width: "80",
        },
        {
          label: "到期时间",
          prop: "newMaintainStopTime",
          width: "100",
        },
      ],
      headerRowStyle: {
        backgroundColor: "#F8F9FE",
        color: "#333",
      },
      customerSituation: [
        {
          label: "正常维护客户",
          prop: "zcwhkhNum",
          icon: "",
          iconColor: "",
        },
        {
          label: "按次维护客户",
          prop: "acwhkhNum",
          icon: "",
          iconColor: "",
        },
        {
          label: "正在实施客户",
          prop: "zzsskhNum",
          icon: "",
          iconColor: "",
        },
        {
          label: "完成实施客户",
          prop: "wcsskhNum",
          icon: "",
          iconColor: "",
        },
        {
          label: "潜在客户",
          prop: "qzkhNum",
          icon: "",
          iconColor: "",
        },
        {
          label: "过潜客户",
          prop: "zqgqkhNum",
          icon: "",
          iconColor: "",
        },
      ],
      loading: false,
      isExpired: false,
      headBoxParams: {},
      isAudit: false,
      allCustomer: [],
      employeeList: [],
      formSearch: {
        fk_sell_employee_id: "",
        startTime:"",
        endTime:""
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo", "indexFkSellEmployeeId","cookiesUserInfo"]),
    showExpiredData() {
      return this.isExpired ? this.expired : this.noOverdueList;
    },
  },
  mounted() {
    this.headBoxParams.startTime = moment(new Date())
      .startOf("month")
      .format("YYYY-MM-DD");
    this.headBoxParams.endTime = moment(new Date())
      .endOf("month")
      .format("YYYY-MM-DD");
    if (!this.employeeList || this.employeeList.length === 0) {
      this.$store.dispatch("getEmployee").then((res) => {
        this.employeeList = res;
      });
    }
    if (this.indexFkSellEmployeeId) {
      if (Number(this.indexFkSellEmployeeId) > 0) {
        this.formSearch.fk_sell_employee_id = this.indexFkSellEmployeeId;
      }
    }

    this.getList();
    // this.getTaskToDo()
    // 查询合同到期时间
    if (['总经理','财务经理','财务专员'].includes(this.cookiesUserInfo.role_name)) {
      API.checkContractExpires().then(res=>{
        if(res.data && res.data.length > 0){
          let message = ""
          res.data.forEach(item=>{
            message += `<div><h3>${item.employee_name} : ${item.contract_expires}</h3></div>`
          })
          this.$notify({
            title: '劳动合同到期提示',
            dangerouslyUseHTMLString: true,
            message: message ,
            type: 'warning',
            duration: 10000
          });  
        }
      })
    }
  },
  methods: {
    ...mapMutations(["SET_SELL_EMLOYEE_ID"]),
    //查询待办任务列表
    // getTaskToDo() {
    //   API.getTaskToDoQuery({
    //     task_state: 1
    //   }).then(({
    //     data
    //   }) => {

    //     this.tableData = data
    //   })
    // },
    changeValue(val) {
      this.SET_SELL_EMLOYEE_ID(this.formSearch.fk_sell_employee_id);
      this.getBalanceDays();
      this.getPrepareForOpenSea()
    },
    changeTime(){

      this.getBalanceDays();
    },
    takeSellType(sellType) {
      if ([1, 4, 6, 2, 7, 8, 3].includes(sellType)) {
        return "维护";
      } else if ([5, 9].includes(sellType)) {
        return "租用";
      }
      return "其他";
    },
    //续费
    handleClick1(row) {
      console.log(row);
      this.$router.push({
        // path: "/backstage/salesManage/contractAdd",
        path: "/backstage/salesManage/contract",
        query: {
          row: row,
          sellType: [5, 9].includes(row.sellType) ? 5 : 3,
        },
      });
    },
    // 催缴
    handleClick2(row, flag) {
      API.remindCustomer({
        row: row,
        flag: flag,
      }).then(({ data }) => {
        this.success("邮箱发送成功");
      });
    },
    getList() {
      this.isAudit = false;
      this.getAuditCenter();
      this.getHomeBusiness();
      this.getHomeCustomer();
      this.getBalanceDays();
      this.getPrepareForOpenSea()
    },
    getHomeCustomer() {
      API.getHomeCustomer().then(({ data }) => {
        this.customerData = data;
      });
    },
    getHomeBusiness() {
      API.getHomeBusiness().then(({ data }) => {
        this.businessData = data;
      });
    },
    getBalanceDays() {
      if (this.loading) return;
      this.loading = true;
      if (!["总经理"].includes(this.cookiesUserInfo.role_name)) {
        this.formSearch.fk_sell_employee_id = this.cookiesUserInfo.employeeId;
      }
      API.getBalanceDays(this.formSearch)
        .then(({ data }) => {
          data.expired.forEach((item) => {
            item.sellTypeName = this.takeSellType(item.sellType);
            item.overdueFlag = "是";
          });
          this.expired = data.expired;
          data.noOverdueList.forEach((item) => {
            item.sellTypeName = this.takeSellType(item.sellType);
            item.overdueFlag = "否";
          });
          this.noOverdueList = data.noOverdueList;
          this.allCustomer = [];
          this.allCustomer = concat(this.noOverdueList, this.expired);
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    getPrepareForOpenSea(){
      let params = {}
      if (['总经理','财务经理','财务专员'].includes(this.cookiesUserInfo.role_name)) {
        params['fk_sale_employee_id'] = this.formSearch.fk_sell_employee_id
      }else{
        params['fk_sale_employee_id'] = this.cookiesUserInfo.employeeId;
      }
      API.prepareForOpenSea(params).then((res) => {
        this.tableData2 = res.data;
      });
    },
    getAuditCenter() {
      let param = {
        pageNum: 1,
        pageSize: 20,
      };

      // API.getAuditCount().then((res) => {
      //   this.auditNum = res.totalCount;
      // });
      API.queryAudit(param).then((res) => {
        this.tableData = res.data;
        this.auditNum  = res.data.length || 0
      });

    },
    auditList() {
      // if (!this.auditNum) return this.error("无未审核单据");
      this.$router.push({
        path: "/backstage/customerManage/auditCenter",
      });
    },
    cellStyleFn({ column }) {
      if (column.label === "名称")
        return {
          color: "#007dff",
        };
    },
    tableSelect(row) {
    },
    dealTask(item) {

      if (item.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      let type = "";
      let name = "";
      if (item.tareas_type === "意向报告单审核") {
        type = "intention";
        name = "customer_intention_id";
      } else if (item.tareas_type === "销售客户回访单审核") {
        type = "visiting";
        name = "sales_visiting_id";
      } else if (item.tareas_type === "客户修改审核") {
        type = "update";
        name = "customer_temp_id";
      } else if (item.tareas_type === "实施单审核") {
        type = "imple";
        name = "implementation_id";
      } else if (item.tareas_type === "文案审核") {
        type = "article";
        name = "copy_id";
      } else if (item.tareas_type === "报价单审核") {
        type = "quotation";
        name = "quotation_id";
      } else if (item.tareas_type === "合同单审核") {
        type = "contract";
        name = "customer_contract_id";
      } else if (item.tareas_type === "开票单审核") {
        type = "ticket";
        name = "ticket_id";
      } else if (item.tareas_type === "收入单审核") {
        type = "income";
        name = "income_id";
      } else if (item.tareas_type === "发票进项单审核") {
        type = "invoiceIncome";
        name = "invoice_income_id";
      } else if (item.tareas_type === "报销单审核") {
        type = "invoice";
        name = "financial_cost_invoice_id";
      }
      let params = {
        [`${name}`]: item.tareas_id,
      };
      auditAPI[`${type}Info`](params)
        .then((data) => {
          this.isAudit = true;
          this.$refs[`${type}Audit`].Show(data.data);
        })
        .catch(() => {});
    },
  },
};
