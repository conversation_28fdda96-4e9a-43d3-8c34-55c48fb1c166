import Axios from "@/api";
import environment from "@/api/environment";

export default {
    
    /**公用  common */
    commonCustomerDropDown: params => Axios.post(`${environment.basicAPI}omronCustomer/customerDropDown`, params),
    

    /**         项目申请单    projectApply */
    //列表
    projectApplyList: params => Axios.post(`${environment.basicAPI}jqOmronCustomerProject/list`, params),
    //保存
    projectApplySave: params => Axios.post(`${environment.basicAPI}jqOmronCustomerProject/save`, params),
    //详情
    projectApplyDetail: params => Axios.post(`${environment.basicAPI}jqOmronCustomerProject/info`, params),
    //删除
    projectApplyDel: params => Axios.post(`${environment.basicAPI}jqOmronCustomerProject/del`, params),


    /** 配置单     omronConfiguration     */
    //保存
    omronConfigurationSave: params => Axios.post(`${environment.basicAPI}jqOmronConfig/save`, params),
    //详情
    omronConfigurationInfo: params => Axios.post(`${environment.basicAPI}jqOmronConfig/info`, params),
    

    /**客户管理   omronCustomer  */
     //列表
     omronCustomerList: params => Axios.post(`${environment.basicAPI}omronCustomer/list`, params),
     //保存
     omronCustomerSave: params => Axios.post(`${environment.basicAPI}omronCustomer/save`, params),
     //详情
     omronCustomerInfo: params => Axios.post(`${environment.basicAPI}omronCustomer/info`, params),
     //删除
     omronCustomerDel: params => Axios.post(`${environment.basicAPI}omronCustomer/del`, params),

     //绑定欧姆龙客户
     omronCustomerBind: params => Axios.post(`${environment.basicAPI}omronCustomer/bingDingOmronCustomerId`, params),
     //绑定欧姆龙客户
     getOmronCustomer: params => Axios.post(`${environment.basicAPI}omronCustomer/omronCustomerList`, params),

     /**特价申请单 specialApplication */
    //列表
    specialApplicationList: params => Axios.post(`${environment.basicAPI}jqOmronCustomerSpecialprice/list`, params),
     //保存
     specialApplicationSave: params => Axios.post(`${environment.basicAPI}jqOmronCustomerSpecialprice/save`, params),
    //详情
    specialApplicationInfo: params => Axios.post(`${environment.basicAPI}jqOmronCustomerSpecialprice/info`, params),
    //删除
    specialApplicationDel: params => Axios.post(`${environment.basicAPI}jqOmronCustomerSpecialprice/del`, params),
     //调入项目申请单
     specialApplicationCallInProject: params => Axios.post(`${environment.basicAPI}jqOmronCustomerSpecialprice/callInProject`, params),
     //调入项目申请单明细
     specialApplicationCallInProjectDetail: params => Axios.post(`${environment.basicAPI}jqOmronCustomerSpecialprice/callInProjectDetail`, params),
     
    
}

     