import API from "@/api/internalSystem/videoManage/trainingFile/index.js";
import environment from "@/api/environment";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import { mapGetters } from "vuex";
import ossApi from "@/api/aliyun/oss.js";
import { sortTable } from "@/common/global/vxeTableDrag.js";
export default {
  name: "customerList",
  components: {
    TableView,
    Pagination,
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      tableData: [],
      fileList: [],
      environment: environment,
      files: {},
      training_video_id:null,
      sortable:""
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "附件列表",
    },
    fileType: {
      type: String,
    },
    fileId: {
      type: Number,
    },
  },
  mounted() {
    this.columnDrop();
  },
  beforeDestroy() {
    if (this.sortable) {
      this.sortable.destroy();
    }
  },
  methods: {
    columnDrop() {
      this.$nextTick(() => {
        let xTable = this.$refs.xTable;
        this.sortable = sortTable(xTable);
      });
    },
    ossFile(file, index) {
      const loading = this.$loading({
        lock: true,
        text: "正在上传...",
        background: "rgba(0, 0, 0, 0.7)",
        spinner: 'el-icon-loading',
      });
      ossApi
        .uploadFile(file.file)
        .then((res) => {
          let fileObj = {};
          fileObj.file_name = file.file.name;
          fileObj.file_url = res.url;
          fileObj.file_size = file.file.size;
          fileObj.file_suffix = file.file.name.substr(
            file.file.name.lastIndexOf(".") + 1
          );
          // this.fileList.push(fileObj);
          // this.$forceUpdate();
          this.saveFile(fileObj, loading);
        })
        .catch(() => {});
    },
    show(training_video_id) {
      if (!training_video_id) {
        return;
      }
      this.training_video_id = training_video_id;
      this.getList(training_video_id);
      this.dialogVisible = true;
    },
    getList(training_video_id) {
      API.query({ fk_training_video_id: training_video_id }).then((res) => {
        this.fileList = res.data;
      });
    },
    removeFile(row, index) {
      if (row.enable_flag === 1) {
        this.error("启用状态无法删除，请先禁用");
        return;
      }
      this.$confirm("此操作将删除该视频, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          API.remove({
            training_file_id: row.training_file_id,
            fk_training_video_id: row.fk_training_video_id,
          })
            .then(() => {
              this.getList(this.training_video_id);
            })
            .catch(() => {})
            .finally(() => {});
        })
        .catch(() => {});
    },
    saveFile(fileObj, loading) {
      if (!fileObj.file_url) {
        this.error("请先上传附件");
        return;
      }
      // if (!this.form || !this.form.file_type) {
      //   this.error("附件类型不能为空");
      //   return
      // }
      if (!this.training_video_id) {
        this.error("视频id不能为null");
        return;
      }
      let list = [];
      list.push(fileObj);
      API.add({
        list: list,
        file_type: 2,
        fk_training_video_id: this.training_video_id,
      })
        .then((res) => {
          this.getList(this.training_video_id);
          loading.close();
        })
        .catch(() => {
          loading.close();
        });
    },

    //下载
    downloadFile(row) {
      if (row.file_url) {
        if (row.file_suffix === "txt") {
          window.location.href =
            row.file_url + "?response-content-type=application/octet-stream";
        } else {
          window.location.href = row.file_url;
        }
      }
    },
    dialogCancel() {
      this.fileList = [];
      this.dialogVisible = false;
      this.$emit("getList");
    },
    changeEnableFlag(row, index) {
      API.update({
        enable_flag: row.enable_flag === 1 ? 2 : 1,
        training_file_id: row.training_file_id,
      }).then((res) => {
        this.getList(this.training_video_id);
      });
    },
    sortChange({ column, property, order }){
      if(this.isOrder){
        this.$emit("sortChange", { column, prop:property, order });
      }
    },
  },
  computed: {
    ...mapGetters(["buttonPermissions"]),
  },
};
