import {mapGetters} from "vuex"
import API from '@/api/internalSystem/basicManage/salesUnit'
import provinces from '@/components/provinces'

export default {
	data() {
		return {
			form: {
				sales_unit_id: 0,
				company_name: ``,
				company_tax_number: ``,
				open_bill_area: ``,
				open_bill_address: ``,
				fax: ``,
				postal_code: ``,
				operate_city: ``,
				operate_area: ``,
				operate_address: ``,
				open_bill_phone: ``,
				company_legal_person: ``,
				operate_province: ``,
				open_bill_city: ``,
				open_bill_province: ``,
				bank_account: ``,
				opening_bank: ``,
				company_short_name: ``
			},
			loading: false,
			dialogVisible: false,
			title: `新增销售单位`
		}
	},
	methods: {
		submit() {
			this.loading = true
			this.form.open_bill_province = this.$refs.invoiceProvinces.GetProvince()
			this.form.open_bill_city = this.$refs.invoiceProvinces.GetCity()
			
			this.form.operate_province = this.$refs.businessProvinces.GetProvince()
			this.form.operate_city = this.$refs.businessProvinces.GetCity()
			
			if (this.form.sales_unit_id) {
				API.update(this.form).then(() => {
					this.$emit('success')
					this.dialogVisible = false
				}).catch(() => {
				}).finally(() => {
					this.loading = false
				})
				return
			}
			API.add(this.form).then(() => {
				this.$emit('success')
				this.dialogVisible = false
			}).catch(() => {
			}).finally(() => {
				this.loading = false
			})
		},
		show(form = null) {
			Object.assign(this.$data, this.$options.data())
			this.dialogVisible = true
			if (!form) return
			Object.assign(this.form, form)
			this.title = `编辑销售单位`
			this.$nextTick(() => {
				this.$refs.invoiceProvinces.SetProvince(form.open_bill_province)
				this.$refs.invoiceProvinces.SetCity(form.open_bill_city)
				
				this.$refs.businessProvinces.SetProvince(form.operate_province)
				this.$refs.businessProvinces.SetCity(form.operate_city)
			})
		}
	},
	components: {
		provinces
	},
	computed: {
		...mapGetters([])
	}
}
