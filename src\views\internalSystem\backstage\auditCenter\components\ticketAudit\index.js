import API from '@/api/internalSystem/salesManage/openTicket'
import brandAPI from '@/api/internalSystem/basicManage/brand'
import TableCustom from "@/views/internalSystem/backstage/components/tableCustom/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  getOptions
} from "@/common/internalSystem/common.js"
import {
  mapGetters
} from 'vuex'
export default {
  name: "openTicketAudit",
  components: {
    TableCustom,
    MyDate
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        ticket_no: "",
        ticket_date: "",
        ticket_remark: "",
        fk_sale_employee_id: "",
        fk_sale_employee_name: "",
        sell_type: "",
        customer_name: "",
        customer_no: "",
        customer_tax_number: "",
        customer_account: "",
        invoice_number: "",
        express_number: "",
        open_ticket_address: "",
        open_account_bank: "",
        open_ticket_phone: "",
        receive_ticket_address: "",
        receive_ticket_person: "",
        receive_ticket_phone: "",
        fk_sales_unit_id: "",
        sales_unit_id_format: "",
        update_user_name: ""
      },
      softwareVersionList: [], //软件版本
      measurementUnitList: [], //计量单位
      loading: false,
      proList: [], //产品列表
      tableCol: [{
        label: "id",
        prop: "contract_id",
        isHide: true
      }, {
        label: "合同单号",
        prop: "contract_no",
        width: 160
      }, {
        label: "产品",
        prop: "brand_id",
        width: 160
      }, {
        label: "开票名称",
        prop: "open_ticket_name",
        width: 160
      }, {
        label: "开票单价(元)",
        prop: "open_ticket_unit",
        width: 160,
        need: true,
        fn: "f1"
      }, {
        label: "发票税率(%)",
        prop: "invoice_tax_rate",
        width: 160
      }, {
        label: "发票数量",
        prop: "open_ticket_number",
        width: 160,
        need: true,
        fn: "f2"
      }, {
        label: "扣税开票单价(元)",
        prop: "buckle_open_ticket_unit",
        width: 160
      }, {
        label: "税额(元)",
        prop: "tax",
        width: 160
      }, {
        label: "未税开票金额(元)",
        prop: "open_ticket_money",
        width: 160
      }, {
        label: "价税合计(元)",
        prop: "leved_total",
        width: 160
      }, 
      // {
      //   label: "软件版本",
      //   prop: "software_version",
      //   width: 160
      // }, 
      {
        label: "计量单位",
        prop: "measurement_unit",
        width: 160
      }, 
      // {
      //   label: "开票型号",
      //   prop: "open_ticket_model",
      //   width: 160
      // },
       {
        label: "软件序列号",
        prop: "software_no",
        width: 160
      }],
      obj: {},
      dialogVisibleAudit: false,
      auditForm: {
        auditState:1,
        invoice_number: "",
        express_number: "",
        auditRemark: ""
      },
      rules: {
        auditState: [{
          required: true,
          message: "请选择审核状态",
          trigger: "change"
        }],
        invoice_number: [{
          required: true,
          message: "请填写发票号码",
          trigger: "blur"
        }],
        express_number: [{
          required: true,
          message: "请填写快递单号",
          trigger: "blur"
        }]
      },
      auditStateList: [],
      totalOpenMoney:0
    };
  },
  methods: {
    async Show(data = null) {
      this.proList = [];
      this.softwareVersionList = [];
      this.measurementUnitList = [];
      this.dialogVisible = true;
      // this.softwareVersionList = getOptions('t_contract_detail', 'software_version');
      this.measurementUnitList = getOptions('t_contract_detail', 'measurement_unit');
      this.auditStateList = getOptions('t_open_ticket', 'audit_state');
      this.softwareVersionList.forEach(item => {
        item.label = item.sysName;
        item.value = item.sysValue;
      });
      this.measurementUnitList.forEach(item => {
        item.label = item.sysName;
        item.value = item.sysValue;
      });
      await this.getBrand();
      this.obj = {
        contract_id: {
          value: "",
          type: "input"
        },
        contract_no: {
          value: "",
          type: "input",
          disabled: true
        },
        brand_id: {
          value: "",
          type: "select",
          option: this.proList,
          disabled: true
        },
        open_ticket_name: {
          value: "",
          type: "select",
          option: this.proList,
          disabled: true
        },
        open_ticket_unit: {
          value: "",
          type: "float"
        },
        invoice_tax_rate: {
          value: "",
          type: "input",
          disabled: true
        },
        open_ticket_number: {
          value: 1,
          type: "number"
        },
        buckle_open_ticket_unit: {
          value: "",
          type: "float",
          disabled: true
        },
        tax: {
          value: "",
          type: "float",
          disabled: true
        },
        open_ticket_money: {
          value: "",
          type: "float",
          disabled: true
        },
        leved_total: {
          value: "",
          type: "float",
          disabled: true
        },
        // software_version: {
        //   value: "",
        //   type: "select",
        //   option: this.software_version,
        //   disabled: true
        // },
        measurement_unit: {
          value: "",
          type: "select",
          option: this.measurement_unit,
          disabled: true
        },
        // open_ticket_model: {
        //   value: "",
        //   type: "select",
        //   option: this.software_version,
        //   disabled: true
        // },
        software_no: {
          value: "",
          type: "input",
          disabled: true
        }
      }
      if (data) {
        this.ruleForm = data;
        API.detailList({
            ticket_id: data.ticket_id
          })
          .then(res => {
            res.data.map(item => {
              let pro = JSON.parse(JSON.stringify(this.obj))
              for (let v in item) {
                if (pro[v]) {
                  pro[v].value = item[v];
                  pro[v].disabled = true;
                }
              }
              this.$refs.tableCustom.add2(pro);
            })

          })
          .catch(() => {}).finally(() => {

          });
          API.getTotalOpenMoney({ fk_customer_id: data.customer_id, sales_unit_id: data.sales_unit_id }).then((res) => {
            if(res.data){
              this.totalOpenMoney = res.data;
            }else{
              this.totalOpenMoney = 0
            }
            this.totalOpenMoney = this.totalOpenMoney.toFixed(2)
    
          })
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
    },
    save() {
      let params = this.auditForm;
      params.ticket_id = this.ruleForm.ticket_id;
      this.loading = true;
      API.updateAudit(params)
        .then(() => {
          this.dialogCancelAudit();
          this.dialogCancel();
        })
        .catch(() => {}).finally(() => {
          this.loading = false;
        })
    },
    getBrand() {
      return new Promise((resolve, reject) => {
				brandAPI.query()
						.then(data => {
							data.data.forEach(item => {
								this.proList.push({
									label: item.brand_type,
									value: item.brand_id
								})
							});
							resolve(1)
						}).catch(() => {
					reject()
				});
			})
    },
    openAudit() {
      this.dialogVisibleAudit = true;
    },
    dialogCancelAudit() {
      this.dialogVisibleAudit = false;
      this.resetForm('auditForm');
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        ticket_no: "",
        ticket_date: "",
        ticket_remark: "",
        fk_sale_employee_id: "",
        fk_sale_employee_name: "",
        sell_type: "",
        customer_name: "",
        customer_no: "",
        customer_tax_number: "",
        customer_account: "",
        invoice_number: "",
        express_number: "",
        open_ticket_address: "",
        open_account_bank: "",
        open_ticket_phone: "",
        receive_ticket_address: "",
        receive_ticket_person: "",
        receive_ticket_phone: "",
        fk_sales_unit_id: "",
        sales_unit_id_format: "",
        update_user_name: ""
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'buttonPermissions','software_version','measurement_unit',"contract_auditStateList"])
  },
};