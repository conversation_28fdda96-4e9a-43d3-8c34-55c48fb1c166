var creatUpload = function (serverUrl, catalog) {
  var ossUpload = {};
  ossUpload.accessid = '';
  ossUpload.accesskey = '';
  ossUpload.host = '';
  ossUpload.policyBase64 = '';
  ossUpload.signature = '';
  ossUpload.callbackbody = '';
  ossUpload.filename = '';
  ossUpload.key = '';
  ossUpload.expire = 0;
  ossUpload.g_object_name = '';
  ossUpload.autograph = false;
  // g_object_name_type = 'random_name';
  ossUpload.now = ossUpload.timestamp = Date.parse(new Date()) / 1000;

  //从后端获取oss认证信息
  ossUpload.send_request = function () {
    var xmlhttp = null;
    if (window.XMLHttpRequest) {
      xmlhttp = new XMLHttpRequest();
    } else if (window.ActiveXObject) {
      xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
    }

    if (xmlhttp != null) {
      // serverUrl是 用户获取 '签名和Policy' 等信息的应用服务器的URL，请将下面的IP和Port配置为您自己的真实信息。
      xmlhttp.open("GET", serverUrl, false);
      xmlhttp.send(null);
      return xmlhttp.responseText
    } else {
    }
  };

  ossUpload.get_signature = function (callback) {
    // 可以判断当前expire是否超过了当前时间， 如果超过了当前时间， 就重新取一下，3s 作为缓冲。
    this.now = this.timestamp = Date.parse(new Date()) / 1000;
    if (this.expire < this.now + 3) {
      var body = this.send_request();
      var obj = eval("(" + body + ")");
      this.host = obj['host'];
      this.policyBase64 = obj['policy'];
      this.accessid = obj['accessid'];
      this.signature = obj['signature'];
      this.expire = parseInt(obj['expire']);
      this.callbackbody = obj['callback'];
      this.key = obj['dir'];
      if (this.host) { // 有值，获取签名成功
        this.autograph = true;
        callback();
      } else {
        this.$message('获取签名失败，请重新选择图片')
      }
      return true;
    }
    return false;
  };

  ossUpload.random_string = function (len) {
    len = len || 32;
    var chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
    var maxPos = chars.length;
    var pwd = '';
    for (var i = 0; i < len; i++) {
      pwd += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
  };

  ossUpload.get_suffix = function (filename) {
    var pos = filename.lastIndexOf('.');
    var suffix = '';
    if (pos != -1) {
      suffix = filename.substring(pos)
    }
    return suffix;
  };

  ossUpload.calculate_object_name = function (filename) {
    this.suffix = this.get_suffix(filename);
    this.g_object_name = this.key + this.random_string(10) + this.suffix;
    return ''
  };

  ossUpload.uploadOss = function (filename, file) {
    // 此项目需要分离底下两个操作，所以这里先注释
    //this.get_signature() //获取后端必要数据
    this.calculate_object_name(filename) //设置随机文件名
    //组装发送数据
    var request = new FormData();
    request.append("OSSAccessKeyId", this.accessid); //Bucket 拥有者的Access Key Id。
    request.append("policy", this.policyBase64); //policy规定了请求的表单域的合法性
    request.append("Signature", this.signature); //根据Access Key Secret和policy计算的签名信息，OSS验证该签名信息从而验证该Post请求的合法性
    //---以上都是阿里的认证策略
    request.append("key", catalog + this.g_object_name); //文件名字，可设置路径
    request.append("success_action_status", '200'); // 让服务端返回200,不然，默认会返回204
    request.append('file', file); //需要上传的文件 file
    request.append("callback", this.callbackbody); //回调，非必选，可以在policy中自定义
    return request
  };

  return ossUpload
};


export default creatUpload
