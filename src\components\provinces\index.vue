<template>
	<div>
		<el-col class="is-flex" :span="interval">
			<label>
				<span v-if="provinceMandatory" class="required">*</span>
				{{provinceTitle}}:
			</label>
			<el-select
					size="small"
					@change="changeProvince"
					v-model="province"
					:placeholder="`请选择${provinceTitle}`"
					class="all"
					filterable
					clearable>
				<el-option
					v-for="(item, index) in provinceList"
					:key="index"
					:label="item.origin_place"
					:value="item.province"
				/>
			</el-select>
		</el-col>
		<el-col class="is-flex" :span="interval">
			<label>
				<span v-if="cityMandatory" class="required">*</span>
				{{cityTitle}}:
			</label>
			<el-select
					size="small"
					v-model="city"
					:disabled="!province"
					:placeholder="`请选择${cityTitle}`"
					class="all"
					filterable
					clearable>
				<el-option
					v-for="(item, index) in cityList"
					:key="index"
					v-show="item.province === province"
					:label="item.origin_place"
					:value="item.city"
				/>
			</el-select>
		</el-col>
	</div>
</template>

<script>
	import {mapGetters} from "vuex"
	
	export default {
		props: {
			interval: {
				default: 12
			},
			provinceMandatory: { // 选择省份是否必填的符号
				default: true
			},
			provinceTitle: { // 选择省份的标题
				default: `省份`
			},
			cityMandatory: { // 选择城市是否必填的符号
				default: true
			},
			cityTitle: { // 选择城市的标题
				default: `城市`
			},
		},
		data() {
			return {
				province: ``, // 选择的省份
				city: `` // 选择的城市
			}
		},
		methods: {
			GetProvince () {
				return this.province
			},
			SetProvince (province) {
				this.province = province
			},
			GetCity () {
				return this.city
			},
			SetCity (city) {
				this.city = city
			},
			changeProvince() {
				this.city = ``
			}
		},
		computed: {
			...mapGetters([
				'provinceList',
				'cityList',
			])
		}
	}
</script>
