import API from '@/api/internalSystem/customerManage/customerIntention'
import {mapGetters} from 'vuex'
export default {
  name: "intentionAudit",
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_name: "",
        link_man: "",
        phone: "",
        customer_stage: "",
        customer_source: "",
        customer_type: "",
        brand: "",
        province: "",
        city: "",
        belong_industry: "",
        contact_time: "",
        is_software: "1",
        quotation_scheme: "",
        follow_records: "",
        software_demand: "",
        use_describe: ""
      },
      loading: false,
      dialogVisibleAudit: false,
      auditForm: {
        auditState: 1,
        auditRemark: ""
      },
      rules: {
        auditState: [{
          required: true,
          message: "请选择审核状态",
          trigger: "change"
        }]
      },
    };
  },
  methods: {
    Show(data = null) {
      if (!data) {
        return;
      }
      this.dialogVisible = true;
      this.ruleForm = data;
    },
    openAudit() {
      this.dialogVisibleAudit = true;
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancelAudit() {
      this.dialogVisibleAudit = false;
      this.resetForm('auditForm');
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
    },
    save() {
      let params = this.auditForm;
      params.customer_intention_id = this.ruleForm.customer_intention_id;
      this.loading = true;
      API.updateAudit(params)
        .then(() => {
          this.dialogCancelAudit();
          this.dialogCancel();
        })
        .catch(() => {}).finally(() => {
          this.loading = false;
        });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_name: "",
        link_man: "",
        phone: "",
        customer_stage: "",
        customer_source: "",
        customer_type: "",
        brand: "",
        province: "",
        city: "",
        belong_industry: "",
        contact_time: "",
        is_software: "1",
        quotation_scheme: "",
        follow_records: "",
        software_demand: "",
        use_describe: ""
      }
    }
  },
  computed: {
    ...mapGetters([
      'params_constant_customer_stage',
      'params_constant_customer_type',
      'params_constant_belong_industry',
      'params_constant_customer_source',
      'contract_auditStateList'
    ])
  },
};