<template>
  <div class="treeContainer">
    <div class="head">
      <!-- <el-button type="text" size="large" @click="addJob()">添加角色</el-button> -->
      <el-input
        placeholder="输入关键字进行过滤"
        style="margin-top: 50px"
        v-model="filterText"
      />
    </div>
    <div class="main-tree">
      <el-tree
        class="filter-tree"
        :data="jobTreeList"
        node-key="brand_id"
        highlight-current
        :expand-on-click-node="false"
        :default-checked-keys="checkedKey"
        :props="defaultProps"
        default-expand-all
        :filter-node-method="filterNode"
        ref="tree"
        @node-click="setJob"
      >
        <span class="custom-tree-node" slot-scope="{ node }">
          <span>{{ node.label }}</span>
          <!-- <span>
            <el-button type="text" class="primary" size="mini" @click="editJob(data)">
              编辑
            </el-button>
            <el-button type="text" class="danger" size="mini" @click="delJob(data)">
              删除
            </el-button>
          </span> -->
        </span>
      </el-tree>
    </div>

    <AddJob ref="addJob" @getJobTree="getJobTree" />
  </div>
</template>

<script src="./index.js">
</script>

<style lang="scss" scoped>
@import "../index.scss";
</style>