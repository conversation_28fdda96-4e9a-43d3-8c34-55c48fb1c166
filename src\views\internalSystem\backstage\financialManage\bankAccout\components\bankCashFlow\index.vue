<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="660px"
    :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
      <el-form-item label="转出银行" prop="out_bank_accout_id">
        <el-select v-model="ruleForm.out_bank_accout_id" placeholder="请选择转出银行" filterable clearable>
          <el-option v-for="item in bankList" :key="item.financial_bank_accout_id" :label="item.bank_name"
            :value="item.financial_bank_accout_id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="转入银行" prop="into_bank_accout_id">
        <el-select v-model="ruleForm.into_bank_accout_id" placeholder="请选择转入银行" filterable clearable>
          <el-option v-for="item in bankList" :key="item.financial_bank_accout_id" :label="item.bank_name"
            :value="item.financial_bank_accout_id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="转账金额" prop="money_amount">
        <el-input v-model="ruleForm.money_amount"  clearable></el-input>
      </el-form-item>
      <el-form-item label="转账备注" prop="money_remark">
        <el-input v-model="ruleForm.money_remark" type="textarea" clearable></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm('ruleForm')">保 存</el-button>
      <el-button @click="dialogCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script src="./index.js">

</script>