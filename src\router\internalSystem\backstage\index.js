import _ from 'lodash'
import statisticsPage from './statisticsPage/index'
import basicInfo from "./basicInfo/index.js"
import customerManage from "./customerManage/index.js"
import salesManage from "./salesManage/index.js"
import financialManage from "./financialManage/index.js"
import basicManage from "./basicManage/index.js"
import bugManage from "./bugManage/index.js"
import contractManage from "./contractManage/index.js"
export default [{
    path: '/backstage',
    name: 'backstage',
    component: resolve => require(['@/views/internalSystem/backstage/index.vue'], resolve),
    redirect: {
        path: '/backstage/statistics/statisticsPage'
    },
    children: _.concat(
        statisticsPage, basicInfo, customerManage, salesManage, financialManage, basicManage,bugManage,contractManage
    )
}]