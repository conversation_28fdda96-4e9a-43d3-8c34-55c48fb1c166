import Axios from "@/api";
import environment from "@/api/environment";

export default {
    //保存
    //add、update、remove、query
    add: params => Axios.post(`${environment.mesosphereAPI}template/list/add`, params),
    //删除
    update: params => Axios.post(`${environment.mesosphereAPI}template/list/update`, params),
    //修改
    remove: params => Axios.post(`${environment.mesosphereAPI}template/list/remove`, params),
    //查询
    query: params => Axios.post(`${environment.mesosphereAPI}template/list/query`, params),
}