1.  插入新行，参数：records：需要插入的数据，第二个参数：插入的位置，-1 为最后一行。如果不想填充可以直接 insert（）；

```
insertEvent() { //表格新增行
    let records = { //新增行时需要填的数据，可以为空。
    name: "",
    role: ""
  }
  this.$refs.xTable1.insertAt(records, -1);
},
```

2. 删除选中行

```
this.$refs.xTable1.removeSelecteds(); ###获取选中行的数据
this.$refs.xTable1.getSelectRecords();
```

3. 获取当前行的数据

```
this.$refs.xTable1.getCurrentRow(); ##获取表格中所有的数据
this.\$refs.xTable1.getTableData().fullData;
```

4. 隐藏指定列

```
customColumns: [ //隐藏指定列，需要在 vxe-table 添加      :customs.sync="customColumns"
    {
      field: 'customerId',
      visible: false
    }
]
```

5. 禁止编辑

```
 //需要在 vxe-table 加 以下代码
:edit-config="{
  trigger: 'click',
  mode: 'cell',
  activeMethod: activeRowMethod,
  showStatus: false
}"
@edit-disabled="editDisabledEvent"
```

//如果修改状态 modifyState 为真说明可以修改，否则给提示，点击修改按钮修改

```
activeRowMethod({ rowIndex }) {
if (!this.modifyState) {
return rowIndex == 99999 //只有 rowIndex 为 99999 时，那一行才可以编辑
} else {
return rowIndex !== 99999 //只要 rowIndex 不为 99999 时，那一行都能编辑
}
},
editDisabledEvent() {
    //触发不能编辑时的回调函数
    this.\$message({type: 'info',message: '现在处于查看单据状态，如需修改请点击修改按钮!'});
},
```

6. 删除选中状态

```
this.\$refs.xTable.clearSelection();
```

7. 获取表格中的每一列序号

```
<vxe-table-column
    type="seq"
    field="num"
    title="序号"
    width="60"
    :index-method="indexMethod"
    > </vxe-table-column>
//在函数方法中加
indexMethod ({ rowIndex ,row}) {
> row.num=rowIndex+1;
> return rowIndex+1
> },
```

8. 表格效验

```
##:edit-rules="validRules"
validRules: {
    name: [
    { required: true, message: '联系人未填写' },
    ],
    telphone: [
    { required: true, message: '电话未填写' },
    ],
    address: [
    { required: true, message: '联系地址未填写' },
    ],
    linkType: [
    { required: true, message: '类型未选择' },
    ],
    isMain: [
    { required: true, message: '主联系人未选择' },
    ],
},

fullValidEvent() {
    let data = this.$refs.addCustomer.getTableData().fullData;
          if (data.length == 0) {
          this.$message("联系人未添加")
    return false;
    }
    this.\$refs.addCustomer.fullValidate((valid, errMap) => {
    if (valid) {
    this.save();
    } else {
    let msgList = []
    Object.values(errMap).forEach(errList => {
    errList.forEach(params => {
    let { rowIndex, column, rules } = params
    rules.forEach(rule => {
    msgList.push(`第 ${rowIndex + 1} 行 ${column.title} 校验错误：${rule.message}`)
    })
    })
    });
    this.\$XMsg.message({
    status: 'error',
    message: () => {
    return [
            <div class="red" style="max-height: 400px;overflow: auto;">
              {
                msgList.map(msg => {
                return <div>{msg}</div>
                })
              }
            </div>
        ]
      }
  })
  }
  })
},
```
