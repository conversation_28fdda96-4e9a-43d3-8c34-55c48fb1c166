<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small">
      <el-form-item label="名称">
        <el-input v-model="formSearch.name" placeholder="请输入部门名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="add"
          v-permit="'ADD_DEPARTMENT_NEW'">添加</el-button>
      </el-form-item>
    </el-form>
    <table-view :tableList="tableList" :tableData="tableData" :isSel="false"
      :isEdit="'UPDATE_DEPARTMENT_NEW'"
      :isDel="'DEL_DEPARTMENT_NEW'" @modify="modify"
      @del="del"></table-view>
    <Pagination ref="pagination" @success="getList" />
    <el-dialog :title="dialogTitle[titleMap]" :visible.sync="dialogAdd" width="40%" @close="dialogAddClose">
      <el-form ref="form" :model="form" label-width="100px">
        <el-form-item label="部门" style="width: 100%">
          <el-input v-model="form.departmentName"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogAdd = false">取 消</el-button>
        <el-button size="small" type="primary" @click="dialogAddPost">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script src="./index.js"></script>