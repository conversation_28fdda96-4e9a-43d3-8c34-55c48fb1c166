import API from '@/api/internalSystem/common/index.js'
export default {
  name: "addDataDictionary",
  data() {
    return {
      dialogTitle: "新增数据字典",
      dialogVisible: false,
      ruleForm: {
        tbName: "",
        tbCode: "",
        sysName: "",
        sysValue: "",
        sort: "",
        remark: ""
      },
      rules: {
        tbName: [{
          required: true,
          message: "请输入表名",
          trigger: "blur"
        }],
        tbCode: [{
          required: true,
          message: "请输入表字段",
          trigger: "blur"
        }],
        sysName: [{
          required: true,
          message: "请输入字段名称",
          trigger: "blur"
        }],
        sysValue: [{
          required: true,
          message: "请输入字段值",
          trigger: "blur"
        }],
        sort: [{
          required: true,
          message: "请输入排序",
          trigger: "blur"
        }],
        remark: [{
          required: true,
          message: "请输入备注",
          trigger: "blur"
        }]
      }
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      if (!data) {
        this.dialogTitle = "新增数据字典";
      } else {

        this.dialogTitle = "修改数据字典";
        this.ruleForm = data;
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    save() {
      let params = this.ruleForm;
      if (params.id) {
        API.updateDataDictionary(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      } else {
        API.addDataDictionary(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        tbName: "",
        tbCode: "",
        sysName: "",
        sysValue: "",
        sort: "",
        remark: ""
      }
    }
  }
};