import API from "@/api/internalSystem/financialManage/receivables";
import API2 from "@/api/internalSystem/salesManage/contract";
import SalesUnitList from "@/views/internalSystem/backstage/components/salesUnitList/index.vue";
import ContractList from "@/views/internalSystem/backstage/components/contractList/index.vue";
import BankList from "@/views/internalSystem/backstage/components/bankList/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import TableView from "./../../newTableView/index.vue";
import { getOptions, dateFormat } from "@/common/internalSystem/common.js";
import { cloneDeep, concat, take } from "lodash";
import { numAdd } from "@/utils/calculate.js";
import { mapGetters } from "vuex";
export default {
  name: "addReceivables",
  components: {
    SalesUnitList,
    ContractList,
    BankList,
    MyDate,
    TableView,
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        project: "",
        contract_no: "",
        fk_customer_contract_id: "",
        fk_sell_employee_id: "",
        fk_sell_department_id: "",
        fk_sell_employee_name: "",
        fk_sell_department_name: "",
        fk_financial_bank_accout_id: 4, //4
        bank_name: "中国建设银行股份有限公司福州城南支行", //中国建设银行股份有限公司福州城南支行
        customer_no: "",
        customer_name: "",
        settlement_method: 102, //102
        amount: "",
        sales_unit_id_format: "",
        fk_sales_unit_id: "",
        maintain_stop_time: "",
        voucher_num: "",
        sell_type: "",
        receivables_remarks: "",
        confirm_money_remarks: "",
        not_receivables_amount: "",
        hasAmount: 0,
        customer_id: "",
      },
      rules: {
        // contract_no: [{
        // 	required: true,
        // 	message: "请选择收款合同"
        // }],
        // sales_unit_id_format: [{
        // 	required: true,
        // 	message: "请选择销货单位"
        // }],
        bank_name: [
          {
            required: true,
            message: "请选择收款银行",
          },
        ],
        // project: [{
        // 	required: true,
        // 	message: "请选择收款项目",
        // 	trigger: "change"
        // }],
        // settlement_method: [
        //   {
        //     required: true,
        //     message: "请选择结算方式",
        //     trigger: "change",
        //   },
        // ],
        // amount: [{
        // 	required: true,
        // 	message: "请输入收款金额",
        // 	trigger: "blur"
        // }],
        // confirm_money_time: [{
        // 	required: true,
        // 	message: "请选择到款日期",
        // 	trigger: "blur"
        // }]
      },
      projectList: [], //收款项目
      settlementMethodList: [], //结算方式
      sellTypeList: [], //销售类型
      loading: false,
      tableData: [],
      contract_detail_ids: [],
      tableList: [
        {
          // width: 150,
          name: "客户名称",
          value: "customer_name",
          layout: "01",
        },
        {
          // width: 150,
          name: "合同单号",
          value: "contract_no",
          layout: "01",
        },
        // {
        //   width: 100,
        //   name: "已出库金额",
        //   value: "outbound_amount",
        //   layout: "03",
        // },
        {
          width: 100,
          name: "已收款金额",
          value: "receivables_amount",
          layout: "03",
        },
        {
          width: 100,
          name: "未收款金额",
          value: "not_receivables_amount",
          layout: "03",
        },
        // {
        // 	width: 150,
        // 	name: "销售类型",
        // 	value: "sell_type",
        // 	layout: '01'
        // },
        {
          width: 150,
          name: "销售员",
          value: "fk_sell_employee_name",
          layout: "02",
        },
        {
          width: 150,
          name: "到期日期",
          value: "maintain_stop_time",
          layout: "02",
        },
      ],
      receivablesMap: {},
      sellMap: {
        1: "软件销售",
        2: "二次开发",
        3: "软件服务费",
        4: "增加端口",
        5: "软件租用",
        6: "软件升级",
        7: "二次销售",
        8: "赠送端口",
        9: "金万维",
        10: "项目款",
        11: "网站",
        12: "硬件销售",
      },
      isDel: true,
    };
  },
  mounted: {},
  methods: {
    async Show(data = null) {
      this.receivablesMap = {};
      //封装收款类型
      this.receivables_project.forEach((element) => {
        this.receivablesMap[element.label] = element.value;
      });
      this.dialogVisible = true;
      // this.projectList = getOptions('t_receivables', 'project');
      // this.settlementMethodList = getOptions('t_receivables', 'settlement_method');
      // this.sellTypeList = getOptions('t_receivables', 'sell_type');
      this.isDel = true;
      if (data) {
        this.ruleForm = data;
        this.ruleForm.add_user_depot = data.fk_sell_department_name;
        this.tableData = concat(this.tableData, await this.getDetailList(data));

        // this.tableData.project = data.project //收款项目
        this.isDel = false;
      }
    },
    //根据  查询相同的记录
    async getDetailList(row) {
      let { data } = await API.getDetailList({
        receivables_id: row.receivables_id,
      });

      // 确保详情数据包含技术员字段
      data.forEach(item => {
        item.bear_employee_id = item.bear_employee_id || '';
        // 如果有子项，也需要处理技术员字段
        if (item.children && Array.isArray(item.children)) {
          item.children.forEach(child => {
            child.bear_employee_id = child.bear_employee_id || '';
          });
        }
      });

      return data;
    },
    //提交
    submitForm(formName) {
      if(this.ruleForm.data_state ===2){
        return this.error('该记录是旧数据，请上旧系统继续操作')
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel(flag = true) {
      //保存之后，不退出当前页面
      if (flag) {
        this.dialogVisible = false;
        this.$emit("selectData");
      }

      this.resetForm("ruleForm");
      this.clearData();
    },
    save() {
      let params = this.ruleForm;
      if (this.tableData.length === 0) {
        return this.error("请选择收款合同");
      }
      let flag01 = true; //判断收款金额
      let flag02 = true; //判断到账时间
      let flag03 = true; //判断收款项目
      let flag04 = true; //判断收款金额
      // let flag05 = true; //判断收款金额
      this.tableData.forEach((element) => {
        if (!element.project) {
          flag03 = false;
          return;
        }

        if (parseFloat(element.amount) <= 0) {
          flag04 = false;
          return;
        }

        if (
          parseFloat(element.amount) >
          parseFloat(element.not_receivables_amount)
        ) {
          flag01 = false;
          return;
        }
        // if (parseFloat(element.amount) > parseFloat(element.outbound_amount)) {
        //   flag05 = false;
        //   return;
        // }
        if (!element.confirm_money_time) {
          flag02 = false;
          return;
        }

        // 确保技术员字段存在
        if (element.bear_employee_id === undefined) {
          element.bear_employee_id = '';
        }

        // 如果有子项，也要确保技术员字段存在
        if (element.children && Array.isArray(element.children)) {
          element.children.forEach(child => {
            if (child.bear_employee_id === undefined) {
              child.bear_employee_id = '';
            }
          });
        }

        // delete element.project
      });
      if (!flag04) {
        this.error("收款金额不能为空，请检查后保存");
        return;
      }
      // if (!flag05) {
      //   this.error("收款金额不能超过已出库金额，请检查后保存");
      //   return;
      // }
      if (!flag01) {
        this.error("收款金额不能超过未收款金额，请检查后保存");
        return;
      }

      if (!flag02) {
        this.error("到账日期有误，请检查后保存");
        return;
      }
      if (!flag03) {
        this.error("请选择收款项目");
        return;
      }
      this.loading = true;
      params.detailList = this.tableData;

      API.add(params)
        .then(() => {
          if (params.project === 120) {
            let data = {
              customer_id: params.customer_id,
              first: "您的评价会让我们变得更好",
              keyword1: "吉勤软件售后服务满意度调查",
              keyword2: params.fk_sell_employee_name,
            };
            API.satisfaction_push(data)
              .then(() => {})
              .catch(() => {});
          }
          this.dialogCancel(false);
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    back() {
      if(this.ruleForm.data_state ===2){
        return this.error('该记录是旧数据，请上旧系统继续操作')
      }
      //改为批量退回审核
      this.$confirm(`此操作将回退记录, 是否继续?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          for (let i = 0; i < this.tableData.length; i++) {
            let params = {
              receivables_id: this.tableData[i].receivables_id,
              auditState: 3,
            };
            API.updateAudit(params)
              .then(() => {
                this.dialogCancel();
              })
              .catch(() => {})
              .finally(() => {});
          }
        })
    },
    del() {
      if(this.ruleForm.data_state ===2){
        return this.error('该记录是旧数据，请上旧系统继续操作')
      }
      //改为批量退回审核
      this.$confirm(
        `此操作将这${this.tableData.length}条记录删除, 是否继续?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        if (this.ruleForm.check_state == 1)
          return this.error("该单据已审核通过，不允许删除");

        for (let i = 0; i < this.tableData.length; i++) {
          let params = {
            receivables_id: this.tableData[i].receivables_id,
          };
          API.remove(params)
            .then(() => {})
            .catch(() => {});
        }
        this.dialogCancel();
      });
    },
    delDetail(row) {
      let delIndex = "";
      this.tableData.map((item, index) => {
        if (item.customer_contract_id === row.customer_contract_id) {
          delIndex = index;
        }
      });
      this.tableData.splice(delIndex, 1);
      this.contract_detail_ids = this.takeIds(this.tableData);
    },
    //选择销货单位
    chooseSalesUnit() {
      this.$refs.salesUnitList.Show();
    },
    getSalesUnit(info = {}) {
      this.ruleForm.sales_unit_id_format = info.company_name;
      this.ruleForm.fk_sales_unit_id = info.sales_unit_id;
    },
    //选择合同
    chooseContract() {
      if (this.tableData && this.tableData.length > 0) {
        return this.error("只能调入一个合同的明细");
      }
      this.$refs.contractList.Show();
    },
    async getContractInfo(info = []) {
      for (let i = 0; i < info.length; i++) {
        info[i]["confirm_money_time"] = dateFormat("yyyy-MM-dd", new Date());
        info[i]["fk_customer_contract_id"] = info[i]["customer_contract_id"];
        // info[i]["amount"] = info[i]["not_receivables_amount"];
        // info[i]['amount'] =(info[i]['outbound_amount']) - (info[i]['receivables_amount'] || 0)

        info[i]["hasAmount"] = info[i]["receivables_amount"];
        info[i]["customer_id"] = info[i]["fk_customer_id"];
        //根据销售类型id，取销售名字，再取收款类型
        info[i]["project"] = this.receivablesMap[
          this.sellMap[info[i]["sell_type"]]
        ];
        // 初始化技术员字段
        info[i]["bear_employee_id"] = info[i]["bear_employee_id"] || '';
        info[i] = {
          ...info[i],
        };
      }
      let res = await API.findContractDetail({
        customer_contract_id: info[0].customer_contract_id,
      });

      info[0].children = res.data;
      let sumAmount = 0;
      res.data.map((item) => {
        // 为子项也初始化技术员字段
        item["bear_employee_id"] = item["bear_employee_id"] || '';
        sumAmount = numAdd(sumAmount,item['new_money'])
      });

      info[0].amount = sumAmount || 0
      this.tableData = concat(this.tableData, info);
      this.contract_detail_ids = this.takeIds(this.tableData);
    },
    //封装id，判重使用
    takeIds(tableData) {
      return tableData.map((item) => item.customer_contract_id);
    },
    //选择收款银行
    chooseBank() {
      // if (!this.ruleForm.fk_sales_unit_id) return this.error("请先选择合同")
      this.$refs.bankList.Show();
    },
    getBankInfo(info = {}) {
      this.ruleForm.fk_financial_bank_accout_id = info.financial_bank_accout_id;
      this.ruleForm.bank_name = info.bank_name;
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        project: "",
        contract_no: "",
        fk_customer_contract_id: "",
        fk_sell_employee_id: "",
        fk_sell_department_id: "",
        fk_sell_employee_name: "",
        fk_sell_department_name: "",
        fk_financial_bank_accout_id: 4, //4
        bank_name: "中国建设银行股份有限公司福州城南支行", //中国建设银行股份有限公司福州城南支行
        customer_no: "",
        customer_name: "",
        settlement_method: 102, //102
        amount: "",
        sales_unit_id_format: "",
        fk_sales_unit_id: "",
        maintain_stop_time: "",
        voucher_num: "",
        sell_type: "",
        receivables_remarks: "",
        confirm_money_remarks: "",
        not_receivables_amount: "",
        hasAmount: 0,
        customer_id: "",
      };
      this.tableData = [];
      this.contract_detail_ids = [];
    },
    //调入合同明细
    callIn() {
      this.$refs.contractList.Show();
    },
    what(e) {
      this.$forceUpdate();
    },
  },
  computed: {
    ...mapGetters([
      "buttonPermissions",
      "receivables_project",
      "sell_type",
      "receivables_settlement_method",
      "userInfo",
    ]),
    addUserInfo() {
      let name = "";
      if (this.ruleForm.add_user_name && this.ruleForm.add_user_depot) {
        name = this.ruleForm.add_user_name + "-" + this.ruleForm.add_user_depot;
      }
      return name;
    },
  },
};
