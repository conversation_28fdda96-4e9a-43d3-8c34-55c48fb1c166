<template>
  <div class="body-p10">
    <el-form :inline="true" size="small">
      <el-form-item>
        <el-button type="primary" @click="getList" :loading="loading">查询</el-button>
        <el-button type="primary" @click="add"
          v-permit="'ADD_BANKACCOUT_NEW'">添加</el-button>
          <el-button type="primary" @click="flow"
          v-permit="'BANK_CASH_FLOW'">资金流转</el-button>
      </el-form-item>
    </el-form>
    <!-- 新增修改银行账户信息 -->
    <add-bank-accout ref="addBankAccout" @selectData="getList" />
    <!-- 银行流水 -->
    <detail-bank-accout ref="detailBankAccout" @selectData="getList" />
    <!-- 银行资金流转 -->
    <bank-cash-flow ref="bankCashFlow" @selectData="getList" />
    <table-view :tableList="tableList" :tableData="tableData"
      :isEdit="'UPDATE_BANKACCOUT_NEW'"
      :isDel="'DEL_BANKACCOUT_NEW'" 
      :isThrid="'DETAIL_BANKACCOUT_NEW'" 
      @modify="modify" @del="del" thridTitle="银行流水" @thrid="detail" :handleWidth="150"></table-view>
    <Pagination ref="pagination" @success="getList" />
  </div>
</template>

<script src="./index.js"></script>