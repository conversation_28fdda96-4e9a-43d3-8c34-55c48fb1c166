import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: params => {
    return Axios.post(`${environment.internalSystemAPI}receivables/query`, params)
  },
  // 查询收款总金额
  queryAll: params => {
    return Axios.post(`${environment.internalSystemAPI}receivables/queryAll`, params)
  },
  // 新增
  add: params => {
    return Axios.post(`${environment.internalSystemAPI}receivables/add`, params)
  },
  // 删除
  remove: params => {
    return Axios.post(`${environment.internalSystemAPI}receivables/remove`, params)
  },
  // 编辑
  update: params => {
    return Axios.post(`${environment.internalSystemAPI}receivables/update`, params)
  },
  // 获取单条信息
  getInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}receivables/getInfo`, params)
  },
  // 审核
  updateAudit: params => {
    return Axios.post(`${environment.internalSystemAPI}receivables/updateAudit`, params)
  },
  // 根据 
  getDetailList: params => {
    return Axios.post(`${environment.internalSystemAPI}receivables/allDetail`, params)
  },

  // 查询合同明细/收款明细
  findContractDetail: params => {
    return Axios.post(`${environment.internalSystemAPI}receivables/findContractDetail`, params)
  },

  // 推送满意度调查
  satisfaction_push: params => {
    return Axios.post(`${environment.internalSystemAPI}wechat_push/satisfaction_push`, params)
  }

};