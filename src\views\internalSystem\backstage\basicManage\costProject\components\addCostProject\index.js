import API from '@/api/internalSystem/basicManage/costProject'
export default {
  name: "addCostProject",
  data() {
    return {
      dialogTitle: "新增费用项目",
      dialogVisible: false,
      ruleForm: {
        project_no: "",
        project_name: "",
        remark: ""
      },
      rules: {
        project_no: [{
          required: true,
          message: "请输入费用项目编码",
          trigger: "blur"
        }],
        project_name: [{
          required: true,
          message: "请输入费用项目名称",
          trigger: "blur"
        }],
        remark: [{
          required: true,
          message: "请输入项目备注",
          trigger: "blur"
        }]
      }
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      if (!data) {
        this.dialogTitle = "新增费用项目";
      } else {
        this.dialogTitle = "修改费用项目";
        this.ruleForm = data;
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    save() {
      let params = this.ruleForm;
      if (params.financial_cost_project_id) {
        API.update(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      } else {
        API.add(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        project_no: "",
        project_name: "",
        remark: ""
      }
    }
  }
};