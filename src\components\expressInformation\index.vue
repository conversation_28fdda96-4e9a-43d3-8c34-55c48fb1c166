<template>
  <div class="dailogContain">
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @close="Cancel"
      width="900px"
      v-dialogDrag
    >
      <div style="height:500px" class="scroll-container">
        <el-timeline
          v-loading="loading"
          element-loading-text="拼命加载中"
        >
          <el-timeline-item
            placement="top"
            v-for="(activity, index) in activities"
            :key="index"
            :timestamp="activity.date"
          >
             <el-card>
              <p> {{ activity.desc }}</p>
            </el-card>
           
          </el-timeline-item>
        </el-timeline>
      </div>
      <div v-if="!activities.length?true:false">
        当前单号暂无数据
      </div>

      <div style="text-align:center">
        <el-button @click="Cancel">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script src="./index.js"></script>
<style scoped>
.el-dialog__body {
  padding: 30px 50px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
.el-select {
  width: 100%;
}
</style>
