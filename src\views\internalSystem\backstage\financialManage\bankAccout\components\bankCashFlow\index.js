import API from '@/api/internalSystem/financialManage/bankAccout'
export default {
  name: "bankCashFlow",
  data() {
    return {
      dialogTitle: "银行资金流转",
      dialogVisible: false,
      ruleForm: {
        out_bank_accout_id: "",
        into_bank_accout_id: "",
        money_amount: "",
        money_remark: ""
      },
      rules: {
        out_bank_accout_id: [{
          required: true,
          message: "请选择转出银行",
          trigger: "change"
        }],
        into_bank_accout_id: [{
          required: true,
          message: "请选择转入银行",
          trigger: "change"
        }],
        money_amount: [{
          required: true,
          message: "请输入转账金额",
          trigger: "blur"
        }],
        money_remark: [{
          required: true,
          message: "请输入转账备注",
          trigger: "blur"
        }]
      },
      bankList: []
    };
  },
  methods: {
    Show() {
      this.dialogVisible = true;
      API.query()
        .then(data => {
          this.bankList = data.data;
        })
        .catch(() => {});
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    save() {
      let params = this.ruleForm;
        API.addDetail(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {});
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        out_bank_accout_id: "",
        into_bank_accout_id: "",
        money_amount: "",
        money_remark: ""
      }
    }
  }
};