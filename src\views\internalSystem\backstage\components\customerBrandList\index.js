import API from "@/api/internalSystem/salesManage/contract";
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import brandAPI from "@/api/internalSystem/basicManage/brand";

import {
  dateFormat
} from "@/common/internalSystem/common.js"
import {
  map,
  keyBy,
  concat
} from "lodash"

import {
  mapGetters
} from "vuex";
export default {
  name: "customeBrandList",
  components: {
    TableView,
    Pagination
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        customerName: "",
        customerStage: "",
        fk_sale_employee_id: "",
        fk_maintain_employee_id: ""
      },
      tableList: [{
          name: "客户编码",
          value: "customer_no",
        }, {
          name: "客户名称",
          value: "customer_name",
          width:130
        },
        {
          name: "产品服务",
          value: "brand_name",
          width:130
        },
        {
          name: "合同金额",
          value: "contract_amount",
          width: 80
        },


        {
          name: "维护费",
          value: "maintenance_fee",
          width: 80
        },
        {
          name: "端口数",
          value: "original_port_count",
          width: 90
        },
        {
          name: "维护起始时间",
          value: "maintain_start_time",
          width: 120
        },
        {
          name: "维护结束时间",
          value: "new_maintain_stop_time",
          width: 120
        },

        // {
        //   name: "续约次数",
        //   value: "renew_contract_num",
        //   width: 80
        // },
        {
          name: "销售员",
          value: "employee_name",
          width: 100
        },
        {
          name: "销售部门",
          value: "department_name",
          width: 100
        },
        // {
        //   name: "剩余维护天数",
        //   value: "balance_days",
        //   width: 120
        // },
      ],
      customerStageList: [],
      employeeList: [],
      tableMap: {},
      brandList: []
      // contractSellType:0
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "客户列表"
    },
    customerId: {
      type: Number,
      default: null
    },
    contractDetailIds: {
      type: Array,
      default: () => []
    }
    // customerStage: {
    //   type: Number
    // },
    // isJudge: {
    //   type: Boolean,
    //   default: false
    // },
    //成交型客户 customer_stage = 3,4,5,6
    // isDealCustomer: {
    //   type: Boolean,
    //   default: false
    // }
    // isContract:{
    //   type:Boolean,
    //   default:false
    // }
  },
  created() {

  },
  methods: {
    Show(params) {
      if (!["总经理"].includes(this.cookiesUserInfo.role_name)) {
        this.formSearch.fk_sell_employee_id = this.cookiesUserInfo.userId;
      }
      if (params && params.customer_name) {
        this.formSearch.customer_name = params.customer_name
      }
      this.formSearch = {
        ...this.formSearch
      }
      this.$store.dispatch("getEmployee").then((res) => {
        this.employeeList = res;
      });
      brandAPI['query2']()
        .then((data) => {
          data.data.forEach((item) => {

            this.brandList.push({
              label: item.brand_type,
              value: item.brand_id

            });
          });

        })
      this.dialogVisible = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          this.getList(true, params);
        });
      // }, 300);
    },
    getList(f = false) {
      this.loading = true;

      let param = Object.assign(
        this.formSearch,
        this.$refs.cus_pagination.obtain()
      );
      if (f) param.pageNum = 1;

      API.getCustomerBrandByCustomer(param).then(res => {
        this.tableData = res.data;
        this.tableMap = keyBy(this.tableData, 'customer_brand_id')
        this.$refs.cus_pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    //提交
    async submitForm() {
      if (this.selectRecords.length === 0) {
        return this.error("请选择至少一条记录");
      }
      let customerIds = []
      this.selectRecords.map(item => {
        // console.log(item);
        if (!customerIds.includes(item.fk_customer_id)) {
          customerIds.push(item.fk_customer_id)
        }
      })
      if (customerIds.length > 1) {

        return this.error("只能选择同一个客户");
      }

      if (this.customerId && this.customerId !== customerIds[0]) {

        return this.error("只能选择同一个客户");
      }
      let detailFlag = true
      if (this.contractDetailIds) {
        for (let i = 0; i < this.selectRecords.length; i++) {
          if (this.contractDetailIds.includes(this.selectRecords[i].customer_brand_id)) {
            detailFlag = false
          }
        }
      }
      if (!detailFlag) {
        return this.error("不可调入重复的产品");
      }
      // let {
      //   data
      // } = await API.detailList({
      //   ids: map(this.selectRecords, 'contract_detail_id')
      // })
      let item = null
      let d = new Date();
      // for (let i = 0; i < data.length; i++) {
      //   item = this.tableMap[data[i].contract_detail_id]
      //   console.log(' === item', item);
      //   data[i].detail_sell_type =  3
      //   data[i]['contract_amount'] = item['maintenance_fee'] || item['contract_amount']
      //   data[i]['maintenance_fee'] = item['maintenance_fee']
      //   data[i]['add_port_count'] = 0
      //   d = new Date(data[i]['new_maintain_stop_time'])
      //   data[i]['maintain_start_time'] = dateFormat("yyyy-MM-dd", d);
      //   data[i]['original_maintain_stop_time'] = dateFormat("yyyy-MM-dd", d);
      //   d.setFullYear(d.getFullYear() + 1);
      //   data[i]['new_maintain_stop_time'] = dateFormat("yyyy-MM-dd", new Date(d.getTime() - 24 * 60 * 60 * 1000));
      //   data[i].remark = ''
      //   data[i]['year_maintain_cost'] = 100
      //   data[i]['measurement_unit'] = 4
      // }

      for (let i = 0; i < this.selectRecords.length; i++) {
        item = this.selectRecords[i]

        item.detail_sell_type =  3
        item['contract_amount'] = item['maintenance_fee'] || item['contract_amount']

        item['add_port_count'] = 0
        d = new Date(item['new_maintain_stop_time'])
        item['maintain_start_time'] = dateFormat("yyyy-MM-dd", d);
        item['original_maintain_stop_time'] = dateFormat("yyyy-MM-dd", d);
        d.setFullYear(d.getFullYear() + 1);
        item['new_maintain_stop_time'] = dateFormat("yyyy-MM-dd", new Date(d.getTime())); //  - 24 * 60 * 60 * 1000
        item.remark = ''
        item['year_maintain_cost'] = 100
        item['measurement_unit'] = 4
        item['invoice_tax_rate'] = '6'
      }

      this.$emit("getInfo", this.selectRecords, customerIds[0],
        concat(this.contractDetailIds, this.selectRecords.map(item => item.customer_brand_id)));
      this.dialogCancel();
    },
    checkSelltype(detail_sell_type) {
      if (!detail_sell_type) return null
      if (detail_sell_type === 5) {
        return 5
      } else {
        return 3
      }

    },
    dialogCancel() {
      // this.contractSellType = 0
      this.dialogVisible = false;
      this.selectRecords = [];
      this.formSearch = {}
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
    rowDblclick(row, column, event) {
      this.selectRecords = []
      this.selectRecords.push(row)
      this.submitForm()
    }
  },
  computed: {
    ...mapGetters(["buttonPermissions", 
    'params_constant_customer_stage', 
    "sell_type",
    "cookiesUserInfo"])
  }
};