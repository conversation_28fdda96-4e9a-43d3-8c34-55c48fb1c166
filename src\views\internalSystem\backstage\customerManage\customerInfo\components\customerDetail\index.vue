<template>
  <div
    class="body-p10"
    v-if="dialogVisible"
    style="overflow-y: auto; overflow-x: hidden"
  >
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="del" v-permit="'DEL_CUSTOMER_INFO_NEW'"
        >删除</el-button
      >
      <el-button
        type="primary"
        @click="updateSales"
        v-permit="'CUSTOMER_UPDATE_SALESMAN_NEW'"
        >销售员转移</el-button
      >
      <el-button
        type="primary"
        @click="changeStage"
        v-permit="'CHANGE_CUSTOMER_PHASE_NEW'"
        >更改客户阶段</el-button
      >
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="客户基础资料" name="first">
        <basic-data ref="basicData" :customer_id="customer_id" />
      </el-tab-pane>
      <el-tab-pane label="销售合同详情" name="second">
        <contract-detail :customer_id="customer_id" />
      </el-tab-pane>
      <el-tab-pane label="产品信息列表" name="productInformation">
        <ProductInformation :customer_id="customer_id" :port_number="port_number"/>
      </el-tab-pane>
      <el-tab-pane label="联系人列表" name="fourth">
        <contact-list :customer_id="customer_id" />
      </el-tab-pane>

      <el-tab-pane label="销售员转移列表" name="third">
        <sales-transfer-list
          ref="salesTransferList"
          :customer_id="customer_id"
        />
      </el-tab-pane>
      <el-tab-pane label="回访单" name="salesVisiting">
        <salesVisiting :customer_id="customer_id" />
      </el-tab-pane>
    </el-tabs>
    <el-dialog
      title="销售员转移"
      :visible.sync="dialogUpdate"
      width="40%"
      @close="dialogUpdateClose"
    >
      <el-form
        ref="updateForm"
        :model="updateForm"
        :rules="updateFormRules"
        label-width="100px"
      >
        <el-form-item label="销售员" style="width: 100%">
          <el-select
            v-model="updateForm.fk_sale_employee_id"
            placeholder="请选择销售员"
            class="inputBox"
            filterable
            clearable
          >
            <el-option
              v-for="item in salesList"
              :key="item.employeeId"
              :label="item.employee_number + '-' + item.employee_name"
              :value="item.employeeId"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogUpdate = false">取 消</el-button>
        <el-button size="small" type="primary" @click="dialogUpdatePost"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="更改客户阶段"
      :visible.sync="dialogChange"
      width="40%"
      @close="dialogChangeClose"
    >
      <el-form
        ref="changeForm"
        :model="changeForm"
        :rules="changeFormRules"
        label-width="100px"
      >
        <el-form-item label="客户阶段" style="width: 100%">
          <el-select
            v-model="changeForm.customer_stage"
            placeholder="请选择客户阶段"
            class="inputBox"
            filterable
            clearable
          >
            <el-option
              v-for="item in params_constant_customer_stage"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户阶段备注" style="width: 100%">
          <el-input
          v-model="changeForm.customer_stage_remark"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogChange = false">取 消</el-button>
        <el-button size="small" type="primary" @click="dialogChangePost"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script src="./index.js">
</script>
