import API from '@/api/internalSystem/financialManage/invoiceIncome'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddInvoiceIncome from "./components/addInvoiceIncome/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import AuditDetail from '@/mixins/auditDetail.js'
import {
  mapGetters
} from "vuex";
export default {
  name: "invoiceIncome",
  mixins: [AuditDetail],
  data() {
    return {
      title: "发票进项单",
      loading: false,
      tableData: [],
      formSearch: {
        invoice_unit: "",
        startTime: "",
        endTime: ""
      },
      tableList: [{
          name: "审核状态",
          value: "audit_state_name"
        },
        {
          name: "编号",
          value: "invoice_income_no"
        },
        {
          name: "开票单位",
          value: "invoice_unit"
        },
        {
          name: "发票明细",
          value: "invoice_detail"
        },
        {
          name: "发票号码",
          value: "invoice_number"
        },
        {
          name: "收票金额",
          value: "invoice_money"
        },
        {
          name: "发票时间",
          value: "invoice_time"
        }
      ]
    };
  },

  mounted() {
    this.getList();
  },
  methods: {
    getList(f = false) {
      this.isAudit = false;
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
      if (f)
        param.pageNum = 1;
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    add() {
      this.$refs.addInvoiceIncome.Show();
    },
    //打开修改发票进项单会话框
    modify(item) {
      let params = {
        invoice_income_id: item.invoice_income_id
      };
      API.getInfo(params)
        .then(data => {
          this.$refs.addInvoiceIncome.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      if (item.audit_state == 1)
        return this.error("该记录已审核通过，不允许删除")
      let params = {
        invoice_income_id: item.invoice_income_id
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    }
  },

  components: {
    AddInvoiceIncome,
    Pagination,
    TableView,
    MyDate
  },
  computed: {
    ...mapGetters(["buttonPermissions"])
  }
};