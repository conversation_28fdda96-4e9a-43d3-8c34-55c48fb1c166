/**
 * 验证表单的方法
 */

/**
 *验证表头是否符合条件并在不符合条件的第一个位置获取焦点
 * @param {*} refData 表单验证的ref引用
 */
function validateHeadForm(refData) {
  return new Promise((resolve, reject) => {
    //校验表头必填项
    refData.validate(validate => {
      if (validate) {
        //校验表格
        resolve();
      } else {
        setTimeout(() => {
          //获取验证错误的焦点
          var isError = document.getElementsByClassName("is-error");
          isError[0].querySelector("input").click();
        }, 100);
        reject();
      }
    });
  });
}

export { validateHeadForm };
