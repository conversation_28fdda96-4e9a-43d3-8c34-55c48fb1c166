export default {
  checkPhone: (rules, value, callback) => {
    if (!value) callback(new Error(" "));
    if (!/^1[3|4|5|6|7|8|9]\d{9}$/.test(value)) {
      callback(new Error(" "));
    } else {
      callback();
    }
  },
  checkBank: (rules, value, callback) => {
    if (value) {
      var reg = /^\d{15,20}$/;
        if(!reg.test(value)){
          callback(new Error("银行账户格式有误"));
        }else{
          callback();
        }
    } else {
      callback(new Error("请输入银行账户"));
    }
  }
}
// export default {
//   checkPhone: (rules, value, callback) => {
//     if (!value) callback(new Error(" "));
//     if (!/^1[3|4|5|6|7|8|9]\d{9}$/.test(value)) {
//       callback(new Error("手机号码格式有误"));
//     } else {
//       callback();
//     }
//   },
//   checkBank: (rules, value, callback) => {
//     if (value) {
//       var reg = /^\d{15,20}$/;
//         if(!reg.test(value)){
//           callback(new Error("银行账户格式有误"));
//         }else{
//           callback();
//         }
//     } else {
//       callback(new Error("请输入银行账户"));
//     }
//   }
// }