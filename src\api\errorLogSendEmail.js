import axios from "axios";
import store from "@/store/internalSystem/index.js";
function errorLogSendEmail(res){
  console.log(res)
  let userInfo=store.getters.userInfo;
    let params={
        type:0,  //类型 0全部   1云平台  2商贸erp  3在线商城 4仓库
        request_interface:res.request.responseURL,  //接口地址
        parameter:res.config.data,  //参数
        company:1,//公司id，没有就默认1
        email_list:["<EMAIL>"], //发送的邮箱列表
        employees:1, //当前登录人id
        token:res.config.headers.token,
        environment:3,  //环境 1测试 2正式 3本地
        journal:`发生错误的网页地址：${window.location.href}`   //错误日志
    }

    params.environment= process.env.NODE_ENV === "production" ?( window.location.origin.indexOf("test")===-1?2:1):3
    params.company=userInfo.companyId?userInfo.companyId:1;
    params.employees=userInfo.userId?userInfo.userId:1;
    params.type=res.config.url.indexOf("basicAPI")?1:(res.config.url.indexOf("tradeAPI")?2:(res.config.url.indexOf("stockAPI")?4:0));
    if(params.environment===3) return //本地环境不发邮件
    axios.post("/jiqin_server/monitor_log/add",params).then(()=>{
      console.log("发送成功")
    }).catch(() => {})
}
export {
  errorLogSendEmail
};
