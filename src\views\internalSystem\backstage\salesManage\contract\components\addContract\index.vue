<template>
  <div
    class="body-p10 flex1 orderH100"
    style="overflow-y: auto; overflow-x: hidden; margin-right: 20px"
    v-if="dialogVisible"
  >
    <div>
      <!-- {{software_version}} -->
      <!-- {{yearsFeeList}} -->
      <!-- {{moduleList.length}} -->
      <!-- {{proList.length}} -->
      <!-- {{ ruleForm.port_number }} -->
      <!-- {{sell_type}}} -->
      <div style="float: left">
        <el-button @click="dialogCancel(true)">关闭</el-button>
        <el-button type="primary" @click="dialogCancel(false)">新增</el-button>
        <el-button
          :disabled="
            !(
              (isEdit && isOwn && ruleForm.customer_contract_id) ||
              !ruleForm.customer_contract_id
            )
          "
          type="primary"
          @click="submitForm('ruleForm')"
          :loading="loading"
          >保 存</el-button
        >

        <el-button
          v-permit="'SEND_CONTRACT_NEW'"
          :disabled="
            !(
              ruleForm.customer_contract_id &&
              ruleForm.audit_state == 4 &&
              isOwn
            )
          "
          type="primary"
          @click="send"
          :loading="loading"
          >发出</el-button
        >
        <el-button
          :disabled="
            !(
              ruleForm.customer_contract_id &&
              ruleForm.audit_state != 0 &&
              ruleForm.audit_state != 4
            )
          "
          type="primary"
          v-permit="'AUDIT_BACK_CONTRACT_NEW'"
          @click="back"
          :loading="loading"
          >回退</el-button
        >
        <el-button
          type="primary"
          v-permit="'AUDIT_CONTRACT_NEW'"
          @click="openAudit"
          >审 核</el-button
        >
        <el-button
          v-permit="'FILE_CONTRACT_NEW'"
          :disabled="!ruleForm.customer_contract_id"
          type="primary"
          @click="addFile"
          >附件</el-button
        >
        <!-- <el-dropdown
          @command="handleCommand"
          class="ml5"
          v-permit="'PRINT_CONTRACT_NEW'"
          v-if="ruleForm.customer_contract_id"
        > -->
        <el-button type="primary" @click="openContractTemplate"
          >打印合同</el-button
        >
        <!-- <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="update">修改模板</el-dropdown-item>
          <el-dropdown-item command="select">选择模板</el-dropdown-item>
        </el-dropdown-menu> -->
        <!-- </el-dropdown> -->
        <el-popover
          placement="bottom"
          width="100"
          v-model="visible"
          style="margin: 0 6px"
          :disabled="
            !(
              (isEdit && isOwn && ruleForm.customer_contract_id) ||
              !ruleForm.customer_contract_id
            )
          "
        >
          <div style="margin: 0">
            <!-- <el-button type="success" size="mini" @click="visibleFunction(3)"
            >直接从客户信息导入维护费信息</el-button
          > -->
            <div>
              <!-- <el-button
            type="success"
            size="mini"
            @click="visibleFunction(5)"
            style="margin-top: 6px"
            >直接从客户信息导入租用信息</el-button
          > -->
            </div>
            <!-- <el-button
            type="success"
            size="mini"
            @click="visibleFunction(null)"
            style="margin-top: 6px"
            >直接从所有客户列表导入信息</el-button
          > -->
            <div>
              <el-button
                type="success"
                size="mini"
                @click="openCustomerBrand(null)"
                style="margin-top: 0px"
                >打开客户产品信息列表</el-button
              >
            </div>
          </div>
          <el-button type="primary" slot="reference">调入</el-button>
        </el-popover>
        <el-button
          v-if="
            ruleForm.customer_contract_id && ruleForm.audit_state == 4 && isOwn
          "
          style="margin-left: 0px"
          type="danger"
          @click="del2"
          >删 除</el-button
        >
      </div>

      <div style="float: right">
        <!-- <el-tag style="margin-right: 20px;height:20px;"  effect="dark"
          ><span style="    align-content: center;">已上传{{ fileCount }}个附件</span></el-tag
        > -->
        <el-button type="success">
          {{ fileCount > 0 ? "已上传附件" : "未上传附件" }}</el-button
        >
        <!-- <img :src="fileImg" style="width: 100%;height:20px;" v-if="fileCount > 0"/> -->
      </div>
    </div>
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="mt10 flexAndFlexColumn"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单据编号" prop="add_user_name">
            <el-input
              v-model="ruleForm.contract_no"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据日期" prop="add_user_name">
            <el-input
              v-model="ruleForm.update_time"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="制单人" prop="add_user_name">
            <el-input v-model="addUserInfo" disabled clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.customer_name"
              placeholder="请点击选择客户"
              @focus="chooseCustomer"
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="付款方式" prop="pay_type">
            <el-select
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.pay_type"
              placeholder="请选择付款方式"
              filterable
              clearable
            >
              <el-option
                v-for="item in contract_pay_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="8">
          <el-form-item label="操作员工" prop="add_user_name">
            <el-input v-model="ruleForm.add_user_name" disabled clearable></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="销售员" prop="fk_sell_employee_name">
            <el-input
              v-model="fkSaleEmployeeUserInfo"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <!-- <el-col :span="8">
          <el-form-item label="销售员" prop="fk_sell_employee_name">
            <el-input v-model="ruleForm.fk_sell_employee_name" disabled clearable></el-input>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="销货单位" prop="sales_unit_id_format">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.sales_unit_id_format"
              @focus="chooseCompany"
              placeholder="请点击选择销货单位"
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="操作员部门" prop="fk_operator_department_name">
            <el-input v-model="ruleForm.fk_operator_department_name" disabled clearable></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="销售员部门" prop="fk_sell_department_name">
            <el-input v-model="ruleForm.fk_sell_department_name" disabled clearable></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="联系人" prop="link_man">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.link_man"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="ruleForm.train_type === 1 ? 8 : 8" class="formItem2">
          <el-form-item label="培训方式" prop="train_type">
            <el-select
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.train_type"
              placeholder="请选择培训方式"
              filterable
              clearable
            >
              <el-option
                v-for="item in contract_train_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="介绍客户" prop="introducer_format">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.introducer_format"
              placeholder="请点击选择介绍客户"
              @focus="chooseIntroducer"
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="客户传真" prop="fax">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.fax"
              
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->
      <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="所在省份" prop="province">
            <el-select
              :disabled="!isEdit||!isOwn"
              v-model="ruleForm.province"
              placeholder="请选择省份"
              @change="changeProvince"
              filterable
              clearable
            >
              <el-option
                v-for="item in provinceList"
                :key="item.province"
                :label="item.origin_place"
                :value="item.province"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
      <el-row :gutter="20">
        <el-col :span="8" class="formItem2">
          <el-form-item label="手机" prop="phone">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.phone"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="所在城市" prop="city">
            <el-select
              :disabled="!isEdit||!isOwn"
              v-model="ruleForm.city"
              placeholder="选择省份获取可选城市"
              filterable
              clearable
            >
              <el-option
                v-for="item in cityList"
                :key="item.city"
                :label="item.origin_place"
                :value="item.city"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="销售类型" prop="sell_type">
            <el-select
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.sell_type"
              placeholder="请选择销售类型"
              filterable
              clearable
              @change="sellTypeChange"
            >
              <el-option
                v-for="item in sell_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="培训天数" prop="train_days">
            <el-input
              :disabled="!isEdit || !isOwn || ruleForm.train_type !== 1"
              v-model="ruleForm.train_days"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="介绍合同" prop="introducer_contract_format">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.introducer_contract_format"
              placeholder="请点击选择介绍合同"
              @focus="chooseContract"
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="详细地址" prop="address">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.address"
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="联系人QQ" prop="link_qq">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.link_qq"
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->

        <el-col :span="8" class="formItem2">
          <el-form-item label="单据备注" prop="remark">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.remark"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="推荐员工" prop="fk_recommend_employee_id">
            <el-select
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.fk_recommend_employee_id"
              placeholder="请选择推荐员工"
              filterable
              clearable
            >
              <el-option
                v-for="item in employeeList"
                :key="item.employeeId"
                :label="item.employee_name"
                :value="item.employeeId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="续费合同" prop="renewal_contract_format">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.renewal_contract_format"
              placeholder="请点击选择续费合同"
              @focus="chooseRenewContract"
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="首次成交时间" prop="deal_time">
            <el-input
              :disabled="true"
              v-model="ruleForm.deal_time"
              placeholder=""
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="审核状态" prop="audit_state_name">
            <el-input
              disabled
              v-model="ruleForm.audit_state_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8" class="formItem2">
          <el-form-item label="审核备注" prop="audit_remark">
            <el-input
              disabled
              v-model="ruleForm.audit_remark"
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8" class="formItem2">
          <el-form-item label="软件序列号" prop="software_no">
            <el-input
              disabled
              v-model="ruleForm.software_no"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" class="formItem2">
          <el-form-item label="合同总金额" prop="total_money">
            <el-input disabled v-model="total_money" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="销货单位" prop="sales_unit_id">
            <el-select
              v-model="ruleForm.sales_unit_id"
              placeholder="请选择销货单位"
              filterable
              clearable
            >
              <el-option
                v-for="item in salesUnits"
                :key="item.sales_unit_id"
                :label="item.company_name"
                :value="item.sales_unit_id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row :gutter="20">
        <el-col :span="8" class="formItem2">
          <el-form-item label="原有端口数" prop="port_number">
            <el-input
              disabled
              v-model="ruleForm.port_number"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="端口数" prop="sure_port_number">
            <el-input
              disabled
              v-model="ruleForm.sure_port_number"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row> -->
      <div class="flexAndFlexColumn">
        <!-- <el-button
          v-if="
            (isEdit && isOwn && ruleForm.customer_contract_id) ||
            !ruleForm.customer_contract_id
          "
          type="primary"
          @click="add"
          >新增产品</el-button
        > -->
        <!-- <el-button
        :disabled="!(
          (isEdit && isOwn && ruleForm.customer_contract_id) ||
          !ruleForm.customer_contract_id)
        "
        type="primary"
        @click="callIn"
        >调入报价单</el-button
      > -->
        <!-- <el-tabs v-model="activeName" > -->
        <!-- <el-tab-pane label="合同产品" name="first" > -->

        <div>
          <el-button
            v-if="
              (isEdit && isOwn && ruleForm.customer_contract_id) ||
                !ruleForm.customer_contract_id
            "
            type="primary"
            @click="add"
            >新增产品</el-button
          >
        </div>

        <!-- <el-button
          v-if="
            (isEdit && isOwn && ruleForm.customer_contract_id) ||
            !ruleForm.customer_contract_id
          "
          :disabled="!ruleForm.fk_customer_id"
          type="primary"
          @click="openCustomerBrand(true)"
          >客户产品信息</el-button
        > -->
        <!-- <span style="font-size: 14px"> 合同产品</span> -->

        <TableCustom
          class="mt10 tableContent"
          tableHeight="100%"
          ref="proTableCustom"
          :obj="proObj"
          :tableCol="proTableCol"
          :ruleForm="ruleForm"
          @del="del"
          :itemFlag="'proTableCustom'"
          :isDel="
            !!(isEdit && isOwn && ruleForm.customer_contract_id) ||
              !ruleForm.customer_contract_id
          "
          :special="'contract'"
          :parentComponentName="'contract'"
          @getTotalMoney="getTotalMoney"
        />
        <!-- </el-tab-pane> -->

        <!-- <el-tab-pane label="功能模块" name="second">
          <table-custom
            ref="moduleTableCustom"
            :obj="moduleObj"
            :tableCol="moduleTableCol"
            @del="del"
            :itemFlag="'moduleTableCustom'"
            :isDel="
              !!(isEdit && isOwn && ruleForm.customer_contract_id) ||
              !ruleForm.customer_contract_id
            "
          />
        </el-tab-pane> -->
        <!-- </el-tabs> -->
      </div>
    </el-form>

    <CustomerBrandList
      ref="customerBrandList"
      dialogTitle="客户产品信息表"
      :contractDetailIds="contractDetailIds"
      :customerId="customerIds"
      @getInfo="getCustomerBrandInfo"
    />
    <CustomerList
      ref="dealCustomerList"
      dialogTitle="成交客户列表"
      :customerStage="3"
      @getInfo="getInfo"
    />
    <!-- <CustomerList
      ref="customerList"
      dialogTitle="成交客户列表"
      :isJudge="true"
      :isDealCustomer="true"
      @getInfo="getCustomerInfo"
      :isContract="true"
    /> -->
    <CustomerList
      ref="customerAllList"
      dialogTitle="所有客户列表"
      :isJudge="true"
      @getInfo="getCustomerInfo"
      :isContract="true"
    />
    <!-- <SalesUnitList
      ref="salesUnitList"
      dialogTitle="销货单位列表"
      @getInfo="getCampanyInfo"
    /> -->
    <ContractList
      ref="contractList"
      dialogTitle="合同列表"
      :isJudge="true"
      @getInfo="getContractInfo"
      :oneselfFlag="false"
    />
    <!-- <ContractList
      ref="renewContractList"
      dialogTitle="合同列表"
      :isOwn="true"
      @getInfo="getRenewContractInfo"
    /> -->
    <!-- <QuotationList
      ref="quotationList"
      dialogTitle="报价单列表"
      :customerId="ruleForm.fk_customer_id ? ruleForm.fk_customer_id : 0"
      @getInfo="getQuotationInfo"
    /> -->

    <FileList
      ref="fileList"
      :fileId="ruleForm.customer_contract_id"
      fileType="contract"
      @closeFile="closeFile"
    />

    <el-dialog
      title="打印合同"
      :visible.sync="dialogVisibleTem"
      append-to-body
      @close="dialogCancelTem"
      width="800px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <el-form
        :model="temForm"
        :rules="temRules"
        ref="temForm"
        label-width="100px"
      >
        <!-- <el-form-item label="合同模板" prop="contract_template_id">
          <el-select
            v-model="temForm.contract_template_id"
            placeholder="请选择合同模板"
            filterable
            clearable
          >
            <el-option :label="'销售合同模板'" :value="'116'"></el-option>
            <el-option :label="'租用合同模板'" :value="'164'"></el-option>
            <el-option
              :label="'租用合同(适用于金万维/阿里云等)模板'"
              :value="'161'"
            ></el-option>
            <el-option :label="'增加端口合同模板'" :value="'115'"></el-option>
            <el-option :label="'服务协议(按次)'" :value="'163'"></el-option>
            <el-option :label="'维护费合同模板'" :value="'157'"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="合同模板" prop="contract_template_id">
          <el-radio-group v-model="temForm.contract_template_id">
            <div>
              <!-- <el-radio :label="116" border style="height: 40px;"
                ><span style="font-size:20px;">销售合同模板</span></el-radio
              > -->
              <!-- <el-radio :label="164" border style="height: 40px;"
                ><span style="font-size:20px;">租用合同模板</span></el-radio
              > -->
              <el-radio :label="163" border style="height: 40px;"
                ><span style="font-size:20px;">服务协议(按次)</span></el-radio
              >
            </div>
            <div style="margin-top: 10px">
              <el-radio :label="115" border style="height: 40px;"
                ><span style="font-size:20px;">增加端口合同模板</span></el-radio
              >
              <el-radio :label="157" border style="height: 40px;"
                ><span style="font-size:20px;">维护费合同模板</span></el-radio
              >
            </div>
            <div style="margin-top: 10px">
              <el-radio :label="161" border style="height: 40px;"
                ><span style="font-size:20px;"
                  >租用合同模板</span
                ></el-radio
              >
            </div>
            <div style="margin-top: 10px">
              <el-radio :label="165" border style="height: 40px;"
                ><span style="font-size:20px;"
                  >软件购买合同书模板</span
                ></el-radio
              >
            </div>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTem('temForm')">确定</el-button>
        <el-button @click="dialogCancelTem">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="审核"
      :visible.sync="dialogVisibleAudit"
      append-to-body
      @close="dialogCancelAudit"
      width="660px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <el-form
        :model="auditForm"
        :rules="auditRules"
        ref="auditForm"
        label-width="100px"
      >
        <!-- <el-form-item label="审核状态" prop="auditState">
          <el-select
            v-model="auditForm.auditState"
            placeholder="请选择审核状态"
            filterable
            clearable
          >
            <template v-for="item in contract_auditStateList">
              <el-option
                v-show="item.id != ruleForm.audit_state && item.id != 4"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </template>
          </el-select>
        </el-form-item> -->
        <el-form-item label="审核状态" prop="auditState">
          <el-radio-group v-model="auditForm.auditState">
            <template v-for="item in contract_auditStateList">
              <el-radio
                v-if="item.id != ruleForm.audit_state && item.id != 4"
                :key="item.id"
                :label="item.value"
                :value="item.value"
                border
                style="height: 30px;"
                ><span style="font-size:16px;">{{ item.label }}</span></el-radio
              >
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="auditRemark">
          <el-input
            v-model="auditForm.auditRemark"
            type="textarea"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm('auditForm')"
          :loading="loading"
          >保 存</el-button
        >
        <el-button @click="dialogCancelAudit">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script src="./index.js"></script>

<style lang="scss" scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown + .el-dropdown {
  margin-left: 15px;
}
@import "@/assets/css/element/font-color.scss";
</style>
<style lang="scss">
.el-select {
  display: inline-block;
  position: relative;
  width: 100%;
}
</style>
