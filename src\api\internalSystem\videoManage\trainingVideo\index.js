import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: params => {
    return Axios.post(`${environment.internalSystemAPI}trainingVideo/query`, params)
  },
  // 新增
  add: params => {
    return Axios.post(`${environment.internalSystemAPI}trainingVideo/add`, params)
  },
  // 删除
  remove: params => {
    return Axios.post(`${environment.internalSystemAPI}trainingVideo/remove`, params)
  },
  // 编辑
  update: params => {
    return Axios.post(`${environment.internalSystemAPI}trainingVideo/update`, params)
  },
  // 获取单条信息
  getInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}trainingVideo/getInfo`, params)
  }
};