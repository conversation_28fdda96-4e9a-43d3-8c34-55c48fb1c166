<template>
  <el-dialog
    title="提示"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose"
    :modal="false"
  >
    <div slot="title" class="dialog-title">
      <span class="tip">{{ head.problem_id }}</span>
      <span class="title">{{ head.problem_title }}</span>
    </div>
    <div>
      <el-form
        :model="submitData"
        :rules="rules"
        :status-icon="true"
        ref="headForm"
        label-width="80px"
        class="head-ruleForm"
      >
        <el-form-item label="解决方案" v-if="type === 3" prop="solution">
          <el-select v-model="submitData.solution" placeholder="" clearable>
            <el-option
              v-for="(item, index) in problem_solution"
              :key="index"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="解决日期" v-if="type === 3" prop="solve_time">
          <el-date-picker
            style="width: 100%"
            v-model="submitData.solve_time"
            type="date"
            placeholder="选择解决日期"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item
          label="指派给"
          v-if="type !== 4"
          prop="designated_person_id"
        >
          <el-select
            class="flex1"
            v-model="submitData.designated_person_id"
            placeholder="请选择需要指派的员工"
          >
            <el-option
              v-for="(item, index) in userList"
              :key="index"
              :label="item.employee_name"
              :value="item.employeeId"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="bug类型" v-if="type === 2" prop="problem_type">
          <el-select v-model="submitData.problem_type" placeholder="">
            <el-option
              v-for="(item, index) in problem_type"
              :key="index"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="优先级" v-if="type === 2" prop="problem_priority">
          <el-select
            v-model="submitData.problem_priority"
            placeholder=""
            clearable
          >
            <el-option value="">
              <span class="radus wenhao">?</span>
            </el-option>
            <el-option
              v-for="(item, index) in 4"
              :key="index"
              :label="item"
              :value="item"
            >
              <span class="radus">{{ item }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="备注" prop="problem_content">
          <RichText :value="submitData.remarks" @input="richChange" />
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
    </span> -->
  </el-dialog>
</template>

<script>
import API from "@/api/internalSystem/bugManage/bugContent/index.js";
import CommonAPI from "@/api/internalSystem/bugManage/common/index.js";
// import RichText from "@/components/RichText/RichText.vue";
import RichText from "@/components/internalSystem/RichText/RichText.vue";
import { mapGetters } from "vuex";
export default {
  props: {
    //1.指派 2.确认 3.解决 4.关闭 5.激活

    type: {},
  },
  data() {
    return {
      dialogVisible: false,
      head: {},
      submitData: {
        remarks: "",
        designated_person_id: "",
        problem_type: "",
        problem_priority: "",
        solution: "",
        solve_time: "",
      },
      rules: {
        solution: [
          { required: true, message: "请选择解决方案", trigger: "change" },
        ],
      },
      userList: [],
      problem_id: "",
    };
  },

  methods: {
    Show(problem_id) {
      this.problem_id = problem_id;
      this.getDetail();
      this.getUserList();
      this.dialogVisible = true;
      this.$refs.headForm.resetFields();
    },
    getDetail() {
      let params = {};

      params.problem_id = this.problem_id;
      API.getBugInfo(params).then((res) => {
        this.head = res.data;
        this.submitData.problem_type = res.data.problem_type;
        this.submitData.problem_priority = res.data.problem_priority;
        if (this.type === 2 || this.type === 3)
          this.submitData.designated_person_id = res.data.designated_person_id;
        if (this.type === 3) this.submitData.solve_time = new Date();
      });
    },
    handleClose() {
      Object.assign(this.$data, this.$options.data());
    },

    getUserList() {
      CommonAPI.getUserList().then((res) => {
        this.userList = res.data;
      });
    },
    //富文本取值
    richChange(value) {
      this.submitData.remarks = value;
    },
    submit() {
      this.$refs.headForm
        .validate((valid) => {
          if (valid) {
            this.save();
          } else {
            return false;
          }
        })
        .catch(() => {});
    },
    save() {
      let APIName = "";
      let params = {};
      switch (+this.type) {
        case 1:
          APIName = "assignedBug";
          break;
        case 2:
          APIName = "updateConfirm";
          break;
        case 3:
        case 4:
        case 5:
          APIName = "updateProblemState";
          break;
        default:
          APIName = "assignedBug";
      }
      Object.assign(params, this.submitData);
      params.problem_id = this.problem_id;
      /* 
      problem_state 1、激活   2、已解决   3、已关闭
      */
      // 确认
      if (this.type === 2) params.is_confirm = 1;
      // 解决
      if (this.type === 3) params.problem_state = 2;
      // 关闭
      if (this.type === 4) params.problem_state = 3;
      // 激活
      if (this.type === 5) params.problem_state = 1;
      API[APIName](params).then((res) => {
        this.success("操作成功");
        this.$emit("success", res);
        this.handleClose();
      });
    },
  },
  components: {
    RichText,
  },
  computed: {
    ...mapGetters(["problem_type", "problem_solution"]),
  },
};
</script>

<style lang="scss" scoped>
.dialog-title {
  border-bottom: 1px solid #ddd;
  box-sizing: border-box;
  height: 50px;
  width: 100%;
  background-color: #f8fafe;
  .tip {
    border: 1px solid #777;
    color: #777;
    padding: 0 4px;
    font-size: 12px;
  }
  .title {
    font-weight: bold;
    padding-left: 5px;
    margin-left: 10px;
    font-size: 13px;
    color: #333;
  }
}
.el-dialog__wrapper {
  z-index: 20 !important;
}
.v-modal {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  background: #000;
  z-index: 0 !important;
}
</style>
