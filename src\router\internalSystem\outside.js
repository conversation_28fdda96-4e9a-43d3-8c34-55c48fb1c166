export default [
  {
    path: "/outside",
    component: (resolve) =>
      require(["@/views/internalSystem/outside/index.vue"], resolve),
    redirect: {
      path: "/outside/outsideBugList",
    },
    children: [
        {
            path: "outsideBugDetail",
            name: "outsideBugDetail",
            title: "外部bug详情",
            meta: {
              title: "外部bug详情",
            },
            component: () =>
              import("@/views/internalSystem/outside/outsideBugDetail/index.vue"),
          },
      {
        path: "outsideBugList",
        name: "outsideBugList",
        title: "外部bug列表",
        meta: {
          title: "外部bug列表",
        },
        component: () =>
          import("@/views/internalSystem/outside/outsideBugList/index.vue"),
      },
    ],
  },
];
