<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    append-to-body
    @close="dialogCancel"
    width="66%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    v-dialogDrag
  >
    <el-form :inline="true" :model="formSearch" size="small">
  <el-form-item label="查询条件">
        <el-select
          v-model="formSearch.publish_man"
          placeholder="请选择发布人"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_name"
            :value="item.employeeId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="formSearch.title"
          placeholder="请输入主题"
          style="width: 150px"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="formSearch.keywords"
          placeholder="请输入关键词"
          style="width: 150px"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.type"
          placeholder="请选择类型"
          class="inputBox"
          filterable
          clearable
          style="width: 150px"
        >
          <el-option
            v-for="item in typeList"
            :key="item.sysValue"
            :label="item.sysName"
            :value="item.sysValue"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
        <el-button type="primary" @click="submitForm">选择</el-button>
        <el-button type="primary" @click="dialogCancel">取 消</el-button>
      </el-form-item>
    </el-form>
    <table-view
      :tableList="tableList"
      :tableData="tableData"
      :tableHeight="520"
      @getSelectRecords="getSelectRecords"
      :isSel="true"
      :isDblclick="true"
      @rowDblclick="rowDblclick"
    ></table-view>
    <Pagination ref="con_pagination" @success="getList" />
    <span slot="footer" class="dialog-footer">
    </span>
  </el-dialog>
</template>
<script src="./index.js">
</script>