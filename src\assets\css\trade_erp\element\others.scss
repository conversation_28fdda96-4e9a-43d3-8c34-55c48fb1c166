#erp {
  .el-tabs .el-tabs--top {
    height: 100% !important;
  }

  //tab栏容器高度覆盖
  .el-tabs__content {
    height: calc(100% - 40px) !important;

    .el-tab-pane {
      height: 100%;
      width: 100%;
    }
  }
}

//消息提示样式覆盖
.el-message {
  margin-top: 20px !important;
  top: 0 !important;
}
//el树的当前行样式
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #e6f7ff;
  &:hover{
    background-color:#d7effb;
  }
}

//左侧菜单
ul.el-menu--popup{
  width: 140px !important;
  min-width: 140px !important;
}