import API from "@/api/internalSystem/videoManage/trainingVideo/index.js";
import AddTrainingVideo from "../addTrainingVideo/index.vue";
import AddTrainingFile from "../addTrainingFile/index.vue";
import { sortTable } from "@/common/global/vxeTableDrag.js";
export default {
  data() {
    return {
      filterText: ``,
      directoryTreeList: [],
      checkedKey: [],
      defaultProps: {
        children: "children",
        label: "directory_name",
        disabled: true,
      },
      showCheckbox: false,
      directory_id: null,
      tableData: [],
      sortable:""
    };
  },

  mounted() {
    this.columnDrop();
  },
  beforeDestroy() {
    if (this.sortable) {
      this.sortable.destroy();
    }
  },
  watch: {},
  methods: {
    changeEnableFlag(row, index) {
      API.update({
        enable_flag: row.enable_flag === 1 ? 2 : 1,
        training_video_id: row.training_video_id,
      }).then((res) => {
        this.getTrainingFile(this.directory_id);
      });
    },
    addFile(row) {
      this.$refs.addTrainingFile.show(row.training_video_id);
    },
    getList() {
      this.getTrainingFile(this.directory_id);
    },
    //设置选中目录树
    getTrainingFile(directory_id) {
      if (!directory_id) return;
      this.directory_id = directory_id;
      API.query({
        fk_directory_id:directory_id,
      })
        .then((res) => {

          this.tableData = res.data;
        })
        .catch(() => {});
    },
    //删除
    removeFile(row) {
      if (row.enable_flag === 1) {
        this.error("启用状态无法删除，请先禁用");
        return;
      }
      this.$confirm("此操作将删除该视频, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          API.remove({
            training_video_id: row.training_video_id,
          })
            .then(() => {
              this.getTrainingFile(this.directory_id);
            })
            .catch(() => {})
            .finally(() => {});
        })
        .catch(() => {});
    },
    downFile(row) {
      if (row.file_url) {
        if (row.file_suffix === "txt") {
          window.location.href =
            row.file_url + "?response-content-type=application/octet-stream";
        } else {
          window.location.href = row.file_url;
        }
      }
    },
    columnDrop() {
      this.$nextTick(() => {
        let xTable = this.$refs.xTable;
        this.sortable = sortTable(xTable);
      });
    },
    sortChange({ column, property, order }){
      if(this.isOrder){
        this.$emit("sortChange", { column, prop:property, order });
      }
    },
  },
  components: {
    AddTrainingVideo,
    AddTrainingFile,
  },
};
