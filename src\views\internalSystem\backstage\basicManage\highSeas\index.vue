<template>
  <div class="body-p10" style="overflow-y: auto; overflow-x: hidden">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      label-width="110px"
      :rules="rules"
      class="mt10"
      style="width: 50%; margin-left: 20px; margin-top: 20px"
    >
      <el-form-item label="超期天数" prop="days">
        <el-input v-model.number="ruleForm.days" style="width: 260px"></el-input>
      </el-form-item>
      <el-form-item label="转入公海账户" prop="userId">
        <el-select
          v-model="ruleForm.userId"
          placeholder="请选择转入公海账户"
          style="width: 260px"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number + '-' + item.employee_name"
            :value="item.employeeId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')"
          >保存</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
.visitingTitle {
  font-size: 18px;
  font-weight: bold;
  margin-top: 20px;
}
</style>