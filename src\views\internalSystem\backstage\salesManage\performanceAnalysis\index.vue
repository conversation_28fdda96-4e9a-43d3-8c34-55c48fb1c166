<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small">
      <el-form-item label="查询条件">
        <el-select v-model="formSearch.employeeId" placeholder="请选择销售员" class="inputBox" filterable clearable
          style="width: 150px">
          <el-option v-for="item in employeeList" :key="item.employee_number"
            :label="item.employee_number + '-' + item.employee_name" :value="item.employeeId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <MyDate v-model="formSearch.timeList" hint="请选择开始时间" type="month" style="width: 240px"></MyDate>
      </el-form-item>
      <el-form-item class="ml10">
        <el-select v-model="formSearch.queryType" clearable>
          <el-option label="按销售员" value="按销售员"></el-option>
          <el-option label="按产品归属" value="按产品归属"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>

        <!-- <el-button type="primary" @click="dialogVisibleClick()"
          >过滤条件</el-button
        >-->
        <!-- <el-button type="primary" @click="showVChart(true)" 
          >形成图表分析  </el-button
        >-->
      </el-form-item>

    </el-form>
    <TableView :tableList="tableList" :tableData="tableData" :isOperation="false"></TableView>
    <div class="mt10 moneyTitle">
      <el-row>
        <el-col :span="4">合同总金额：{{ allList.contract_amount || 0.00 }}元</el-col>
        <el-col :span="4">到账总金额：{{ allList.receivables_amount || 0.00 }}元</el-col>
        <el-col :span="4">合同未到账总金额：{{ allList.no_contract_receivables_amount || 0.00 }}元</el-col>
      </el-row>
    </div>
    <Pagination ref="pagination" @success="getList" />
    <!-- <el-dialog title="查询条件" :visible.sync="dialogVisible" width="30%" v-dialogDrag>
      <el-form ref="form" :model="formSearch" label-width="130px">
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="dialogVisibleQuery()">确 定</el-button>
      </span>
    </el-dialog> -->
    <!-- <el-dialog
      title="业绩图标分析"
      :visible.sync="dialogVisibleVChart"
      width="70%"
      v-dialogDrag
    >
      <ve-line :data="chartData"></ve-line>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleVChart = false">关闭</el-button>
      </span>
    </el-dialog>-->
  </div>
</template>

<script src="./index.js"></script>