import API from '@/api/internalSystem/customerManage/customerInfo'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
export default {
  name: "salesTransferList",
  data() {
    return {
      loading: false,
      tableData: [],
      tableList: [{
          name: "客户编号",
          value: "customer_no"
        },
        {
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "原销售员",
          value: "raw_salesman_name"
        },
        {
          name: "新销售员",
          value: "new_salesman_name"
        },
        {
          name: "操作员工",
          value: "add_user_id_name"
        },
        {
          name: "操作时间",
          value: "update_time"
        }
      ]
    };
  },
  props: {
    customer_id: {
      type: Number
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      let param = Object.assign(this.$refs.sales_pagination.obtain());
      param.customer_id=this.customer_id;
      API.updateSalesmanList(param).then(res => {
        this.tableData = res.data;
        this.$refs.sales_pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    }
  },
  components: {
    TableView,
    Pagination
  }
};