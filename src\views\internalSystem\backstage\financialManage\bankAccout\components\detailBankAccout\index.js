import API from '@/api/internalSystem/financialManage/bankAccout'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  getOptions,
} from "@/common/internalSystem/common.js"
import {
  mapGetters
} from "vuex";
export default {
  name: "detailBankAccout",
  components: {
    TableView,
    Pagination,
    MyDate
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      tableData: [],
      formSearch: {
        money_type: "",
        startTime: "",
        endTime: ""
      },
      tableList: [{
          name: "钱款类型",
          value: "money_type_format",
          width: 100
        },
        {
          name: "单据编号",
          value: "documents_no"
        },
        {
          name: "钱款备注",
          value: "money_remark"
        },
        {
          name: "钱款金额",
          value: "money_amount",
          width: 100
        },
        {
          name: "钱款日期",
          value: "money_date",
          width: 100
        },
        {
          name: "创建日期",
          value: "add_time",
          width: 100
        },
        {
          name: "账户余额",
          value: "bank_amount",
          width: 100
        }
      ],
      moneyTypeList: [],
      financial_bank_accout_id: "",
      exLoading: false
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "银行流水"
    }
  },
  methods: {
    Show(financial_bank_accout_id) {
      this.financial_bank_accout_id = financial_bank_accout_id;
      this.dialogVisible = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          this.moneyTypeList = getOptions('t_financial_bank_water', 'money_type');
          this.getList();
        });
      // }, 300);
    },
    getList(f = false) {
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.bank_pagination.obtain());
      if (f)
        param.pageNum = 1;
      param.financial_bank_accout_id = this.financial_bank_accout_id;
      API.detailList(param).then(res => {
        this.tableData = res.data;
        this.$refs.bank_pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    dialogCancel() {
      this.dialogVisible = false;
    },
    //导出银行流水
    exportBankDetail() {
      this.exLoading = true;
      let param = this.formSearch;
      let data = {
        url: "excel/bankDetailExport",
        data: param
      }
      this.ws.send(JSON.stringify(data));
      this.ws.onmessage = e => {
        let res = JSON.parse(e.data)
        if (res.code === 1) {
          this.success(res.message)
          this.Download(res.data);
        } else
          this.error(res.message)
        this.exLoading = false;
      }
    }
  },
  computed: {
    ...mapGetters(["ws", "buttonPermissions","money_type"])
  }
};