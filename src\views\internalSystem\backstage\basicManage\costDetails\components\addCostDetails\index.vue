<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="660px"
    :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
      <el-form-item label="明细编码" prop="cost_detail_no">
        <el-input v-model="ruleForm.cost_detail_no" clearable></el-input>
      </el-form-item>
      <el-form-item label="明细名称" prop="cost_detail_name">
        <el-input v-model="ruleForm.cost_detail_name" clearable></el-input>
      </el-form-item>
      <el-form-item label="费用项目" prop="fk_financial_cost_project_id">
        <el-select v-model="ruleForm.fk_financial_cost_project_id" placeholder="请选择费用项目" class="inputBox" filterable clearable>
          <el-option v-for="item in costProjectList" :key="item.financial_cost_project_id" :label="item.project_no+'-'+item.project_name"
            :value="item.financial_cost_project_id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="明细说明" prop="cost_description">
        <el-input type="textarea" v-model="ruleForm.cost_description" clearable></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm('ruleForm')">保 存</el-button>
      <el-button @click="dialogCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script src="./index.js">

</script>