# trade_erp_web

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Run your tests
```
npm run test
```

### Lints and fixes files
```
npm run lint
```

### 打包压缩
```
node ./config/pack.js
```

### 检测page.json依赖包版本是否可升级 list
```
npm outdated
```

### npm升级最新版本
```
npm update
```


### node版本

```
13.9.0
```

### npm版本

```
6.13.7
```

### node-sass 版本
```
4.13.1
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).
