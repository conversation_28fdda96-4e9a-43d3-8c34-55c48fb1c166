<template>
  <el-table
    :data="tableData"
    border
    align="center"
    :height="tableHeight ? tableHeight : '100%'"
    size="mini"
    :default-expand-all="true"
    :header-cell-style="{ 'font-weight': 'bold', color: '#333333' }"
  >
    <el-table-column type="selection" width="40" v-if="isSel">
    </el-table-column>

    <el-table-column type="expand">
      <template slot-scope="props">
        <el-table :data="props.row.children" stripe style="width: 100%" border>
          <el-table-column prop="brand_name" label="产品" width="400">
          </el-table-column>
          <el-table-column prop="sell_type_name" label="销售类型" width="180">
          </el-table-column>
          <el-table-column prop="contract_amount" label`="合同金额" width="100">
          </el-table-column>
          <!-- <el-table-column
            prop="outbound_money"
            label`="已出库金额"
            width="100"
          >
          </el-table-column> -->
          <el-table-column
            prop="receivables_money"
            label="已收款金额"
            width="120"
          >
          </el-table-column>
          <el-table-column prop="new_money" label="本次分配金额" width="120">
            <template slot-scope="scope">
              {{ !(isDel && isOperation) === true ? 0 : scope.row.new_money }}
            </template>
          </el-table-column>
          <el-table-column prop="percentage" label="分配比例" width="120">
            <template slot-scope="scope">
              {{ scope.row.percentage + "%" }}
            </template>
          </el-table-column>
          <template slot-scope="scope">
            <el-button @click="handleClick(scope.row)" type="danger" size="small" 
              >删除</el-button
            >
          </template>
        </el-table>
      </template>
    </el-table-column>

    <!-- 列表前面 -->
    <template v-for="item in tableList">
      <el-table-column
        v-if="item.layout === '01'"
        :key="item.value"
        :label="item.name"
        :prop="item.value"
        :width="item.width ? item.width : ''"
        :sortable="item.sortable ? item.sortable : false"
      >
        <template slot-scope="scope">
          <el-popover
            placement="bottom"
            title=""
            width="200"
            :open-delay="300"
            trigger="hover"
            :content="
              item.value !== 'sell_type' ? scope.row[item.value] + '' : ''
            "
          >
            <div
              slot="reference"
              class="ellipsis"
              v-if="item.value !== 'sell_type'"
            >
              {{ scope.row[item.value] }}
            </div>

            <!-- <div v-else slot="reference" class="ellipsis">
              <el-select
                v-model="scope.row[item.value]"
                placeholder="请选择销售类型"
                :disabled="true"
                filterable
                clearable
              >
                <el-option
                  v-for="item in sell_type"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </div> -->
          </el-popover>
        </template>
      </el-table-column>
    </template>
    <el-table-column label="收款项目" width="180" prop="project">
      <template slot-scope="scope">
        <el-select
          v-model="scope.row.project"
          placeholder="请选择收款项目"
          filterable
          clearable
          :disabled="!isDel"
        >
          <el-option
            v-for="item in receivables_project"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </template>
    </el-table-column>

    <!-- 列表中间 -->
    <template v-for="item in tableList">
      <el-table-column
        v-if="item.layout === '03'"
        :key="item.value"
        :label="item.name"
        :prop="item.value"
        :width="item.width ? item.width : ''"
        :sortable="item.sortable ? item.sortable : false"
      >
        <template slot-scope="scope">
          <el-popover
            placement="bottom"
            title=""
            width="200"
            :open-delay="300"
            trigger="hover"
            :content="scope.row[item.value] + ''"
          >
            <div slot="reference" class="ellipsis">
              {{ scope.row[item.value] }}
            </div>
          </el-popover>
        </template>
      </el-table-column>
    </template>
    <el-table-column label="收款金额" width="180" prop="amount">
      <template slot-scope="scope">
        <el-input
          v-model="scope.row.amount"
          :disabled="!isDel"
          @change="changeAmount(scope.row)"
        >
        </el-input>
      </template>
    </el-table-column>

    <el-table-column label="到款日期" width="180" prop="confirm_money_time">
      <template slot-scope="scope">
        <my-date
          v-model="scope.row.confirm_money_time"
          :isAllDate="false"
          hint="请选择到款日期"
          :disabled="!isDel"
        >
        </my-date>
      </template>
    </el-table-column>

    <!-- 列表后面 -->
    <template v-for="item in tableList">
      <el-table-column
        v-if="item.layout === '02'"
        :key="item.value"
        :label="item.name"
        :prop="item.value"
        :width="item.width ? item.width : ''"
        :sortable="item.sortable ? item.sortable : false"
      >
        <template slot-scope="scope">
          <el-popover
            placement="bottom"
            title=""
            width="200"
            :open-delay="300"
            trigger="hover"
            :content="scope.row[item.value] + ''"
          >
            <div
              slot="reference"
              class="ellipsis"
              v-if="item.value !== 'sell_type'"
            >
              {{ scope.row[item.value] }}
            </div>
          </el-popover>
        </template>
      </el-table-column>
    </template>
    <el-table-column label="收款备注" width="180" prop="confirm_money_remarks">
      <template slot-scope="scope">
        <el-input v-model="scope.row.confirm_money_remarks" :disabled="!isDel">
        </el-input>
      </template>
    </el-table-column>
    <!-- <el-table-column label="单据备注" width="180" prop="receivables_remarks">
      <template slot-scope="scope">
        <el-input v-model="scope.row.receivables_remarks"> </el-input>
      </template>
    </el-table-column> -->
    <!-- <el-table-column label="凭证号码" width="180" prop="voucher_num">
      <template slot-scope="scope">
        <el-input v-model="scope.row.voucher_num"> </el-input>
      </template>
    </el-table-column> -->

    <el-table-column
      fixed="right"
      label="操作"
      :width="handleWidth"
      v-if="isDel && isOperation"
    >
      <template slot-scope="scope">
        <!-- <el-button
          @click="four(scope.row)"
          v-permit="isFour"
          v-if="isFour"
          type="text"
          size="small"
          class="success ml30"
          >{{ fourTitle }}
        </el-button> -->
        <el-button
          @click="del(scope.row)"
          type="text"
          size="small"
          class="danger ml30"
          >删除</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>
<script src="./index.js"></script>
<style scoped lang="scss">
.danger {
  color: #f56c6c;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
@import "@/assets/css/element/font-color.scss";
</style>