import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  sortTable
} from "@/common/global/vxeTableDrag.js";
export default {
  name: "tableView",
  components: {
    MyDate

  },
  props: {
    tableData: {
      default () {
        return {}
      },
      type: Array
    },
    tableList: {
      default () {
        return {}
      },
      type: Array
    },
    isEdit: {
      default: '',
      type: String
    },
    isDel: {
      default: '',
      type: String
    },
    isSel: {
      default: false,
      type: Boolean
    },
    tableHeight: {
      type: Number
    },
    isThrid: {
      default: '',
      type: String
    },
    thridTitle: {
      type: String
    },
    isFour: {
      default: '',
      type: String
    },
    fourTitle: {
      type: String
    },
    handleWidth: {
      type: Number,
      default: 120
    },
    isOperation: { //是否显示操作
      type: Boolean,
      default: true
    },
    //是否双击
    isDblclick: {
      type: Boolean,
      default: false
    },
    isOrder: {
      type: <PERSON>olean,
      default: false
    }
  },
  data() {
    return {
      tabClickIndex: null, // 点击的单元格
      tabClickLabel: "", // 当前点击的列名
      success: "",
      sortable: ""
    }
  },
  mounted() {
    this.columnDrop()
  },
  beforeDestroy() {
    if (this.sortable) {
      this.sortable.destroy();
    }
  },
  methods: {
    columnDrop() {
      this.$nextTick(() => {
        let xTable = this.$refs.xTable;
        this.sortable = sortTable(xTable);
      });
    },
    //自定义键盘事件
    keyDownMethods(data) {
      const keyCodes = ["Tab", "Enter", "NumpadEnter"];
      if (keyCodes.includes(data.$event.code)) {
        this.$nextTick(() => {
          // setTimeout(() => {
            if (keyCodes.includes(data.$event.code)) {
              let cellInfo = this.$refs.xTable.getSelectedCell();

              if (!cellInfo || !cellInfo.row || !cellInfo.column) {
                // this.$refs.xTable.insertAt(this.tableRecord, -1);
                return;
              }
              let {
                row,
                column
              } = cellInfo;
              this.$refs.xTable.setActiveCell(row, column.property);
            }
          }, 100);
        // });
      }
    },
    del(item) {
      let num = 0
      for (let index = 0; index < this.tableData.length; index++) {
        if (this.tableData[index]['_XID'] === item['_XID']) {
          num = index
        }
      }
      this.tableData.splice(num, 1)
    },
    leaveClick() {
      this.tabClickIndex = null
      this.tabClickLabel = null
    },

    getData() {
      this.$refs.xTable.clearActived()
      return this.$refs.xTable.getTableData().fullData
    }


  }
};