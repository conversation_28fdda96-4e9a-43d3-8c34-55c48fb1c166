<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="660px"
    :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
      <el-form-item label="项目编码" prop="project_no">
        <el-input v-model="ruleForm.project_no" clearable></el-input>
      </el-form-item>
      <el-form-item label="项目名称" prop="project_name">
        <el-input v-model="ruleForm.project_name" clearable></el-input>
      </el-form-item>
      <el-form-item label="项目备注" prop="remark">
        <el-input type="textarea" v-model="ruleForm.remark" clearable></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm('ruleForm')">保 存</el-button>
      <el-button @click="dialogCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script src="./index.js">

</script>