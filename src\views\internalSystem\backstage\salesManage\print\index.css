.head-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    height: 20px;
    border-bottom: 1px solid #ccc
}

.table-left {
    float: left;
    width: 33%;
}

.demo {
    line-height: 26px;
    margin-top: 10px;
}

.demo .input-left {
    font-size: 14px;
    height: 26px;
    line-height: 26px;
    color: #333333;
    float: left;
}

.demo .input-left-1 {
    width: 90px;
    font-size: 14px;
    height: 26px;
    line-height: 26px;
    color: #333333;
    margin: 0 20px 0 20px;
    text-align: right;
}

.demo .input-text {
    width: 60%;
    height: 26px;
    border: 1px solid #e5e5e5;
}

.img-add {
    height: 5px;
    line-height: 120px;
    margin-top: 20px;
}

.img-add .img-left {
    width: 90px;
    font-size: 14px;
    height: 26px;
    line-height: 26px;
    color: #333333;
    float: left;
    margin: 0 20px 0 20px;
    text-align: right;
}

.img-add .img {
    width: 120px;
    height: 120px;
    float: left;
    border: 1px solid #e5e5e5;
}

.img-add .img-btn {
    width: 80px;
    height: 28px;
    line-height: 28px;
    color: #fff;
    background-color: #259bef;
    float: left;
    margin: 94px 0 0 20px;
    border: none;
    cursor: pointer;
}

.select {
    height: 26px;
    width: 40%;
}

.table_content {
    margin-top: 20px;
}

.table_content table {
    border: 0;
    border-collapse: collapse;
    font-size: 12px;
}

.table_content table td:hover {
    background-color: #C1EBFF;
}

.table_content table td {
    height: 26px;
    word-wrap: break-word;
    max-width: 200px;
    vertical-align: middle;
    border: 1px solid #b8d8e5;
    background-color: #fbfeff;
}

textarea {
    line-height: 1.1;
    resize: none;
    /*outline:none;*/
    overflow: hidden;
    border: 0px solid;
    width: 100%;
}

.textareas {
    line-height: 1.2;
    height: 20px;
}

.textareas_td {
    line-height: 22px;
    /* height: 22px; */
    text-align: center;
    vertical-align: center;
    
}

.textareas_td_t {
    line-height: 32px;
    height: 32px;
    text-align: center;
}


input {
    outline: none;
}

td {
    line-height: 20px;
    text-align: center;
}

th {
    line-height: 24px;
    text-align: center;
}

.textareas_td_little {
    line-height: 25px;
    height: 25px;
}

/*.functional-condition{
    position: relative;
    width: 9px;
    display: inline-block;
    margin: 5px;
}*/
input[type="checkbox"] {
    width: 15px;
    height: 15px;
}

.functional-condition {
    font-size: 16px;
}


.wrapper {
    width: 100%;
    margin: 0 auto;
}

td {
    line-height: 16px;
    text-align: center
}

.textareas_center {
    text-align: center;
}

.update {
    width: 850px;
    margin: 0 auto;
}

.select {
    width: 850px;
    margin: 0 auto;
}

.imgDiv {
    width: 850px;
    margin: 0  auto;
    position: relative;
}

img {
    position: absolute;
    right: 50px;
    bottom: 0px;
    z-index: 999;
    width: 180px;
    height: 180px;
}
td, th {
    padding: 0 2px;
    border:2px solid black;
}

.button_fixed {
    position: fixed;
    right: 20px;
    top: 10px;
}