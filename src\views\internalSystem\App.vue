<template>
	<div id="App" class="internalSystem">
		<router-view/>
	</div>
</template>

<script>
	import {mapMutations} from "vuex"
	import API from "@/api/internalSystem/common/index"
	
	export default {
		mounted() {
			this.SET_ENV('internalSystem')
		},
		created() {
			// this.loginOut()
		},
		methods: {
			loginOut() {
				API.loginOut({}).then(() => {
				}).catch(() => {
				})
			},
			...mapMutations(['SET_ENV'])
		}
	};
</script>
<style scoped lang="scss">
	#App {
		width: 100%;
		height: 100vh;
		overflow: hidden;
	}
	
	@media print {
		@page {
			size: auto;
			margin: 1cm 1.5cm;
		}
	}
</style>
