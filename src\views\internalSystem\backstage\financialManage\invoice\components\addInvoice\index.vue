<template>
  <div
    class="body-p10 orderH100"
    style="overflow-y: auto; overflow-x: hidden"
    v-if="dialogVisible"
  >
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button
        v-if="!ruleForm.financial_cost_invoice_id"
        type="primary"
        @click="submitForm('ruleForm')"
        :loading="loading"
        >保 存</el-button
      >
      <el-button
        type="primary"
        @click="back"
        v-permit="'AUDIT_BACK_INVOICE_NEW'"
        v-if="
          ruleForm.financial_cost_invoice_id &&
          (this.ruleForm.approval_state == 1 ||
            this.ruleForm.approval_state == 2)
        "
      >
        退回审核</el-button
      >
    </div>
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="160px"
      class="mt10 flexAndFlexColumn"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单据编号" prop="invoice_no">
            <el-input
              v-model="ruleForm.invoice_no"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据日期" prop="invoice_date">
            <el-input
              v-model="ruleForm.invoice_date"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" class="formItem2">
          <el-form-item label="操作员" prop="add_user_name">
            <el-input
              v-model="ruleForm.add_user_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="操作部门" prop="department_name">
            <el-input
              v-model="ruleForm.department_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8"  class="formItem2">
          <el-form-item label="购销单位" prop="sales_unit_id_format">
            <el-select :disabled="!!ruleForm.financial_cost_invoice_id" v-model="ruleForm.invoice_unit" placeholder="请选择购销单位" class="inputBox" filterable
              clearable>
              <el-option v-for="item in companyList" :key="item.sales_unit_id" :label="item.company_name"
                :value="item.sales_unit_id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="单据备注" prop="invoice_remark">
            <el-input
              :disabled="!!ruleForm.financial_cost_invoice_id"
              v-model="ruleForm.invoice_remark"
              clearable
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <div>
        <el-button
          v-if="!ruleForm.financial_cost_invoice_id"
          type="primary"
          @click="add"
          >新增
        </el-button>
      </div>

      <table-custom
        ref="tableCustom"
        class="mt10 tableContent"
        :obj="obj"
        :tableCol="tableCol"
        :isDel="!ruleForm.financial_cost_invoice_id"
      />
    </el-form>
  </div>
</template>
<script src="./index.js">
</script>

<style lang="scss" scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown + .el-dropdown {
  margin-left: 15px;
}
@import "@/assets/css/element/font-color.scss";
</style>