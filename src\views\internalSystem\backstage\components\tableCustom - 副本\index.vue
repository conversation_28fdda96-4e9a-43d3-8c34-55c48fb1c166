<template>
  <div>
    <el-form :model="form" ref="form">
      <el-table
        :data="form.tableData"
        border
        stripe
        align="center"
                header-align="center"
        :header-cell-style="{ 'font-weight': 'bold', color: '#333333' }"
      >
        <template v-for="(item, index) in tableCol">
          <el-table-column
            :prop="item.prop"
            :key="index"
            :label="item.label"
            :width="item.width ? item.width : ''"
            v-if="!item.isHide"
            align="center"
          >
            <template slot="header">
              <div>
                <span v-if="item.need" style="color: red">*</span
                >{{ item.label }}
              </div>
            </template>
            <template slot-scope="scope">
              <!-- 下拉框 -->
              <div v-if="scope.row[item.prop].type == 'select'">
                <el-form-item
                  :prop="`tableData.${scope.$index}.${item.prop}.value`"
                  :rules="item.need ? rules.select : []"
                >
                  <el-select
                    v-model="scope.row[item.prop].value"
                    style="width: 100%"
                    :disabled="scope.row[item.prop].disabled"
                    clearable
                    @change="
                      selectChange(
                        scope.row[item.prop].option,
                        scope.row[item.prop].value,
                        item.prop,
                        scope
                      )
                    "
                  >
                    <el-option
                      v-for="(item2, index) in scope.row[item.prop].option"
                      :key="index"
                      :label="item.isJoin ? item2.label + '%' : item2.label"
                      :value="
                        item2.value ||
                        (item2.value !== 0 && item2.value !== false
                          ? item2.id
                          : item2.value)
                      "
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <!-- 输入框 -->
              <div v-else-if="scope.row[item.prop].type == 'input'">
                <el-form-item
                  :prop="`tableData.${scope.$index}.${item.prop}.value`"
                  :rules="item.need ? rules.input : []"
                >
                  <el-input
                    v-model="scope.row[item.prop].value"
                    :disabled="scope.row[item.prop].disabled"
                    style="width: 100%"
                    clearable
                  ></el-input>
                </el-form-item>
              </div>
              <div v-else-if="scope.row[item.prop].type == 'date'">
                <el-form-item
                  :prop="`tableData.${scope.$index}.${item.prop}.value`"
                  :rules="item.need ? rules.select : []"
                >
                  <my-date
                    v-model="scope.row[item.prop].value"
                    style="width: 100%"
                    :disabled="scope.row[item.prop].disabled"
                  ></my-date>
                </el-form-item>
              </div>
              <!-- 数字输入框 -->
              <div v-else-if="scope.row[item.prop].type == 'number'">
                <el-form-item
                  :prop="`tableData.${scope.$index}.${item.prop}.value`"
                  :rules="item.need ? rules.number : []"
                >
    
                  <el-input
                    
                    v-model.number="scope.row[item.prop].value"
                    @input="
                      (e) => {
                        item.fn ? priceFn(e, item, scope.row) : '';
                      }
                    "
                    :disabled="scope.row[item.prop].disabled"
                    style="width: 100%"
                    clearable
                  ></el-input>
                </el-form-item>
              </div>
              <div v-else-if="scope.row[item.prop].type == 'float'">
                <el-form-item
                  :prop="`tableData.${scope.$index}.${item.prop}.value`"
                  :rules="item.need ? rules.float : []"
                >
                  <el-input
                    
                    v-model="scope.row[item.prop].value"
                    placeholder="请填写"
                    @input="
                      (e) => {
                        item.fn ? priceFn(e, item, scope.row) : '';
                      }
                    "
                    :disabled="scope.row[item.prop].disabled"
                    style="width: 100%"
                    clearable
                  ></el-input>
                </el-form-item>
              </div>
              <div v-else-if="scope.row[item.prop].type == 'dialog'">
                <el-form-item
                  :prop="`tableData.${scope.$index}.${item.prop}.value`"
                  :rules="item.need ? rules.dialog : []"
                >
                  <el-input
                    v-model="scope.row[item.prop].value"
                    :disabled="scope.row[item.prop].disabled"
                    placeholder="请点击选择"
                    style="width: 100%"
                    @focus="choose(scope.row[item.prop].dialogType, scope)"
                    readonly
                    clearable
                  >
                  </el-input>
                </el-form-item>
              </div>
              <div v-else>{{ scope.row[item.prop] }}</div>
              <!-- <div>{{JSON.stringify(scope.row[item.prop])}}{{item.prop}}</div> -->
            </template>
          </el-table-column>
        </template>
        <el-table-column fixed="right" label="操作" width="80" v-if="isDel" align="center">
          <template slot-scope="scope">
            <el-button
              @click="del(scope)"
              type="text"
              size="small"
              class="danger ml30"
              
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <bank-list ref="bankList" dialogTitle="银行列表" @getInfo="getBankInfo" />
    <cost-list
      ref="costList"
      dialogTitle="费用明细列表"
      @getInfo="getCostInfo"
    />
    <employee-list
      ref="employeeList"
      dialogTitle="员工列表"
      @getInfo="getEmployeeInfo"
    />
  </div>
</template>
<script src="./index.js"></script>

<style lang="scss" scoped>
@import "@/assets/css/element/font-color.scss";
// .el-form-item--small.el-form-item{
//       margin-bottom: 6px;
// }
</style>