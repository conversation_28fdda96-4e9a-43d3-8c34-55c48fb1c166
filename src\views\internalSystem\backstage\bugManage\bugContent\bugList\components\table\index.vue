<template>
  <div class="tableContainer">
    <vxe-toolbar
      id="buglist"
      :custom="{ storage: true, immediate: true }"
      :resizable="{ storage: true }"
      class="hiddenButton"
      title="点击可隐藏列表项目"
    >
    </vxe-toolbar>
    <vxe-table
      column-key
      :border="false"
      resizable
      show-overflow
      show-header-overflow
      highlight-hover-row
      highlight-current-row
      auto-resize
      ref="xTable"
      min-height="500"
      :loading="loading"
      align="center"
      header-align="center"
      @cell-click="selectCheck"
      @select-change="getSelectRecords"
      @radio-change="getRadioRow"
      @select-all="getSelectRecords"
      @cell-dblclick="dblclick"
      :checkbox-config="{
        reserve: true,
        showHeader: true,
      }"
      :data="tableData"
      size="small"
      :keyboard-config="{ isArrow: true }"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
    >
      <!-- <vxe-table-column
        type="checkbox"
        align="center"
        width="40"
      ></vxe-table-column> -->
      <vxe-table-column field="problem_id" title="ID" width="60" sortable>
        <template v-slot="{ row }">
          <div class="a" @click="toDetail(row)">
            {{ row.problem_id || 0 }}
          </div>
        </template>
      </vxe-table-column>
      <!-- 级别 -->
      <vxe-table-column
        field="problem_severity"
        title="级别"
        width="60"
        sortable
      >
        <template v-slot="{ row }">
          <div
            class="radus"
            :class="[row.problem_severity === 1 ? 'danger' : '']"
          >
            {{ row.problem_severity || 0 }}
          </div>
        </template>
      </vxe-table-column>
      <!-- 优先级 -->
      <vxe-table-column
        field="problem_priority"
        title="优先级"
        width="100"
        sortable
      >
        <template v-slot="{ row }">
          <div class="radus yxj">{{ row.problem_priority || 0 }}</div>
        </template>
      </vxe-table-column>
      <vxe-table-column field="title" title="BUG标题" align="left" sortable>
        <template v-slot="{ row }">
          <div class="bugtitle" :title="row.problem_title">
            <span :class="[row.is_confirm === 1 ? 'success' : '']"
              >[{{ row.is_confirm_name }}]</span
            >
            <span class="title" @click="toDetail(row)">{{
              row.problem_title
            }}</span>
          </div>
        </template>
      </vxe-table-column>
      <vxe-table-column field="add_user_name" title="创建" width="100" sortable>
      </vxe-table-column>
      <vxe-table-column field="add_time" title="创建日期" width="100" sortable>
      </vxe-table-column>
      <vxe-table-column
        field="designated_person_name"
        title="指派给"
        width="120"
        sortable
      >
        <template v-slot="{ row }">
          <div>
            <span
              :class="[
                row.designated_person_name === userInfo.fullName
                  ? 'fz-danger'
                  : '',
              ]"
              >{{ row.designated_person_name }}</span
            >
          </div>
        </template>
      </vxe-table-column>
      <vxe-table-column field="deadline" title="截止日期" width="120" sortable>
      </vxe-table-column>
      <vxe-table-column
        field="solve_person_name"
        title="解决"
        width="120"
        sortable
      >
      </vxe-table-column>
      <vxe-table-column field="solution_name" title="方案" width="120" sortable>
      </vxe-table-column>
      <vxe-table-column title="操作" v-if="!outside" :width="200" fixed="right">
        <template v-slot="{ row }">
          <div class="operation">
            <i
              class="el-icon-search icon"
              :class="row.is_confirm === 1 ? 'disabled' : ''"
              @click="confirm(row)"
              title="确认"
            ></i>
            <i
              class="el-icon-thumb icon iconRight"
              :class="[3].includes(row.problem_state) ? 'disabled' : ''"
              @click="assign(row)"
              title="指派"
            ></i>
            <i
              class="el-icon-circle-check icon"
              :class="[2, 3].includes(row.problem_state) ? 'disabled' : ''"
              @click="solve(row)"
              title="解决"
            ></i>
            <i
              class="el-icon-circle-close icon"
              :class="row.problem_state !== 2 ? 'disabled' : ''"
              @click="close(row)"
              title="关闭"
            ></i>
            <i
              class="el-icon-edit icon"
              :class="[3].includes(row.problem_state) ? 'disabled' : ''"
              @click="edit(row)"
              title="编辑"
            ></i>
            <i
              class="el-icon-document-copy icon"
              @click="copy(row)"
              title="复制Bug"
            ></i>
          </div>
        </template>
      </vxe-table-column>
    </vxe-table>
    <Pagination ref="pagination" @success="getList" />
    <Dialog ref="dialog" :type="stateType" @success="dialogSuccess" />
    <!-- this.$refs.brand_pagination.obtain() -->
    <!-- this.$refs.brand_pagination.setTotal(res.totalCount); -->
  </div>
</template>

<script>
import { sortTable } from "@/common/global/vxeTableDrag.js";
import { mapGetters } from "vuex";
import API from "@/api/internalSystem/bugManage/bugContent/index.js";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import Dialog from "../../../components/dialog/index.vue";
export default {
  props: {
    outside: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      tableData: [],
      params: {},
      stateType: "",
    };
  },
  mounted() {
    this.columnDrop();
  },
  beforeDestroy() {
    if (this.sortable) {
      this.sortable.destroy();
    }
  },
  methods: {
    //列拖拽
    columnDrop() {
      this.$nextTick(() => {
        let xTable = this.$refs.xTable;
        this.sortable = sortTable(xTable);
      });
    },
    SetParams(params) {
      this.params = params;
      this.getList();
    },
    getList() {
      let params = {};
      Object.assign(params, this.params, this.$refs.pagination.obtain());
      API.getBugList(params).then((res) => {
        this.tableData = res.data;
        this.$refs.pagination.setTotal(res.totalCount);
      });
    },
    getSelectRecords(selectRecord = {}) {
      //当前改变的row  row.row
      if (selectRecord.checked && selectRecord.row) {
        selectRecord.row.checked = true;
        this.$emit("click", selectRecord.row);
      }

      //获取多选框选中的数据getSelectRecords
      let records = [];
      records =
        this.$refs.xTable.getSelectRecords(); /* 用于多选行，获取已选中的行数据 */
      this.$emit("getSelectRecords", records);
    },
    getRadioRow() {
      //获取单选框选中的数据
      this.selectRecords = this.$refs.xTable.getRadioRow();

      this.$emit("getSelectRecords", this.selectRecords);
    },
    getCellFixedDemcial(field, value) {
      // return getFixedDemical(field, value);
      return value;
    },
    selectCheck({ row, column }) {
      // @cell-click="selectCheck"  头部加这个生效
      // this.$emit("click", row);
      if (column.type == "checkbox") return;

      //   if (this.isRadio && this.rowClick) {
      //     this.$refs.xTable.setRadioRow(row);
      //     this.getRadioRow();
      //   }
      //   if (this.isCheck && this.rowClick) {
      this.$refs.xTable.toggleRowSelection(row);
      this.getSelectRecords();
      //   }
    },
    checkMethod({ row }) {
      return !row.checked;
    },

    GetFullTableData() {
      return this.$refs.xTable.getTableData().fullData;
    },

    dblclick({ row }) {
      //单元格双击事件
      this.$emit("dblclick", row);
    },
    confirm(row) {
      //已关闭
      if (row.problem_state === 3) {
        return;
      }
      if (row.is_confirm === 1) return;

      this.stateType = 2;
      this.$refs.dialog.Show(row.problem_id);
    },
    assign(row) {
      //已关闭
      if (row.problem_state === 3) {
        return;
      }
      this.stateType = 1;
      this.$refs.dialog.Show(row.problem_id);
    },
    solve(row) {
      //已关闭
      if (row.problem_state === 3) {
        return;
      }
      if (row.problem_state === 2) return;
      this.stateType = 3;
      this.$refs.dialog.Show(row.problem_id);
    },
    close(row) {
      //已关闭
      if (row.problem_state === 3) {
        return;
      }
      if (row.problem_state !== 2) return;
      this.stateType = 4;
      this.$refs.dialog.Show(row.problem_id);
    },
    edit(row) {
      //已关闭
      if (row.problem_state === 3) {
        return;
      }
      this.$router.push({
        path: "/backstage/bugManage/bugContent/addBug",
        query: {
          problem_id: row.problem_id,
          type: "edit",
        },
      });
    },
    copy(row) {
      this.$router.push({
        path: "/backstage/bugManage/bugContent/addBug",
        query: {
          problem_id: row.problem_id,
          type: "copy",
        },
      });
    },
    toDetail(row) {
      let path = "/backstage/bugManage/bugContent/bugDetail";
      if (this.outside) {
        path = "/outside/outsideBugDetail";
      }
      this.$router.push({
        path,
        query: {
          problem_id: row.problem_id,
        },
      });
    },
    dialogSuccess() {
      this.getList();
    },
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  components: {
    Pagination,
    Dialog,
  },
};
</script>

<style lang="scss" scoped>
.a {
  cursor: pointer;
  color: #03c;
}
.operation {
  .icon {
    font-size: 18px;
    color: rgb(79, 115, 206);
    cursor: pointer;
    margin: 0 3px;
  }
  .iconRight {
    transform: rotate(90deg);
  }
  .disabled:before {
    color: #ccc;
    cursor: not-allowed;
  }
}
.radus {
  border-radius: 50%;
  height: 18px;
  width: 18px;
  line-height: 18px;
  background-color: #db7c12;
  color: #fff;
  display: inline-block;
  //   display: flex;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  align-items: center;
  box-sizing: border-box;
}
.yxj {
  background-color: #fff;
  color: #db7c12;
  border: 1px solid #db7c12;
}
.danger {
  background-color: #d71319;
}
.fz-danger {
  color: #d71319;
}
.success {
  color: green;
}
.border-danger {
  border-color: #d71319;
  color: #d71319;
}
.wenhao {
  border: 2px solid #ccc;
  background-color: #fff;
  color: #ccc;
  font-weight: bold;
}

.bugtitle {
  span {
    color: gray;
    font-size: 9px;
  }
  .success {
    color: green;
  }
  .title {
    color: #03c;
    cursor: pointer;
  }
}
</style>

<style scoped lang="scss">
.tableContainer {
  width: 100%;
  height: 100%;

  position: relative;
  // user-select: none;
  // overflow: auto;
  .hiddenButton {
    position: absolute;
    top: -35px;
    right: 0;
    // width: 32px;
    height: 32px;

    &.vxe-toolbar {
      padding: 0;
    }

    .vxe-button--wrapper {
      display: none;
    }
  }
}
</style>
