<template>
  <div class="container">
    <Detail class="body" outside />
  </div>
</template>

<script>
import Detail from "@/views/internalSystem/backstage/bugManage/bugContent/bugDetail/index.vue";
export default {
  components: {
    Detail,
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
 overflow: auto;
  .body {
    width: 1200px;
    margin: 10px auto;
    height: auto;
  }
 
}
</style>
