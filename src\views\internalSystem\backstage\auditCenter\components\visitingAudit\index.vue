<template>
  <div class="body-p10" style="overflow-y:auto;overflow-x: hidden;" v-if="dialogVisible">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="openAudit">审 核</el-button>
    </div>
    <el-form :model="ruleForm" ref="ruleForm" label-width="200px" class="mt10">
      <span class="visitingTitle">一、客户基础信息</span>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input disabled v-model="ruleForm.customer_name" placeholder="请点击选择客户" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input disabled v-model="ruleForm.phone"  clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="回访方式" prop="visiting_form">
            <el-select disabled v-model="ruleForm.visiting_form" placeholder="请选择回访方式" filterable clearable>
              <el-option v-for="item in sales_visiting_form" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系时间" prop="contact_time">
            <el-input disabled v-model="ruleForm.contact_time" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="销售员">
            <el-input disabled v-model="ruleForm.fk_sale_employee_name" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="QQ号码" prop="qq">
            <el-input disabled v-model="ruleForm.qq"  clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系人" prop="link_man">
            <el-input disabled v-model="ruleForm.link_man" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="微信号码" prop="we_chat">
            <el-input disabled v-model="ruleForm.we_chat" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <span class="visitingTitle">二、系统运行情况</span>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="数据备份是否完整" prop="dateBackupState">
            <el-select disabled v-model="ruleForm.dateBackupState" placeholder="请选择数据备份是否完整" filterable clearable>
              <el-option v-for="item in confirmNormal" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="应收数据是否准确" prop="receiveDataState">
            <el-select disabled v-model="ruleForm.receiveDataState" placeholder="请选择应收数据是否准确" filterable clearable>
              <el-option v-for="item in accurate" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="是否需交维护费" prop="isHandMaintenance">
            <el-select disabled v-model="ruleForm.isHandMaintenance" placeholder="请选择是否需交维护费" filterable clearable>
              <el-option v-for="item in whether" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="库存是否准确" prop="inventoryState">
            <el-select disabled v-model="ruleForm.inventoryState" placeholder="请选择库存是否准确" filterable clearable>
              <el-option v-for="item in accurate" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="应付数据是否准确" prop="payDataState">
            <el-select disabled v-model="ruleForm.payDataState" placeholder="请选择应付数据是否准确" filterable clearable>
              <el-option v-for="item in accurate" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="航天开票接口使用是否正常" prop="invoicePortState">
            <el-select disabled v-model="ruleForm.invoicePortState" placeholder="请选择航天开票接口使用是否正常" filterable clearable>
              <el-option v-for="item in normal" :key="item.value" :label="item.name" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <span class="visitingTitle">三、本次跟进内容</span>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="16">
          <el-form-item label="" prop="follow_content">
            <el-input disabled type="textarea" placeholder="请输入跟进内容" v-model="ruleForm.follow_content" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <span class="visitingTitle">四、客户现有功能模块</span>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="16">
          <el-form-item label="" prop="use_describe">
            <el-checkbox-group disabled v-model="checkSoftware">
              <el-checkbox v-for="item in softwareList" :key="item.value" :label="item.value" name="type">{{item.name}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
      <span class="visitingTitle">五、客户提的需求/bug</span>
      <el-row :gutter="20" class="mt20">
        <el-col :xl="16">
          <el-form-item label="" prop="customer_demand">
            <el-input disabled type="textarea" placeholder="请输入客户提的需求/bug" v-model="ruleForm.customer_demand" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog title="审核" :visible.sync="dialogVisibleAudit" append-to-body @close="dialogCancelAudit" width="660px"
      :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
      <el-form :model="auditForm" :rules="rules" ref="auditForm" label-width="100px">
        <!-- <el-form-item label="审核状态" prop="auditState">
          <el-select v-model="auditForm.auditState" placeholder="请选择审核状态" filterable clearable>
            <template v-for="item in contract_auditStateList">
            <el-option  :key="item.id" :label="item.label" :value="item.value">
            </el-option>
            </template>
          </el-select>
        </el-form-item> -->
        <el-form-item label="审核状态" prop="auditState">
          <el-radio-group v-model="auditForm.auditState">
            <template v-for="item in contract_auditStateList">
              <el-radio
                :key="item.id"
                :label="item.value"
                :value="item.value"
                border
                style="height: 30px;"
                ><span style="font-size:16px;">{{ item.label }}</span></el-radio
              >
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" clearable></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('auditForm')" :loading="loading">保 存</el-button>
        <el-button @click="dialogCancelAudit">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script src="./index.js">

</script>

<style lang="scss" scoped>
  .visitingTitle {
    font-size: 18px;
    font-weight: bold;
    margin-top: 20px;
  }
</style>