import API from "@/api/jiqin_server/index.js";
export default {
  data() {
    return {
      dialogVisible: false,
      loading: false,
      dialogTitle: "快递信息查询",
      formLabelWidth: "120px",
      tableData: [],
      activities: []
    };
  },
  methods: {
    Show(orderNo = null) {
      if(!orderNo) return this.error("快递单号错误，请联系管理员")
      this.dialogVisible = true;
      this.getExpressInformation(orderNo);
    },
    Cancel() {
      this.dialogVisible = false;
      this.activities = [];
    },
    save() {
      //   const loading = this.$loading({
      //     lock: true,
      //     text: "正在保存...",
      //     background: "rgba(0, 0, 0, 0.7)",
      //     target: document.querySelector(".el-dialog__body")
      //   });
      //   let _this = this;
      //   let params = {
      //     parentDepartmentId: _this.parentDepartmentId,
      //     departmentName: _this.ruleForm.departmentName,
      //     departmentId: _this.reviseId,
      //   };
      //   API.addDepartment(params)
      //     .then(() => {
      //       this.Cancel();
      //       this.$emit("getDepartmentList");
      //     })
      //     .catch(() => {})
      //     .finally(() => {
      //       loading.close();
      //     });
    },
    
    getExpressInformation(orderNo) {
      //根据快递单号获取对应快递信息
      //   1582534900
      let params = { order_no: orderNo };
      this.loading=true;
      API.getExpressInformation(params).then(res => {
        res.data.context.forEach(item=>{
            item.date=this.forMatTime(item.time)
        })
        this.activities = res.data.context;
      }).catch(() => {}).finally(()=>{
        this.loading=false;
      })
    },
    forMatTime(timeStamp) {
        //时间戳10位需乘1000，14位就不用乘
        timeStamp=timeStamp.length===14?timeStamp:timeStamp*1000;
      var time = new Date(timeStamp);
      var y = time.getFullYear();
      var m = time.getMonth() + 1;
      var d = time.getDate();
      var h = time.getHours();
      var mm = time.getMinutes();
      var s = time.getSeconds();
      return (
        y +
        "-" +
        this.add0(m) +
        "-" +
        this.add0(d) +
        " " +
        this.add0(h) +
        ":" +
        this.add0(mm) +
        ":" +
        this.add0(s)
      );
    },
    add0(m) {
      return m < 10 ? "0" + m : m;
    }
  }
};
