<template>
  <div
    class="body-p10 orderH100"
    style="overflow-y: auto; overflow-x: hidden"
    v-if="dialogVisible"
  >
    <div>
      <el-button @click="dialogCancel(true)">关 闭</el-button>
      <el-button type="primary" @click="dialogCancel(false)">新增</el-button>
      <el-button
        :disabled="
          !((isEdit && isOwn && ruleForm.ticket_id) || !ruleForm.ticket_id)
        "
        type="primary"
        @click="submitForm('ruleForm')"
        :loading="loading"
        >保 存
      </el-button>
      <el-button :disabled="!!ruleForm.ticket_id" type="primary" @click="callIn"
        >调入合同
      </el-button>
      <el-button
        :disabled="
          ruleForm.audit_state === 1 ||
            ruleForm.audit_state === 0 ||
            !ruleForm.ticket_id
        "
        type="danger"
        @click="del(ruleForm)"
        :loading="loading"
        >删除
      </el-button>
      <el-button
        v-permit="'SEND_OPENTICKET_NEW'"
        :disabled="!(ruleForm.ticket_id && ruleForm.audit_state == 4 && isOwn)"
        type="primary"
        @click="send"
        :loading="loading"
        >发出</el-button
      >
      <el-button
        v-permit="'AUDIT_BACK_OPENTICKET_NEW'"
        :disabled="
          !(
            ruleForm.ticket_id &&
            ruleForm.audit_state !== 0 &&
            ruleForm.audit_state !== 4
          )
        "
        type="primary"
        @click="back"
        :loading="loading"
        >回退</el-button
      >
      <el-button
        v-permit="'FILE_OPENTICKET_NEW'"
        :disabled="!ruleForm.ticket_id"
        type="primary"
        @click="addFile"
        >附件
      </el-button>
      <!-- <el-dropdown @command="handleCommand" class="ml5" v-if="ruleForm.ticket_id">
        <el-button type="primary"> 打印合同</el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="update">修改模板</el-dropdown-item>
          <el-dropdown-item command="select">选择模板</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->
    </div>
    <el-form
      :model="ruleForm"
      ref="ruleForm"
      :rules="rules"
      label-width="100px"
      class="mt10 flexAndFlexColumn mr10 ml5"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单据编号" prop="ticket_no">
            <el-input
              v-model="ruleForm.ticket_no"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据日期" prop="ticket_date">
            <el-input
              v-model="ruleForm.ticket_date"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="操作员" prop="update_user_name">
            <el-input
              v-model="ruleForm.update_user_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户编号" prop="customer_no">
            <el-input
              v-model="ruleForm.customer_no"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="销售员" prop="fk_sale_employee_name">
            <el-input
              v-model="ruleForm.fk_sale_employee_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="销货单位" prop="sales_unit_id_format">
            <el-input
              disabled
              v-model="ruleForm.sales_unit_id_format"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input
              disabled
              v-model="ruleForm.customer_name"
              clearable
              type="textarea"
              :rows="2"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="纳税税号" prop="customer_tax_number">
            <el-input
              v-model="ruleForm.customer_tax_number"
              disabled
              clearable
              type="textarea"
              :rows="2"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="收票人" prop="receive_ticket_person">
            <el-input
              v-model="ruleForm.receive_ticket_person"
              disabled
              clearable
              type="textarea"
              :rows="2"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" class="formItem2">
          <el-form-item label="开户行" prop="open_account_bank">
            <el-input
              v-model="ruleForm.open_account_bank"
              disabled
              clearable
              type="textarea"
              :rows="2"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="客户账号" prop="customer_account">
            <el-input
              disabled
              v-model="ruleForm.customer_account"
              clearable
              type="textarea"
              :rows="2"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="收票电话" prop="receive_ticket_phone">
            <el-input
              disabled
              v-model="ruleForm.receive_ticket_phone"
              clearable
              type="textarea"
              :rows="2"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" class="formItem2">
          <el-form-item label="开票地址" prop="open_ticket_address">
            <el-input
              disabled
              v-model="ruleForm.open_ticket_address"
              clearable
              type="textarea"
              :rows="2"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="开票电话" prop="open_ticket_phone">
            <el-input
              disabled
              v-model="ruleForm.open_ticket_phone"
              clearable
              type="textarea"
              :rows="2"
              style="font-family: Arial;"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="收票地址" prop="receive_ticket_address">
            <el-input
              disabled
              v-model="ruleForm.receive_ticket_address"
              clearable
              type="textarea"
              :rows="2"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" class="formItem2">
          <el-form-item  v-if="[3,2,1].includes(ruleForm.audit_state)" :label="'单据备注'" prop="ticket_remark">
            <span v-if="!!ruleForm.ticket_remark"  style="color:red;">    {{ ruleForm.ticket_remark }}</span>
            <el-input
              v-if="!ruleForm.ticket_remark"
              :disabled="true"
              v-model="ruleForm.ticket_remark"
              clearable
            >
          </el-input>
          </el-form-item>
          <el-form-item v-if="![3,2,1].includes(ruleForm.audit_state)" label="单据备注" prop="ticket_remark">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.ticket_remark"
              clearable
            >
          </el-input>
          </el-form-item>

        </el-col>
        <el-col :span="8" class="formItem2" v-if="ruleForm.ticket_id">
          <el-form-item label="快递单号" prop="express_number">
            <el-input
              disabled
              v-model="ruleForm.express_number"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2" v-if="ruleForm.ticket_id">
          <el-form-item label="发票号码" prop="invoice_number">
            <el-input
              disabled
              v-model="ruleForm.invoice_number"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" class="formItem2" v-if="ruleForm.ticket_id">
          <el-form-item label="审核状态" prop="audit_state_format">
            <el-input
              disabled
              v-model="ruleForm.audit_state_format"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2" v-if="ruleForm.ticket_id">
          <el-form-item label="审核备注" prop="audit_remark">
            <el-input
  
              disabled
              v-model="ruleForm.audit_remark"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8" class="formItem2">
          <el-form-item label="本次开票金额" prop="total_money">
            <el-input disabled v-model="total_money" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" class="formItem2">
          <el-form-item label="可开票总金额" prop="totalOpenMoney">
            <el-input style="    color: red;" disabled v-model="totalOpenMoney" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <TableCustom
        class="mt10 tableContent"
        ref="tableCustom"
        tableHeight="100%"
        :obj="obj"
        :tableCol="tableCol"
        @del="delDetail"
        :isDel="!ruleForm.ticket_id"
        :parentComponentName="'openTicket'"
        @getTotalMoney="getTotalMoney"
      />
    </el-form>
    <!-- :has_contract_anexo="true" -->
    <!-- :has_receivables="true" -->
    <ContractList
      ref="contractList"
      dialogTitle="已收款的合同列表"
      :isReceive="true"
      :isJudge="true"
      :not_has_open_ticket_no_audit="true"
      @getInfo="getContract"
      :type="2"
      :data_state="1"
      :invoice_tax_rate="true"
      :customer_no="this.ruleForm.customer_no"
      :contract_ids="fk_customer_contract_ids"
      :isAllReceive="true"
    />

    <FileList
      ref="fileList"
      :fileId="ruleForm.ticket_id"
      fileType="openTicket"
      :customerInfo="customerInfo"
    />

    <el-dialog
      title="打印合同"
      :visible.sync="dialogVisibleTem"
      append-to-body
      @close="dialogCancelTem"
      width="660px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <el-form
        :model="temForm"
        :rules="temRules"
        ref="temForm"
        label-width="100px"
      >
        <el-form-item label="合同模板" prop="contract_template_id">
          <el-select
            v-model="temForm.contract_template_id"
            placeholder="请选择合同模板"
            filterable
            clearable
          >
            <el-option
              v-for="item in temList"
              :key="item.contract_template_id"
              :label="item.contract_template_name"
              :value="item.contract_template_id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTem('temForm')">确定</el-button>
        <el-button @click="dialogCancelTem">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script src="./index.js"></script>

<style lang="scss" scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown + .el-dropdown {
  margin-left: 15px;
}

@import "@/assets/css/element/font-color.scss";
</style>
