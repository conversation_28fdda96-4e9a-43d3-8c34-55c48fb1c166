import API from '@/api/internalSystem/customerManage/implementation'
import API2 from "@/api/internalSystem/salesManage/contract";
import {
  getOptions
} from "@/common/internalSystem/common.js"
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";

import { mapGetters } from 'vuex';
export default {
  name: "impleAudit",
  components: {
    TableView
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_contract_id: "",
        contract_no: "",
        customer_name: "",
        fk_sell_employee_id: "",
        fk_sell_employee_name: "",
        fk_customer_id: "",
        link_man: "",
        address: "",
        phone: "",
        customer_type: "",
        implementation_time: "",
        over_time: "",
        sell_type: "",
        train_type: "",
        implementation_conclusion: "",
        original_port_count: "",
        add_port_count: "",
        version_no: "",
        follow_records: "",
        remark: "",
        attention_matters: ""
      },
      implementationUserList:[],
      customerTypeList: [], //客户类型
      sellTypeList: [], //销售类型
      softwareVersionList: [], //软件版本
      trainTypeList: [], //培训方式
      customerStageList: [], //客户阶段
      loading: false,
      dialogVisibleAudit: false,
      auditForm: {
        auditState: 1,
        auditRemark: ""
      },
      rules: {
        auditState: [{
          required: true,
          message: "请选择审核状态",
          trigger: "change"
        }]
      },
      auditStateList: [],
      tableData: [],
      tableList: [

        {
          width: 150,
          name: "销售合同号",
          value: "contract_no",
        },
        {
  
          name: "产品服务",
          value: "brandName",
        },
        {
          name: "销售类型",
          value: "detail_sell_name",
          width: 150
        },
        {
          name: "计量单位",
          value: "measurement_unit_name",
          width: 150
        },
        {
          name: "合同数量",
          value: "contract_count",
          width: 150
        },
        {
          name: "合同金额",
          value: "contract_amount",
          width: 150
        },
        {
          name: "原有端口数",
          value: "original_port_count",
          width: 150
        },
        {
          name: "新增端口数",
          value: "add_port_count",
          width: 150
        },
  
        {
          width: 150,
          name: "合同备注",
          value: "remark",
        },
        {
          width: 150,
          name: "软件序列号",
          value: "software_no",
        },
        {
          width: 150,
          name: "成交日期",
          value: "fixtrue_time",
        },
      ],
    };
  },
  watch:{
    'ruleForm.contract_no': {
      handler(newValue,oldValue) {
        if(newValue){
          this.getDetail()
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    getDetail(){


      if(!this.ruleForm.customer_contract_id){
        return
      }
      API2.detailList({
        customer_contract_id: this.ruleForm.customer_contract_id,
        ditail_tyoe : 1
      }).then(({data})=>{

        this.tableData = data
      })

    },
    Show(data = null) {
      this.dialogVisible = true;
      this.customerTypeList = getOptions('t_customer', 'customer_type');
      this.sellTypeList = getOptions('t_implementation', 'sell_type');
      this.softwareVersionList = getOptions('t_implementation', 'software_version');
      this.trainTypeList = getOptions('t_implementation', 'train_type');
      this.auditStateList = getOptions('t_implementation', 'auditState');
      if (data) {
        this.ruleForm = data;
      }
      this.$store.dispatch('getEmployee').then(res => {
				this.implementationUserList  = res
			}).catch(() => {
			})
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
    },
    openAudit() {
      this.dialogVisibleAudit = true;
    },
    dialogCancelAudit() {
      this.dialogVisibleAudit = false;
      this.resetForm('auditForm');
    },
    save() {
      let params = this.auditForm;
      params.implementation_id = this.ruleForm.implementation_id;
      this.loading = true;
      API.updateAudit(params)
        .then(() => {
          this.dialogCancelAudit();
          this.dialogCancel();
        })
        .catch(() => {}).finally(() => {
          this.loading = false;
        });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_contract_id: "",
        contract_no: "",
        customer_name: "",
        fk_sell_employee_id: "",
        fk_sell_employee_name: "",
        fk_customer_id: "",
        link_man: "",
        address: "",
        phone: "",
        customer_type: "",
        implementation_time: "",
        over_time: "",
        train_type: "",
        implementation_conclusion: "",
        original_port_count: "",
        add_port_count: "",
        version_no: "",
        follow_records: "",
        remark: "",
        attention_matters: ""
      },
      this.tableData = []
    }
  },
  computed: {
    ...mapGetters(["contract_auditStateList", "sell_type",
    "contract_train_type",
    "software_version",])
  },

};