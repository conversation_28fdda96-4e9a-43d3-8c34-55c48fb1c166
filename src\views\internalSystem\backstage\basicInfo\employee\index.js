import API from '@/api/internalSystem/basicInfo/employee/employeeApi.js'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddUser from "./components/addUser/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import {
  mapGetters
} from "vuex";
export default {
  name: "employee",
  data() {
    return {
      title: "员工设置",
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        employee_number: "",
        employee_name: "",
        in_position: ''
      },
      tableList: [{
          name: "工号",
          value: "employee_number",
          width: 120
        },
        {
          name: "姓名",
          value: "employee_name"
        },
        {
          name: "性别",
          value: "gender_name",
          width: 80
        },
        {
          name: "联系电话",
          value: "telephone",
          width: 150
        },
        {
          name: "出生年月",
          value: "birthday",
          width: 130
        },
        {
          name: "身份证号码",
          value: "id_card",
          width: 180
        },
        {
          name: "部门",
          value: "department_name"
        },
        {
          name: "岗位",
          value: "role_name"
        },
        {
          name: "所属上级",
          value: "superior_name"
        },
        {
          name: "是否在职",
          value: "entry_time",
          width: 130
        },
        {
          name: "在职状态",
          value: "in_position_name"
        },
      ]
    };
  },

  mounted() {
    this.getList()
  },
  methods: {
    getList(f = false) {
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
      if (f)
        param.pageNum = 1;
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    add() {
      this.$refs.AddUser.Show();
    },
    //打开修改数据字典会话框
    modify(item) {
      let params = {
        id: item.employeeId
      };
      API.getInfo(params)
        .then(data => {
          data.data.superiorId = data.data.superiorId != -1 ? data.data.superiorId : "";
          this.$refs.AddUser.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      let params = {
        id: item.employeeId
      };
      API.remove(params)
        .then(() => {
          this.getList();
          this.$store.commit('GET_EMPLOYEE', [])
        })
        .catch(() => {});
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    }
  },

  components: {
    AddUser,
    Pagination,
    TableView
  },
  computed: {
    ...mapGetters(["in_position"])
  }
};
