import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: (params) => {
    return Axios.post(`${environment.internalSystemAPI}brand/query`, params);
  },
  query2: (params) => {
    return Axios.post(`${environment.internalSystemAPI}brand/query2`, params);
  },
  // 新增
  add: (params) => {
    return Axios.post(`${environment.internalSystemAPI}brand/add`, params);
  },
  // 删除
  remove: (params) => {
    return Axios.post(`${environment.internalSystemAPI}brand/remove`, params);
  },
  // 编辑
  update: (params) => {
    return Axios.post(`${environment.internalSystemAPI}brand/update`, params);
  },
  // 获取单条信息
  getInfo: (params) => {
    return Axios.post(`${environment.internalSystemAPI}brand/getInfo`, params);
  },
  // 上级产品的结束时间
  getParentBrandObj: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}brand/getParentBrandObj`,
      params
    );
  },
  // 需要端口产品
  getPortBrand: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}brand/getPortBrand`,
      params
    );
  },
};
