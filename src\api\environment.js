import _ from 'lodash'

const _defaultConfig = {
	development: { // 开发环境
		backEndUrl: "http://192.168.0.26:5006",
		basicAPI: `/basicAPI/`,
		jiqin_server: "/jiqin_server/",
		internalSystemAPI: "/internalSystemAPI/",
		internalsystemWebsocket:"://192.168.2.116:5112/internalsystemWebsocket/"
	},
	production: { // 正式环境
		backEndUrl: "http://yun.jiqinyun.com",
		basicAPI: `/basicAPI/`, // 雲平臺
		jiqin_server: "/jiqin_server/",
		internalSystemAPI: "/internalSystemAPI/",
		internalsystemWebsocket:"://jiqin.jiqinyun.com/internalsystemWebsocket/"
	}
}
let _env = (process.env.NODE_ENV || 'production').toLowerCase()

let envConfig = _env === 'production' ? _defaultConfig.production : _defaultConfig.development

console.log(`当前环境：${_env}`)

export default _.merge(
		envConfig,
		{
			env: _env
		})
