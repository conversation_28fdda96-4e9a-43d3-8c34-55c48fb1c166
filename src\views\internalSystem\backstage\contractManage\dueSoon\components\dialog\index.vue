<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
  >
    <div style="height:400px">
      <TableView :tableList="tableList" :tableData="tableData" />
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submit()">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import API from "@/api/internalSystem/contractManage/index.js";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";

export default {
  data() {
    return {
      title: "回访列表",
      dialogVisible: false,
      ruleForm: {
        implement_id: "",
      },
      propleList: [],
      rules: {
        implement_id: [{ required: true, message: "请选择", trigger: "blur" }],
      },
      selectObj: {},
      tableList: [
        {
          name: "回访结果",
          value: "content",
        },
        {
          name: "回访时间",
          value: "update_time",
        },
      ],
      tableData: [],
    };
  },
  methods: {
    Show(row) {
      this.dialogVisible = true;
      this.title=`${row.contract_no}的回访列表`
      this.getInfo(row.customer_contract_id);
    },
    getInfo(customer_contract_id) {
      API.returnVisitList({ customer_contract_id }).then((res) => {
        this.tableData = res.data;
      });
    },
    handleClose() {
      Object.assign(this.$data, this.$options.data());
      this.selectObj = {};
    },
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) return false;
        this.save();
      });
    },
    save() {
      let params = {
        implement_state: 0,
        customer_contract_id: this.selectObj.customer_contract_id,
        implement_id: this.ruleForm.implement_id,
      };
      API.setImplementContract(params).then(() => {
        this.success("保存成功");
        this.handleClose();
        this.$emit("success");
      });
    },
  },
  components: {
    TableView,
  },
};
</script>
