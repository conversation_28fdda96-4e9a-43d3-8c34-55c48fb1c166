import API from "@/api/common/menu/index.js";

const state = {
  params_constant: {}, // 内部系统常量
  auditStateList: [
    {
      id: 1,
      value: 1,
      label: "审核通过",
      remarks: "",
    },
    {
      id: 2,
      value: 2,
      label: "审核拒绝",
      remarks: "",
    },

  ],
};
const getters = {
  //审核中心审核状态
  contract_auditStateList: (state) => state.auditStateList || [],
  // 菜单类型
  params_constant_menu_type: (state) => state.params_constant.menu_type || [],
  // 客户类型
  customer_stage: (state) => state.params_constant.customer_stage || [],
  // 参数类型
  params_constant_parameter_type: (state) =>
    state.params_constant.parameter_type || [],
  // 性别
  params_constant_gender: (state) => state.params_constant.gender || [],
  // 销售类型
  sell_type: (state) => state.params_constant.sell_type || [],
  // 合同付款方式
  contract_pay_type: (state) => state.params_constant.contract_pay_type || [],
  // 合同培训方式
  contract_train_type: (state) =>
    state.params_constant.contract_train_type || [],
  // 软件版本
  software_version: (state) => state.params_constant.software_version || [],
  // 计量单位
  measurement_unit: (state) => state.params_constant.measurement_unit || [],
  // 审核状态
  audit_state: (state) => state.params_constant.audit_state || [],
  // 收款项目
  receivables_project: (state) =>
    state.params_constant.receivables_project || [],
  //结算方式
  receivables_settlement_method: (state) =>
    state.params_constant.receivables_settlement_method || [],
  //文案审核
  copy_audit: (state) => state.params_constant.copy_audit || [],
  //文案类型
  copy_type: (state) => state.params_constant.copy_type || [],
  //客户实施单审核
  implementation_audit: (state) =>
    state.params_constant.implementation_audit || [],

  //费用报销单审核
  financial_cost_invoice_audit: (state) =>
    state.params_constant.financial_cost_invoice_audit || [],
  //收入类型
  income_type: (state) => state.params_constant.income_type || [],
  //钱款类型
  money_type: (state) => state.params_constant.money_type || [],
  // 客户阶段
  params_constant_customer_stage: (state) =>
    state.params_constant.customer_stage || [],

  // 客户类型
  params_constant_customer_type: (state) =>
    state.params_constant.customer_type || [],
  // 所属行业
  params_constant_belong_industry: (state) =>
    state.params_constant.customer_belong_industry || [],
  // 客户来源
  params_constant_customer_source: (state) =>
    state.params_constant.customer_source || [],
  // 重要等级
  params_constant_important_rank: (state) =>
    state.params_constant.customer_important_rank || [],
  // 联系人类型
  params_constant_linkman_type: (state) =>
    state.params_constant.linkman_type || [],
  // 客户修改单审核状态
  customer_temp_audit: (state) =>
    state.params_constant.customer_temp_audit || [],
  // 客户意向单审核状态
  customer_intention_audit: (state) =>
    state.params_constant.customer_intention_audit || [],
  // 回访方式
  sales_visiting_form: (state) =>
    state.params_constant.sales_visiting_form || [],
  // 回访单审核状态
  customer_sales_visiting_audit: (state) =>
    state.params_constant.customer_sales_visiting_audit || [],
  // 品牌分类
  brand_classify: (state) => state.params_constant.brand_classify || [],
  // 销售合同审核状态
  customer_contract_audit: (state) =>
    state.params_constant.customer_contract_audit || [],
  // 产品信息设置状态
  disable_state: (state) => state.params_constant.disable_state || [],
  // 员工在职状态
  in_position: (state) => state.params_constant.in_position || [],
  //bug类型
  problem_type: (state) => state.params_constant.problem_type || [],
  //操作系统
  operating_system: (state) => state.params_constant.operating_system || [],
  //浏览器
  problem_browser: (state) => state.params_constant.problem_browser || [],
  //问题状态
  problem_state: (state) => state.params_constant.problem_state || [],
  //解决方案
  problem_solution: (state) => state.params_constant.problem_solution || [],
  //是否授权
  product_is_grant: (state) => state.params_constant.product_is_grant || [],
  //有无维护期
  product_is_period: (state) => state.params_constant.product_is_period || [],
  //软件模式
  product_sale_model: (state) => state.params_constant.product_sale_model || [],
  //销售方式
  sale_pattern: (state) => state.params_constant.sale_pattern || [],
  //是否开票
  is_open_bill: (state) => state.params_constant.is_open_bill || [],
  //销售类型
  sale_type: (state) => state.params_constant.sale_type || [],
  //是否新增端口
  is_add_port: (state) => state.params_constant.is_add_port || [],
  //是否仅购买端口
  is_only_port: (state) => state.params_constant.is_only_port || [],

};

const mutations = {
  SET_CONSTANT(state, data = {}) {
    state.params_constant = data;
  },
};
const actions = {
  getConstant: ({
    commit
  }) => {
    API.getConstant({})
      .then((res) => {
        if (res.data) {
          commit("SET_CONSTANT", res.data);
        } else {
          window.location.href = `http://${window.location.host}/#/login`;
        }
      })
      .catch(() => {});
  },
};
export default {
  state,
  getters,
  mutations,
  actions,
};