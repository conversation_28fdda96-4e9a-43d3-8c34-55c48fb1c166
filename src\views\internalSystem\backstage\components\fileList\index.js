import API from "@/api/internalSystem/common/enclosure.js";
import environment from "@/api/environment";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import EmailAPI from "@/api/internalSystem/common/email.js";
import EmailLogList from "@/views/internalSystem/backstage/components/emailLogList/index.vue";
import CustomerAPI from "@/api/internalSystem/customerManage/customerInfo/index.js";
import { mapGetters } from "vuex";
import pdf from "vue-pdf";
export default {
  name: "fileList",
  components: {
    TableView,
    Pagination,
    pdf,
    EmailLogList,
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      tableData: [],
      tableList: [
        {
          name: "文件名",
          value: "file_name",
        },
        {
          name: "附件类型",
          value: "file_type_text",
          width: 120,
        },
        {
          name: "文件大小",
          value: "file_size",
          width: 120,
        },
      ],
      environment: environment,
      files: {},
      url: "",
      showDoc: false,
      showPdf: false,
      showImages: false,
      anexoName: "",
      pageNum: 1,
      pageTotalNum: 1, // 总页数
      loadedRatio: 0, // 当前页面的加载进度，范围是0-1 ，等于1的时候代表当前页已经完全加载完成了

      
      selectedFileType: '', // 选中的附件类型
      fileTypeMap: {
        'contract': '合同附件',
        'invoice': '发票附件',
        'other': '其他附件'
      },

      // 动态设置的属性（避免与props冲突，使用不同的名称）
      currentFileType: '',
      currentFileId: null,
      currentCustomerInfo: {},

      // 邮件发送相关数据
      emailDialogVisible: false,
      emailSending: false,
      emailForm: {
        email: '',
        subject: '',
        content: '',
        file_url: ''
      },
      currentFile: null,

      // 邮件日志相关
      emailLogDialogVisible: false,
      currentLogFileId: null,

      // 更多操作相关
      moreActionsVisible: false,
      currentMoreFile: null,
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "附件列表",
    },
    fileType: {
      type: String,
    },
    fileId: {
      type: Number,
    },
    customerInfo: {
      type: Object,
      default: () => ({})
    },
  },
  watch: {
    loadedRatio: {
 
      handler(newValue) {
        if(newValue == 1){
          this.closeFullScreen(this.openFullScreen());

        }
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters(["userInfo", "buttonPermissions"]),
    // 根据用户部门获取默认附件类型
    defaultFileType() {
      if (!this.userInfo || !this.userInfo.department_name) {
        return 'contract'; // 默认合同附件
      }
      const department_name = this.userInfo.department_name.toLowerCase();

      // 财务相关部门默认选择发票附件
      const financeKeywords = ['财务', '会计', 'finance', 'accounting'];
      if (financeKeywords.some(keyword => department_name.includes(keyword))) {
        return 'invoice';
      }
      const employeeNumber = this.userInfo.employeeNumber.toLowerCase();
      const salesKeywords = ['0101'];
      if (salesKeywords.some(keyword => employeeNumber.includes(keyword))) {
        return 'invoice';
      }

      // 其他部门默认选择合同附件
      return 'contract';
    }
  },
  methods: {
    Show(fileType, fileId, customerInfo = {}) {
      this.currentFileType = fileType;
      this.currentFileId = fileId;
      this.currentCustomerInfo = customerInfo;
      this.dialogVisible = true;
      // 根据用户部门设置默认附件类型
      this.selectedFileType = this.defaultFileType;
      console.log('用户信息:', this.userInfo);
      console.log('用户部门:', this.userInfo && this.userInfo.departmentName);
      console.log('默认附件类型:', this.defaultFileType);
      this.$nextTick(() => {
        this.getList();
      });
    },
    getList() {
      this.loading = true;
      let url = this.currentFileType + "FileList";

      // 如果pagination组件还没有初始化，使用默认分页参数
      let param = {};
      if (this.$refs.file_pagination) {
        param = Object.assign(this.$refs.file_pagination.obtain());
      } else {
        param = {
          pageNum: 1,
          pageSize: 10
        };
      }

      param.id = this.currentFileId;
      
      API[url](param)
        .then((res) => {
          // 处理数据，添加附件类型文本
          this.tableData = res.data.map(item => {
            return {
              ...item,
              file_type_text: this.fileTypeMap[item.file_type] || '其他附件'
            }
          });
          // 如果pagination组件存在，设置总数
          if (this.$refs.file_pagination) {
            this.$refs.file_pagination.setTotal(res.totalCount);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    checkFile(file) {
      console.log('检查文件：', file.name);
      if(!this.selectedFileType){
        this.error("请先选择附件类型")
        return false;
      }
      return true;
      // if(file.type.toLowerCase()!='application/pdf'){
      //   this.error("请选择pdf文件进行上传")
      //   return false;
      // }
    },



    
    handlePreview(res) {
      if (!res || res.code !== 1 || !res.data) {
        this.error(res.message);
        this.getList();
        return;
      }
      this.files = res.data;
      this.upLoadSuccess();
    },
    recovery() {
      this.error("文件上传失败");
    },
    upLoadSuccess() {
      let url = this.currentFileType + "Add";
      let params = {
        file_url: this.files.file_url,
        file_name: this.files.raw_name,
        file_size: this.files.file_size,
        file_suffix: this.files.file_suffix,
        id: this.currentFileId,
        file_type: this.selectedFileType // 添加附件类型
      };
      API[url](params)
        .then(() => {
          this.success("文件上传成功");
          // 保持默认附件类型选择，不重置为空
          this.selectedFileType = this.defaultFileType;
          this.getList();
        })
        .catch(() => {});
    },
    del(item) {
      if (item.data_state === 2) {
        return this.error("该记录是旧数据，请上旧系统继续操作");
      }
      // 去掉重复的确认框，TableView组件已经处理了确认逻辑
      let url = this.currentFileType + "Del";
      API[url]({
        anexo_id: item.anexo_id,
      })
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
    // 预览文件
    previewFile(row) {
      let fileType = row.file_suffix && row.file_suffix.toLowerCase();
      this.anexoName = row.file_name;

      // 支持的文件类型
      const doc = ["docx", "doc", "xlsx", "xls", "txt", "ppt", "pptx"];
      const pdf = ["pdf"];
      const images = ["png", "jpg", "jpeg", "gif", "bmp", "webp"];
      const video = ["mp4", "avi", "mov", "wmv", "flv", "webm"];
      const audio = ["mp3", "wav", "ogg", "aac"];

      if (doc.includes(fileType)) {
        this.url = encodeURIComponent(row.file_url);
        this.showDoc = true;
      } else if (pdf.includes(fileType)) {
        this.openFullScreen();
        this.url = row.file_url;
        this.showPdf = true;
      } else if (images.includes(fileType)) {
        this.url = row.file_url;
        this.showImages = true;
      } else if (video.includes(fileType) || audio.includes(fileType)) {
        // 对于视频和音频文件，直接在新窗口打开
        window.open(row.file_url, '_blank');
      } else {
        // 不支持预览的文件类型，提示用户下载查看
        this.$confirm(`${fileType || '该'} 格式文件不支持在线预览，是否下载查看？`, '提示', {
          confirmButtonText: '下载',
          cancelButtonText: '取消',
          type: 'info'
        }).then(() => {
          this.downloadFile(row);
        }).catch(() => {});
      }
    },

    // 下载文件（原来的five方法改名为downloadFile）
    downloadFile(row) {
      window.location.href = row.file_url;
    },
    openFullScreen() {
      let loading = this.$loading({
        lock: true,
        text: '正在加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      return loading
    },
    closeFullScreen(loading) {
      loading.close();
    },
    uploading(event) {
      this.files = {
        name: event.file.name,
        percentage: event.file.percentage,
        raw: event.file.raw,
        size: event.file.size,
        status: event.file.status,
      };
      this.upLoadSuccess();
    },
    // 改变PDF页码,val传过来区分上一页下一页的值,0上一页,1下一页
    prePage() {
      var page = this.pageNum;
      page = page > 1 ? page - 1 : 1;
      this.pageNum = page;
    },
    nextPage() {
      var page = this.pageNum;
      page = page < this.pageTotalNum ? page + 1 : this.pageTotalNum;
      this.pageNum = page;
    },
    // pdf加载时
    iconByType(row) {
      return `http://www.xdocin.com/xdoc?_func=to&_format=html&_xdoc=${row.file_url}`;
    },
    closePreviewClick() {
      this.showDoc = false;
      this.showPdf = false;
      this.showImages = false;
      this.pageNum = 1;
      this.pageTotalNum = 1;
    },
    getNumPages() {
      let that = this;
      let url =
        "//www.xdocin.com/xdoc?_func=to&_format=html&_xdoc=" + this.pdfUrl;
      let numPages = pdf.createLoadingTask(url);
      numPages.promise.then((pdf) => {
        that.pageTotalNum = pdf.numPages;
      });
    },

    // 显示更多操作
    showMoreActions(file) {
      this.currentMoreFile = file;
      this.moreActionsVisible = true;
    },

    // 从操作列打开邮件发送弹窗
    openEmailDialogForFile(file) {
      this.currentFile = file;
      this.emailDialogVisible = true;
      this.moreActionsVisible = false; // 关闭更多操作弹窗
      // 设置默认值
      this.emailForm.email = this.currentCustomerInfo && this.currentCustomerInfo.email || '';
      this.emailForm.subject = `附件文件 - ${file.file_name}`;
      this.emailForm.file_url = file.file_url || '';
      this.emailForm.content = `您好，\n\n请查收附件：${file.file_name}。\n\n谢谢！`;
    },

    // 发送邮件
    sendEmail() {
      this.$refs.emailForm.validate((valid) => {
        if (valid) {
          this.emailSending = true;

          // 构造邮件数据，只发送文件名和URL
          const emailData = {
            email: this.emailForm.email,
            subject: this.emailForm.subject,
            content: this.emailForm.content,
            customer_id: this.currentCustomerInfo && this.currentCustomerInfo.fk_customer_id, // 添加客户ID
            attachments: [{
              fileName: this.currentFile.file_name,
              fileUrl: this.emailForm.file_url // 使用表单中可编辑的URL
            }]
          };

          // 调用发送邮件API
          this.sendEmailAPI(emailData);
        }
      });
    },

    // 显示邮件发送日志
    showEmailLogs(file) {
      this.currentLogFileId = file.anexo_id;
      this.emailLogDialogVisible = true;
      this.moreActionsVisible = false; // 关闭更多操作弹窗
      this.$nextTick(() => {
        this.$refs.emailLogList.Show(this.currentLogFileId);
      });
    },

    // 发送邮件API调用
    async sendEmailAPI(emailData) {
      try {
        // 在发送数据中加入附件ID
        emailData.anexo_id = this.currentFile.anexo_id;
        // 这里调用的是通用的邮件发送接口，后端根据有无附件内容来决定如何处理
        const result = await EmailAPI.sendEmail(emailData);

        // 发送成功后，更新客户邮箱信息
        if (emailData.customer_id && emailData.email) {
          try {
            await this.updateCustomerEmail(emailData.customer_id, emailData.email);
            console.log(`客户邮箱已更新: ${emailData.email}`);
          } catch (error) {
            console.warn('更新客户邮箱失败:', error);
            // 不影响邮件发送的成功状态
          }
        }

        this.success('邮件发送成功！');
        this.emailDialogVisible = false;
        this.$refs.emailForm.resetFields();
        this.getList(); // 发送成功后刷新列表，更新日志数量

        return result;
      } catch (error) {
        this.error('邮件发送失败，请重试！');
        throw error;
      } finally {
        this.emailSending = false;
      }
    },

    // 更新客户邮箱信息
    async updateCustomerEmail(customerId, email) {
      const params = {
        customer_id: customerId,
        email: email
      };
      return CustomerAPI.update(params);
    },

    dialogCancel() {
      this.$emit("closeFile", this.currentFileId);
      this.dialogVisible = false;
      // 关闭时重置为空，下次打开时会重新设置默认值
      this.selectedFileType = '';
    },
  },
  mounted() {},
};
