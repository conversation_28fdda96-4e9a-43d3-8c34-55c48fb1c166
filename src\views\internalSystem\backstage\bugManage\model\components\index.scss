.treeContainer {
    margin: 0 auto;
    width: 90%;
    height: 100%;
    // overflow: auto;
    display: flex;
    flex-direction: column;
    .el-tree-node__content{
      height: 45px;
    }
    .head {
      height: auto;
      flex: 0 0 auto;
      text-align: center;
      margin-top: 20px;
      .filterInput{
        width: 100%;
      }
    }
    .main-tree {
      flex: 1;
      overflow: auto;
    }
    .custom-tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      padding-right: 8px;
    }
  }