<template>
  <div v-if="dialogVisible">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button v-if="(isEdit&&ruleForm.customer_intention_id)||!ruleForm.customer_intention_id" type="primary"
        @click="submitForm('ruleForm')" :loading="loading">保 存</el-button>
    </div>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" class="mt10">
      <el-row :gutter="20">

        <el-col :xl="8" :sm="12">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input :disabled="!isEdit" v-model="ruleForm.customer_name" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系人" prop="link_man">
            <el-input :disabled="!isEdit" v-model="ruleForm.link_man" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户类型" prop="customer_type">
            <el-select :disabled="!isEdit" v-model="ruleForm.customer_type" placeholder="请选择客户类型" filterable clearable>
              <el-option v-for="item in params_constant_customer_type" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input :disabled="!isEdit" v-model="ruleForm.phone"  clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="主营品牌" prop="brand">
            <el-input :disabled="!isEdit" v-model="ruleForm.brand" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="联系时间" prop="contact_time">
            <my-date :disabled="!isEdit" v-model="ruleForm.contact_time" :isAllDate="false" hint="请选择联系时间"></my-date>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户地区" prop="province">
            <el-select :disabled="!isEdit" v-model="ruleForm.province" placeholder="请选择省份" @change="changeProvince"
              filterable clearable>
              <el-option v-for="item in provinceList" :key="item.province" :label="item.origin_place"
                :value="item.province">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户阶段" prop="customer_stage">
            <el-select v-model="ruleForm.customer_stage" placeholder="请选择客户阶段" disabled filterable clearable>
              <el-option v-for="item in params_constant_customer_stage" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="所在城市" prop="city">
            <el-select :disabled="!isEdit" v-model="ruleForm.city" placeholder="选择省份获取可选城市" filterable clearable>
              <el-option v-for="item in cityList" :key="item.city" :label="item.origin_place" :value="item.city">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户行业" prop="belong_industry">
            <el-select :disabled="!isEdit" v-model="ruleForm.belong_industry" placeholder="请选择客户行业" filterable
              clearable>
              <el-option v-for="item in params_constant_belong_industry" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="8" :sm="12">
          <el-form-item label="客户来源" prop="customer_source">
            <el-select :disabled="!isEdit" v-model="ruleForm.customer_source" placeholder="请选择客户来源" filterable
              clearable>
              <el-option v-for="item in params_constant_customer_source" :key="item.id" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="16">
          <el-form-item label="是否使用过erp软件" prop="is_software">
            <el-radio :disabled="!isEdit" v-model="ruleForm.is_software" :label="0">否</el-radio>
            <el-radio :disabled="!isEdit" v-model="ruleForm.is_software" :label="1">是</el-radio>
          </el-form-item>
        </el-col>
        <el-col :xl="16">
          <el-form-item v-if="ruleForm.is_software==1" label="" prop="use_describe">
            <el-input :disabled="!isEdit" type="textarea" placeholder="请输入使用描述" v-model="ruleForm.use_describe"
              clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="16">
          <el-form-item label="报价方案" prop="quotation_scheme">
            <el-input :disabled="!isEdit" type="textarea" placeholder="请输入报价方案" v-model="ruleForm.quotation_scheme"
              clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="16">
          <el-form-item label="跟进记录" prop="follow_records">
            <el-input :disabled="!isEdit" type="textarea" placeholder="请输入跟进记录" v-model="ruleForm.follow_records"
              clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :xl="16">
          <el-form-item label="企业痛点/软件需求" prop="software_demand">
            <el-input :disabled="!isEdit" type="textarea" placeholder="请输入企业痛点/软件需求" v-model="ruleForm.software_demand"
              clearable>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script src="./index.js">

</script>

<style lang="scss" scoped>


</style>