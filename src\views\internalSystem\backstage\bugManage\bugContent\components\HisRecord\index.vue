<template>
  <fieldset class="content" v-show="!!recordList.length">
    <legend class="content-title">历史记录</legend>
    <div class="content-body">
      <div
        class="content-body-item-his"
        v-for="(recordItem, rIndex) in recordList || []"
        :key="rIndex"
      >
        {{ rIndex + 1 }}. {{ recordItem.label }}
        <div class="history" v-if="!!recordItem.remarks">
          <div v-html="recordItem.remarks"></div>
        </div>
      </div>
    </div>
  </fieldset>
</template>

<script>
export default {
  props: {
    recordList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
};
</script>

<style lang="scss" scoped>
.history {
  background: #fbfbfb;
  padding: 4px 10px 6px;
  min-height: 20px;
  border: 1px solid #f5f5f5;
}
.content {
  margin-bottom: 15px;
  border: 1px solid #e5e5e5;
  padding: 10px 15px 15px;
  .content-title {
    color: #333;
    border: 0;
    width: auto;
    margin: 0 0 0 -5px;
    font-size: 13px;
    font-weight: bold;
    border-bottom: 0;
    padding: 0 5px;
  }
  .content-body {
    .content-body-item {
      cursor: pointer;
      .hide {
        display: none;
      }
      .fileName {
        font-size: 13px;
      }
    }
    .content-body-item-his {
      font-size: 14px;
    }
    .content-body-item:hover {
      .hide {
        display: inline-block;
      }
    }
  }
}
</style>