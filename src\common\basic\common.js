const common = {
  checkPhone,
  checkFax,
  checkPost,
  checkEmail,
  checkBankAccount,
  checkQQ,
  checkWechat,
  checkIDCrad
}

//检测电话号码有效性
function checkPhone(phone) {
  if (!(/^1[3|4|5|7|8]\d{9}$/.test(phone))) {
    return false;
  }
}

//检测传真号码有效性
function checkFax(fax) {
  if (!/^(\d{3,4}-)?\d{7,8}$/.test(fax)) {
    return false;
  }
}

//检测邮政编码有效性
function checkPost(post) {
  if (!(/^[1-9]{1}(\d+){5}$/.test(post))) {
    return false;
  }
}

//检测邮箱有效性
function checkEmail(email) {
  if (!(/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(email))) {
    return false;
  }
}

//判断银行卡号长度
function checkBankAccount(bankaccount) {
  if (!/^\d{12,19}$/.test(bankaccount)) {
    return false;
  }
}

//判断qq号码
function checkQQ(qq) {
  if (!/^[1-9]\d{4,}$/.test(qq)) {
    return false;
  }
}

//判断微信号
function checkWechat(wechat) {
  if (!/^[a-zA-Z]{1}[-_a-zA-Z0-9]{5,19}$/.test(wechat)) {
    return false;
  }
}

//判断身份证号码
function checkIDCrad(ID) {
  if (!/^[1-9]\d{5}\d{4}\d{2}\d{2}\d{3}[0-9X]$/.test(ID)) {
    return false;
  }
}

export default common