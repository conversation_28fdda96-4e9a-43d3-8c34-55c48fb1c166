/*树形控件的样式，需要在外层再加个相对定位*/
@import "./element/colour.scss";

#erp{
    .datepicker>input{
       
        border-radius: 3px;
        
    }
    .datepicker{
        input::-webkit-input-placeholder{
            color:rbg(192,196,204);
        }
        input::-moz-placeholder{   /* Mozilla Firefox 19+ */
            color:rbg(192,196,204);
        }
        input:-moz-placeholder{    /* Mozilla Firefox 4 to 18 */
            color:rbg(192,196,204);
        }
        input:-ms-input-placeholder{  /* Internet Explorer 10-11 */ 
            color:rbg(192,196,204);
        }
    }
    .tree {
        width: 100%;
        z-index: 9999;
        position: absolute;
        top: 30px;
        border: 1px solid #ccc;
        max-height: 300px;
    }

    .position-r {
        position: relative;
    }

    /*单据高级查询的label样式*/
    .moreSearchDialog {
        label {
            padding-left: 10px;
            height: 32px;
            line-height: 32px;
            width: 100%;
            display: inline-block;
            vertical-align: middle;
            text-align: center;
        }
    }

    .moreSearch-dialog {
        height: auto;
        width: 100%;
        padding: 0 20px;
        box-sizing: border-box;
        margin-left: auto;
        margin-right: auto;

        .is-label {
            display: flex;
            margin-bottom: 10px;

            label {
                width: auto;
                flex: 0 0 auto;
                font-size: 14px;
                line-height: 32px;
                margin-right: 5px;
            }
        }
    }

    /*选择商品按钮中外层div的样式*/
    .checkProBox {
        position: relative;
        width: 100%;
    }

    /*单据表单中选择商品的按钮样式*/
    .inputButton {
        padding: 5px 3px;
        font-size: 8px;
        position: absolute;
        transform: translate(-50%, -50%);
        top: 50%;
        right: 0px;
    }

    /*隐藏的占位类*/
    .hidden-warp {
        flex: 0 0 32px;
        width: 32px;
        height: 100%;
        margin-right: 10px;
    }
    //数量刷新图标按钮
    .tdWarp {
        width: 100%;
        height: 100%;
        overflow: hidden;
        align-items: center;
      
        .iconPosition {
          cursor: pointer;
          flex: 0 0 18px;
          height: 18px;
          width: 18px;
          border-radius: 3px;
      
          .icon {
            width: 16px;
            height: 100%;
            transition: all 0.3s;
          }
      
          &:active {
            border: 1px solid #999;
          }
        }
      
        .textPosition {
          margin-right: 5px;
        }
      }


    .danger {
        color: $--color-danger;
    }

    .success {
        color: $--color-success;
    }
}
