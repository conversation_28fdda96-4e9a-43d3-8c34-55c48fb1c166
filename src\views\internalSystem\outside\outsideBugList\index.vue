<template>
    <div class="container">
        <List class="body" outside />
    </div>
</template>


<script>
import List from "@/views/internalSystem/backstage/bugManage/bugContent/bugList/index.vue"
export default {
    components:{
        List
    }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
 overflow: auto;
  .body {
    width: 1200px;
    margin: 10px auto;
    height: auto;
  }
 
}
</style>