export default [{
  path: 'basicManage',
  name: 'basicManage',
  title: '基础管理',
  icon: 'home',
  show: true,
  meta: {
    keepAlive: false
  },
  component: resolve =>
    require([
      "@/views/internalSystem/backstage/components/view/view.vue"
    ], resolve),
  children: [{
      path: 'costProject',
      name: 'costProject',
      title: '费用项目设置',
      meta: {
        title: "费用项目设置"
      },
      component: () => import('@/views/internalSystem/backstage/basicManage/costProject/index.vue')
    },
    {
      path: 'costDetails',
      name: 'costDetails',
      title: '费用明细设置',
      meta: {
        title: "费用明细设置"
      },
      component: () => import('@/views/internalSystem/backstage/basicManage/costDetails/index.vue')
    },
    {
      path: 'brand',
      name: 'brand',
      title: '产品信息设置',
      meta: {
        title: "产品信息设置"
      },
      component: () => import('@/views/internalSystem/backstage/basicManage/brand/index.vue')
    },
    {
      path: 'parameter',
      name: 'parameter',
      title: '税率比例设置',
      meta: {
        title: "税率比例设置"
      },
      component: () => import('@/views/internalSystem/backstage/basicManage/parameter/index.vue')
    },

    {
      path: 'salesUnit',
      name: 'salesUnit',
      title: '销货单位设置',
      meta: {
        title: "销货单位设置"
      },
      component: () => import('@/views/internalSystem/backstage/basicManage/salesUnit/index.vue')
    },
    {
      path: 'bankAccout',
      name: 'bankAccout',
      title: '银行账户管理',
      meta: {
        title: "银行账户管理"
      },
      component: () => import('@/views/internalSystem/backstage/financialManage/bankAccout/index.vue')
    },
    {
      path: 'qrCode',
      name: 'qrCode',
      title: '二维码设置',
      meta: {
        title: "二维码管理"
      },
      component: () => import('@/views/internalSystem/backstage/basicManage/qrCode/index.vue')
    },
    {
      path: 'highSeas',
      name: 'highSeas',
      title: '公海客户参数设置',
      meta: {
        title: "公海客户参数设置"
      },
      component: () => import('@/views/internalSystem/backstage/basicManage/highSeas/index.vue')
    },
    {
      path: 'updatepassword',
      name: 'updatePassword',
      title: '修改密码',
      meta: {
        title: "修改密码"
      },
      component: () => import('@/views/internalSystem/backstage/basicManage/password/index.vue')
    }
  ]
}]