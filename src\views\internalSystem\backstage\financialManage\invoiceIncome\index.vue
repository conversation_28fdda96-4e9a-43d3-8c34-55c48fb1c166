<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small" v-if="!isAudit">
      <el-form-item label="查询条件">
        <el-input v-model="formSearch.invoice_unit" placeholder="请输入开票单位" filterable clearable></el-input>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.startTime" hint="请选择开始时间"></my-date>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.endTime" hint="请选择结束时间"></my-date>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
        <el-button
          type="primary"
          @click="add"
          v-permit="'ADD_INVOICEINCOME_NEW'"
        >添加</el-button>
      </el-form-item>
    </el-form>
    <table-view
      v-if="!isAudit"
      :tableList="tableList"
      :tableData="tableData"
      :isEdit="'UPDATE_INVOICEINCOME_NEW'"
      :isDel="'DEL_INVOICEINCOME_NEW'"
      @modify="modify"
      @del="del"
      :isThrid="'AUDIT_INVOICEINCOME_NEW'"
      :thridTitle="'审核'"
      @thrid="item => toAuditDet(item, '发票进项单审核','audit_state')"
    ></table-view>
    <Pagination ref="pagination" @success="getList" v-show="!isAudit" />
    <!-- 新增修改发票进项单信息 -->
    <add-invoice-income ref="addInvoiceIncome" @selectData="getList" />
    <!-- 审核 -->
    <InvoiceIncomeAudit ref="invoiceIncomeAudit" v-show="isAudit" @selectData="getList" />
  </div>
</template>

<script src="./index.js"></script>