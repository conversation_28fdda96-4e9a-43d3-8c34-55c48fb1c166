import API from '@/api/internalSystem/financialManage/income'
import bankAPI from '@/api/internalSystem/financialManage/bankAccout'
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  getOptions,
} from "@/common/internalSystem/common.js"
import { mapGetters } from 'vuex';
export default {
  name: "incomeAudit",
  components: {
    MyDate
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        income_type: "",
        content: "",
        amount: "",
        income_time: "",
        fk_bank_account_id: ""
      },
      incomeTypeList: [],
      bankList: [],
      loading: false,
      dialogVisibleAudit: false,
      auditForm: {
        auditState: 1,
        auditRemark: ""
      },
      rules: {
        auditState: [{
          required: true,
          message: "请选择审核状态",
          trigger: "change"
        }]
      },
      auditStateList: []
    };
  },
  methods: {
    Show(data = null) {
      this.dialogVisible = true;
      this.incomeTypeList = getOptions('t_income', 'income_type');
      this.auditStateList = getOptions('t_income', 'audit_state');
      bankAPI.query()
        .then(data => {
          this.bankList = data.data;
        })
        .catch(() => {});
      this.ruleForm = data;
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.resetForm('ruleForm');
      this.clearData();
      this.$emit("selectData");
      this.dialogVisible = false;
    },
    openAudit() {
      this.dialogVisibleAudit = true;
    },
    dialogCancelAudit() {
      this.dialogVisibleAudit = false;
      this.resetForm('auditForm');
    },
    save() {
      let params = this.auditForm;
      params.income_id = this.ruleForm.income_id;
      this.loading = true;
      API.updateAudit(params)
        .then(() => {
          this.dialogCancelAudit();
          this.dialogCancel();
        })
        .catch(() => {}).finally(() => {
          this.loading = false;
        });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        income_type: "",
        content: "",
        amount: "",
        income_time: "",
        fk_bank_account_id: ""
      }
    }
  },
  computed: {
    ...mapGetters(["contract_auditStateList","income_type"])
  },
};