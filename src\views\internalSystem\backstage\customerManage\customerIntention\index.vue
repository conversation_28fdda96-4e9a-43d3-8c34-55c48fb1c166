<template>
  <div style="overflow-y:auto;overflow-x: hidden;" class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small" v-if="!isAdd&&!isAudit">
      <el-form-item label="查询条件">
        <el-input v-model="formSearch.customer_name" placeholder="请输入客户名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.fk_sale_employee_id"
          placeholder="请选择销售员"
          class="inputBox"
          filterable
          clearable
          :disabled="!['总经理'].includes(cookiesUserInfo.role_name)"
        >
          <el-option
            v-for="item in employeeList"
            :key="item.employeeId"
            :label="item.employee_number+'-'+item.employee_name"
            :value="item.employeeId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="formSearch.auditState"
          placeholder="请选择审核状态"
          class="inputBox"
          filterable
          clearable
        >
          <el-option
            v-for="item in customer_intention_audit"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.startTime" hint="请选择开始时间"></my-date>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.endTime" hint="请选择结束时间"></my-date>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
        <el-button
          type="primary"
          @click="add"
          v-permit="'ADD_CUSTOMER_INTENTION_NEW'"
        >制单</el-button>
      </el-form-item>
    </el-form>
    <table-view
      :tableList="tableList"
      :tableData="tableData"
      v-if="!isAdd&&!isAudit"
      @del=" del"
      @modify="modify"
      isEdit="UPDATE_CUSTOMER_INTENTION_NEW"
      :isDel="'DEL_CUSTOMER_INTENTION_NEW'"
      isThrid="AUDIT_CUSTOMER_INTENTION_REPORT_NEW"
      :thridTitle="'审核'"
      @thrid="item => toAuditDet(item, '意向报告单审核', 'auditState')"
    ></table-view>
    <Pagination ref="pagination" @success="getList" v-show="!isAdd&&!isAudit" />
    <!-- 新增客户意向单 -->
    <add-intention ref="addIntention" @selectData="getList" />
    <!-- 审核 -->
    <IntentionAudit ref="intentionAudit" v-show="isAudit" @selectData="getList" />
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
.moneyTitle {
  font-size: 14px;
  font-weight: bold;
}

@import "@/assets/css/element/font-color.scss";
</style>