import API from "@/api/internalSystem/customerManage/customerPushLog";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import AddCustomerPush from "./components/addCustomerPush/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import AuditDetail from "@/mixins/auditDetail.js";
import DictAPI from "@/api/internalSystem/dict";
import { mapGetters } from "vuex";
export default {
  name: "customerPush",
  mixins: [AuditDetail],
  data() {
    return {
      title: "客户满意度回访",
      loading: false,
      tableData: [],
      formSearch: {
        customer_name: "",
        send_user_id: "",
        fk_template_id: "",
        startTime: "",
        endTime: "",
      },
      tableList: [
        {
          name: "编号",
          value: "customer_push_no",
        },
        {
          name: "推送模板",
          value: "template_name",
        },
        {
          name: "推送人",
          value: "employee_name",
        },
        {
          name: "计划推送时间",
          value: "plan_send_time",
        },

        {
          name: "推送客户人数",
          value: "customer_number",
        },
        {
          name: "成功推送客户人数",
          value: "success_customer_number",
        },
        {
          name: "收到回访数量",
          value: "feedback_customer_number",
        },
        {
          name: "实际推送时间",
          value: "actual_send_time",
        },
        {
          name: "备注",
          value: "remark",
        },
      ],
      employeeList: [],
      templateOption: [],
      isAdd: false,
      isAudit: false,
    };
  },

  mounted() {
    this.getList();
    this.$store.dispatch("getEmployee").then((res) => {
      this.employeeList = res;
    });
  },
  created() {
    if (!["总经理"].includes(this.cookiesUserInfo.role_name)) {
      this.formSearch.send_user_id = this.cookiesUserInfo.userId;
    }
  },
  methods: {
    // getDict() {
    //   DictAPI.getDict({
    //     type: "customer_push_template",
    //   }).then((res) => {
    //     this.templateOption = res.data;
    //   });
    // },
    getList(f = false) {
      this.isAdd = false;
      this.isAudit = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(
            this.formSearch,
            this.$refs.pagination.obtain()
          );
          // let param ={}
          if (f) param.pageNum = 1;
          param.fk_template_id = 1
          API.query(param)
            .then((res) => {
              this.tableData = res.data;
              this.$refs.pagination.setTotal(res.totalCount);
            })
            .finally(() => {
              this.loading = false;
            });
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addCustomerPush.Show();
    },
    modify(item) {
      this.isAdd = true;
      this.$refs.addCustomerPush.Show(item);
      // let params = {};
      // API.getInfo(params)
      //   .then(data => {
      //     this.isAdd = true;
      //     this.$refs.addCustomerPush.Show(data.data);
      //   })
      //   .catch(() => {});
    },
    del(item) {
      if (item.auditState == 1) return this.error("该单据已审核，不允许删除");
      let params = {
        sales_visiting_id: item.sales_visiting_id,
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
  },

  components: {
    AddCustomerPush,
    Pagination,
    TableView,
    MyDate,
  },
  computed: {
    ...mapGetters(["customer_sales_visiting_audit","cookiesUserInfo"]),
  },
};
