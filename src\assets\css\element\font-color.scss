  //不可编辑字体加黑
  .el-input.is-disabled /deep/ .el-input__inner {
    background-color: #F5F7FA;
    border-color: #E4E7ED;
    color: #000000 ;
    cursor: not-allowed;
}

.formItem{
  margin-top: -6px;
}

.formItem2{
  margin-top: -14px;
}

//输入框
.el-select /deep/ .el-input.is-disabled .el-input__inner {
  cursor: not-allowed;
   color: #000000;
}

.el-form-item--small /deep/ .el-form-item__label {
    line-height: 32px;
    color: black ;
}

.ellipsis{
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  color: black;
  // font-size: initial;  //字体加大
}

//多行文本框
.el-textarea.is-disabled /deep/ .el-textarea__inner {
  background-color: #F5F7FA;
  border-color: #E4E7ED;
  color: #000000;
  cursor: not-allowed;
  font-family: Arial;
}


/deep/ .el-input.is-disabled .el-input__inner {
  background-color: #F5F7FA;
  border-color: #E4E7ED;
  color: #000000;
  cursor: not-allowed;
}

/deep/ .el-input--small .el-input__inner {
  height: 32px;
  line-height: 32px;
  color: black;
}

.formSearch-bottom {
  margin-bottom: -10px;
}

// .blue-row {
//   background-color: #EAF5FF!important;
// }
// .yellow-row {
//   background-color: #FFFAF3!important;
// }

//以下是 vxe-table
/deep/.head-row-class {
  background-color: #409eff;

  .vxe-cell {
    color: #fff;
  }
}
.col-header {
  font-weight: bold;
  // color: #fff;
}
.danger {
  color: #f56c6c;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.tableContent {
  flex: 1;
  height: 99%;
  overflow: hidden;
}

.el-table td,
.building-top .el-table th.is-leaf {
  border-bottom: 1px solid #f00;
}
//以上是vxe-table
