<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="900px"
    :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="110px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="员工工号" prop="employee_number">
            <el-input :disabled="!isEdit" v-model="ruleForm.employee_number" class="inputBox" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="员工名称" prop="employee_name">
            <el-input :disabled="!isEdit" v-model="ruleForm.employee_name" class="inputBox" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="性别" prop="gender">
            <el-select :disabled="!isEdit" v-model="ruleForm.gender" placeholder="请选择性别" class="inputBox"
              filterable clearable>
              <el-option v-for="item in params_constant_gender" :key="item.id" :label="item.label" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="手机号码" prop="telephone">
            <el-input :disabled="!isEdit" v-model="ruleForm.telephone" class="inputBox" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="紧急联系电话" prop="emergency_call">
            <el-input  :disabled="!isEdit" v-model="ruleForm.emergency_call" class="inputBox" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="电子邮箱" prop="email">
            <el-input :disabled="!isEdit" v-model="ruleForm.email" class="inputBox" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="身份证号" prop="id_card">
            <el-input :disabled="!isEdit" v-model="ruleForm.id_card" class="inputBox" @blur="getBirthday" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="身份证期限" prop="card_time">
            <el-date-picker :disabled="!isEdit" style="width:100%" v-model="ruleForm.card_time" class="inputBox"
              align="right" type="date" placeholder="选择身份证期限" value-format="yyyy-MM-dd" format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="出生日期" prop="birthday">
            <el-date-picker :disabled="!isEdit" style="width:100%" v-model="ruleForm.birthday" class="inputBox"
              align="right" type="date" placeholder="选择出生日期" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="学历" prop="education">
            <el-input :disabled="!isEdit" v-model="ruleForm.education" class="inputBox" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="毕业院校" prop="graduate_school">
            <el-input :disabled="!isEdit" v-model="ruleForm.graduate_school" class="inputBox" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="专业" prop="professional">
            <el-input :disabled="!isEdit" v-model="ruleForm.professional" class="inputBox" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所在部门" prop="fk_dept_id">
            <el-select :disabled="!isEdit" v-model="ruleForm.fk_dept_id" placeholder="请选择部门" class="inputBox"
              @change="changeDepartment" filterable clearable>
              <el-option v-for="item in departmentList" :key="item.departmentId" :label="item.departmentName"
                :value="item.departmentId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="岗位" prop="role_id">
            <el-select :disabled="!isEdit" v-model="ruleForm.role_id" placeholder="选择部门获取可选岗位" class="inputBox"
              filterable clearable>
              <el-option v-for="item in jobList" :key="item.roleId" :label="item.roleName" :value="item.roleId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所属上级" prop="superior_id">
            <el-select :disabled="!isEdit" v-model="ruleForm.superior_id" placeholder="选择部门获取可选上级" class="inputBox"
              filterable clearable>
              <el-option v-for="item in superiorList" :key="item.employeeId" :label="item.employee_name"
                :value="item.employeeId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所在省份" prop="province">
            <el-select :disabled="!isEdit" v-model="ruleForm.province" placeholder="请选择省份" class="inputBox"
              @change="changeProvince" filterable clearable>
              <el-option v-for="item in provinceList" :key="item.province" :label="item.origin_place"
                :value="item.province">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所在城市" prop="city">
            <el-select :disabled="!isEdit" v-model="ruleForm.city" placeholder="选择省份获取可选城市" class="inputBox"
              @change="changeCity" filterable clearable>
              <el-option v-for="item in cityList" :key="item.city" :label="item.origin_place" :value="item.city">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="县（区）" prop="county">
            <el-select :disabled="!isEdit" v-model="ruleForm.county" placeholder="选择城市获取可选县（区）" class="inputBox"
              filterable clearable>
              <el-option v-for="item in countyList" :key="item.area" :label="item.origin_place" :value="item.area">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="详细地址" prop="origin_place">
            <el-input :disabled="!isEdit" v-model="ruleForm.origin_place" class="inputBox" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="现居住地" prop="now_address">
            <el-input :disabled="!isEdit" v-model="ruleForm.now_address" class="inputBox" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="籍贯" prop="native_place">
            <el-input :disabled="!isEdit" v-model="ruleForm.native_place" class="inputBox" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="入职时间" prop="entry_time">
            <el-date-picker :disabled="!isEdit" style="width:100%" v-model="ruleForm.entry_time" class="inputBox"
              align="right" type="date" placeholder="选择入职时间" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="离职时间" prop="departure_time">
            <el-date-picker :disabled="!isEdit" style="width:100%" v-model="ruleForm.departure_time" class="inputBox"
              align="right" type="date" placeholder="选择离职时间" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="转正时间" prop="positive_time">
            <el-date-picker :disabled="!isEdit" style="width:100%" v-model="ruleForm.positive_time" class="inputBox"
              align="right" type="date" placeholder="选择转正时间" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="合同到期时间" prop="contract_expires">
            <el-date-picker :disabled="!isEdit" style="width:100%" v-model="ruleForm.contract_expires" class="inputBox"
              align="right" type="date" placeholder="选择合同到期时间" value-format="yyyy-MM-dd" format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="年假天数" prop="annual_leave">
            <el-input  :disabled="!isEdit" v-model="ruleForm.annual_leave" class="inputBox" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="建行卡号" prop="construction_bank">
            <el-input  :disabled="!isEdit" v-model="ruleForm.construction_bank" class="inputBox" clearable>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否在职" prop="in_position">
            <el-select :disabled="!isEdit" v-model="ruleForm.in_position" placeholder="选择是否在职" class="inputBox"
              filterable clearable>
              <el-option v-for="item in in_position" :key="item.id" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注说明" prop="remarks">
            <el-input :disabled="!isEdit" v-model="ruleForm.remarks" class="inputBox" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button v-permit="'UPDATE_EMPLOYEE_NEW'" v-if="(ruleForm.employeeId)||!ruleForm.employeeId" type="primary"
        @click="submitForm('ruleForm')" :loading="loading">保 存</el-button>
      <el-button @click="dialogCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script src="./index.js">

</script>

<style lang="scss" scoped>
  // .inputBox{
  //   width: 300px;
  // }
</style>
