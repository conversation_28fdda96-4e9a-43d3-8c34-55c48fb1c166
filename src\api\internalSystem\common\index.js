import Axios from "@/api";
import environment from "@/api/environment";

export default {
  // 获取登录信息
  getInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}user/getInfo`, params)
  },
  // 退出
  loginOut: params => {
    return Axios.post(`${environment.internalSystemAPI}user/exit`, params)
  },
  // 查询数据字典
  queryDataDictionary: params => {
    return Axios.post(`${environment.internalSystemAPI}dataDictionary/query`, params)
  },
  // 新增数据字典
  addDataDictionary: params => {
    return Axios.post(`${environment.internalSystemAPI}dataDictionary/add`, params)
  },
  // 删除数据字典
  removeDataDictionary: params => {
    return Axios.post(`${environment.internalSystemAPI}dataDictionary/remove`, params)
  },
  // 编辑数据字典
  updateDataDictionary: params => {
    return Axios.post(`${environment.internalSystemAPI}dataDictionary/update`, params)
  },
  // 获取单条信息数据字典
  dataDictionaryInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}dataDictionary/getInfo`, params)
  },
  // 查询地区编码
  queryAreaCode: params => {
    return Axios.post(`${environment.internalSystemAPI}areaCode/query`, params)
  },
  // 上传文件
  uploadFile: params => {
    return Axios.post(`${environment.internalSystemAPI}assets/uploadFile`, params)
  },
  // 预览文件
  filePreview: params => {
    return Axios.get(`${environment.internalSystemAPI}assets/file`, params)
  },
  // 下载文件
  fileDownload: params => {
    return Axios.get(`${environment.internalSystemAPI}assets`, params)
  },
  // 实施单附件列表
  impleFileList: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/fileList`, params)
  },
  // 实施单上传附件
  impleAdd: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/addFile`, params)
  },
  // 实施单删除附件
  impleDel: params => {
    return Axios.post(`${environment.internalSystemAPI}implementation/delFile`, params)
  },
  // 获取审核数量
  getAuditCount: params => {
    return Axios.post(`${environment.internalSystemAPI}auditCenter/getCount`, params)
  },
  // 审核中心
  queryAudit: params => {
    return Axios.post(`${environment.internalSystemAPI}auditCenter/query`, params)
  },
  // 准备转入公海客户
  prepareForOpenSea: params => {
    return Axios.post(`${environment.internalSystemAPI}customer/prepareForOpenSea`, params)
  },
  // 首页经营情况
  getHomeBusiness: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/getHomeBusiness`, params)
  },
  // 我的客户情况
  getHomeCustomer: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/getHomeCustomer`, params)
  },
  // 我的客户情况
  getBalanceDays: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/getBalanceDays`, params)
  },
  // 催缴
  remindCustomer: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/remindCustomer`, params)
  },
  // 列表查询
  getTaskToDoQuery: params => {
    return Axios.post(`${environment.internalSystemAPI}taskTodo/query`, params)
  },
  // 查询合同到期时间
  checkContractExpires: params => {
    return Axios.post(`${environment.internalSystemAPI}user/checkContractExpires`, params)
  },
};