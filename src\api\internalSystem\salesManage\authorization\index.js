import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: params => {
    return Axios.post(`${environment.internalSystemAPI}authorization/query`, params)
  },
  // 查询明细
  detailList: params => {
    return Axios.post(`${environment.internalSystemAPI}authorization/detailList`, params)
  },
  // 查询明细
  detailList2: params => {
    return Axios.post(`${environment.internalSystemAPI}authorization/detailList2`, params)
  },
  // 新增
  add: params => {
    return Axios.post(`${environment.internalSystemAPI}authorization/add`, params)
  },
  // 新增
  add2: params => {
    return Axios.post(`${environment.internalSystemAPI}authorization/add2`, params)
  },
  // 删除
  remove: params => {
    return Axios.post(`${environment.internalSystemAPI}authorization/remove`, params)
  },
  // 编辑
  update: params => {
    return Axios.post(`${environment.internalSystemAPI}authorization/update`, params)
  },
  // 获取单条信息
  getInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}authorization/getInfo`, params)
  },
  // 同步端口
  updateEndState: params => {
    return Axios.post(`${environment.internalSystemAPI}authorization/updateEndState`, params)
  },
  // 根据合同id，查找可以授权的产品明细
  getDetails: params => {
    return Axios.post(`${environment.internalSystemAPI}authorization/getDetails`, params)
  },
  checkContractInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}authorization/checkContractInfo`, params)
  },
  
};