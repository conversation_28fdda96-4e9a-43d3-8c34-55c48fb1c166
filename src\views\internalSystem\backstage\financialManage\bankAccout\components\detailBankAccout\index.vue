<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="1200px"
    :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
    <el-form :inline="true" :model="formSearch" size="small">
      <el-form-item label="查询条件">
        <el-select v-model="formSearch.money_type" placeholder="请选择钱款类型" class="inputBox" filterable clearable>
          <el-option v-for="item in money_type" :key="item.id" :label="item.label" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.startTime" hint="请选择开始时间"></my-date>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.endTime" hint="请选择结束时间"></my-date>
      </el-form-item>
      <el-form-item>
        <el-input v-model="formSearch.money_remark" placeholder="钱款备注" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
        <el-button type="primary" @click="exportBankDetail" :loading="exLoading"
          v-permit="'EXPORT_DETAIL_BANKACCOUT_NEW'">导出
        </el-button>
      </el-form-item>
    </el-form>
    <table-view :tableList="tableList" :tableData="tableData" :tableHeight="520"></table-view>
    <Pagination ref="bank_pagination" @success="getList" />
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script src="./index.js">

</script>