import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: params => Axios.post(`${environment.internalSystemAPI}salesUnit/query`, params),
  // 新增
  add: params => Axios.post(`${environment.internalSystemAPI}salesUnit/add`, params),
  // 删除
  remove: params => Axios.post(`${environment.internalSystemAPI}salesUnit/remove`, params),
  // 编辑
  update: params => Axios.post(`${environment.internalSystemAPI}salesUnit/update`, params),
  // 获取单条信息
  getInfo: params => Axios.post(`${environment.internalSystemAPI}salesUnit/getInfo`, params)
};
