import API from "@/api/internalSystem/salesManage/quotation";
import comAPI from "@/api/internalSystem/common/index.js";
import paramAPI from "@/api/internalSystem/basicManage/parameter";
import brandAPI from "@/api/internalSystem/basicManage/brand";
import CustomerList from "@/views/internalSystem/backstage/components/customerList/index.vue";
import SalesUnitList from "@/views/internalSystem/backstage/components/salesUnitList/index.vue";
import ContractList from "@/views/internalSystem/backstage/components/contractList/index.vue";
import TableCustom from "@/views/internalSystem/backstage/components/tableCustom/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import { getOptions } from "@/common/internalSystem/common.js";
import { mapGetters } from "vuex";
export default {
  name: "addQuotation",
  components: {
    CustomerList,
    SalesUnitList,
    ContractList,
    TableCustom,
    MyDate,
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_name: "",
        fk_customer_id: "",
        train_type: "",
        pay_type: "",
        sell_type: "",
        sales_unit_id: "",
        link_man: "",
        fk_sell_employee_id: "",
        phone: "",
        introducer_name: "",
        introducer: "",
        province: "",
        city: "",
        remark: "",
        customer_address: "",
        fax: "",
        software_no: "",
        link_qq: "",
        sales_unit_id_format: "",
        introducer_contract_format: "",
        introducer_contract_id: "",
      },
      rules: {
        customer_name: [
          {
            required: true,
            message: "请选择客户",
          },
        ],
        sales_unit_id_format: [
          {
            required: true,
            message: "请选择销货单位",
          },
        ],
        train_type: [
          {
            required: true,
            message: "请选择培训方式",
          },
        ],
        pay_type: [
          {
            required: true,
            message: "请选择付款方式",
            trigger: "change",
          },
        ],
        sell_type: [
          {
            required: true,
            message: "请选择销售类型",
            trigger: "change",
          },
        ],
        link_man: [
          {
            required: true,
            message: "请输入联系人",
          },
        ],
        phone: [
          {
            required: true,
            message: "请输入联系电话",
          },
        ],
        sales_unit_id: [
          {
            required: true,
            message: "请选择销货单位",
          },
        ],
      },
      trainTypeList: [], //培训方式
      payTypeList: [], //付款方式
      sellTypeList: [], //销售类型
      provinceList: [], //省
      cityList: [], //市
      softwareVersionList: [], //软件版本
      measurementUnitList: [], //计量单位
      paramsList: [],
      loading: false,
      activeName: "first",
      proList: [], //产品列表
      moduleList: [], //模块列表
      quotationRateList: [], //报价税率
      prepaymentRatioList: [], //预付款比例
      yearsFeeList: [], //年维护费比例
      proTableCol: [
        {
          label: "产品服务",
          prop: "brand_id",
          need: true,
          width: 160,
        },
        {
          label: "软件版本",
          prop: "software_version",
          need: true,
          width: 160,
        },
        {
          label: "计量单位",
          prop: "measurement_unit",
          need: true,
          width: 160,
        },
        {
          label: "报价数量",
          prop: "quotation_number",
          width: 160,
        },
        {
          label: "报价单价(元)",
          prop: "quotation_unit",
          need: true,
          width: 160,
        },
        {
          label: "原有端口数",
          prop: "qriginal_port_number",
          width: 160,
        },
        {
          label: "新增端口数",
          prop: "new_port_number",
          width: 160,
        },
        {
          label: "备注",
          prop: "remark",
          width: 160,
        },
        {
          label: "报价税率",
          prop: "quotation_rate",
          width: 160,
        },
        {
          label: "预付款比例%",
          prop: "prepayment_ratio",
          width: 160,
        },
        {
          label: "年维护费比例%",
          prop: "years_maintenance_fee",
          width: 160,
        },
        {
          label: "维护起始日期",
          prop: "maintain_start_time",
          need: true,
          width: 160,
        },
        {
          label: "新维护结束日期",
          prop: "new_maintain_stop_time",
          need: true,
          width: 160,
        },
        {
          label: "原维护结束日期",
          prop: "original_maintain_stop_time",
          width: 160,
        },
        {
          label: "付款天数",
          prop: "payment_day",
          need: true,
          width: 160,
        },
      ],
      proObj: {},
      moduleTableCol: [
        {
          label: "模块名称",
          prop: "brand_id",
          need: true,
        },
        {
          label: "计量单位",
          prop: "measurement_unit",
          need: true,
        },
        {
          label: "报价单价(元)",
          prop: "quotation_unit",
          need: true,
        },
        {
          label: "备注",
          prop: "remark",
        },
      ],
      moduleObj: {},
      isEdit: false,
      isOwn: true,
    };
  },
  methods: {
    async Show(data = null) {
      this.dialogVisible = true;
      this.proList = [];
      this.moduleList = [];
      this.softwareVersionList = [];
      this.measurementUnitList = [];
      this.quotationRateList = [];
      this.prepaymentRatioList = [];
      this.yearsFeeList = [];
      this.getProvinceList();
      this.trainTypeList = getOptions("t_quotation", "train_type");
      this.payTypeList = getOptions("t_quotation", "pay_type");
      this.sellTypeList = getOptions("t_quotation", "sell_type");
      this.softwareVersionList = getOptions(
        "t_quotation_detail",
        "software_version"
      );
      this.measurementUnitList = getOptions(
        "t_quotation_detail",
        "measurement_unit"
      );
      this.activeName = "first";
      this.softwareVersionList.forEach((item) => {
        item.label = item.sysName;
        item.value = item.sysValue;
      });
      this.measurementUnitList.forEach((item) => {
        item.label = item.sysName;
        item.value = item.sysValue;
      });
      this.ruleForm.add_user_name = this.userInfo.fullName;
      this.ruleForm.add_user_department_name = this.userInfo.department_name;
      this.isEdit = true;
      this.isOwn = true;
      await this.getBrand();
      await this.getParam();
      this.proObj = {
        brand_id: {
          value: "",
          type: "select",
          option: this.proList,
        },
        software_version: {
          value: "",
          type: "select",
          option: this.software_version,
        },
        measurement_unit: {
          value: "",
          type: "select",
          option: this.measurement_unit,
        },
        quotation_number: {
          value: "1",
          type: "input",
          disabled: true,
        },
        quotation_unit: {
          value: "",
          type: "float",
        },
        qriginal_port_number: {
          value: 0,
          type: "number",
          disabled: true
        },
        new_port_number: {
          value: "",
          type: "number",
        },
        remark: {
          value: "",
          type: "input",
        },
        quotation_rate: {
          value: "",
          type: "select",
          option: this.quotationRateList,
        },
        prepayment_ratio: {
          value: "",
          type: "select",
          option: this.prepaymentRatioList,
        },
        years_maintenance_fee: {
          value: "",
          type: "select",
          option: this.yearsFeeList,
        },
        maintain_start_time: {
          value: "",
          type: "date",
        },
        new_maintain_stop_time: {
          value: "",
          type: "date",
        },
        original_maintain_stop_time: {
          value: "",
          type: "date",
        },
        payment_day: {
          value: "",
          type: "number",
        },
      };
      this.moduleObj = {
        brand_id: {
          value: "",
          type: "select",
          option: this.moduleList,
        },
        measurement_unit: {
          value: "",
          type: "select",
          option: this.measurement_unit,
        },
        quotation_unit: {
          value: "",
          type: "input",
        },
        remark: {
          value: "",
          type: "input",
        },
      };
      if (data) {
        this.ruleForm = data;
        // this.isEdit = this.existence(this.buttonPermissions, 'update')) : false) && data.audit_state != 1;
        this.isEdit = this.permissionToCheck("update") && data.audit_state != 1;
        this.isOwn = data.update_user_id === this.userInfo.userId;
        this.getCityList();
        API.detailList({
          quotation_id: data.quotation_id,
        })
          .then((res) => {
            res.data.map((item) => {
              let pro = JSON.parse(JSON.stringify(this.proObj));
              let module = JSON.parse(JSON.stringify(this.moduleObj));
              for (let v in item) {
                if (item.detail_type == 1) {
                  if (pro[v]) {
                    pro[v].value = item[v];
                    if (v !== "quotation_number")
                      pro[v].disabled = !this.isEdit || !this.isOwn;
                  }
                } else {
                  if (module[v]) {
                    module[v].value = item[v];
                    module[v].disabled = !this.isEdit || !this.isOwn;
                  }
                }
              }
              if (item.detail_type == 1) {
                this.$refs.proTableCustom.add2(pro);
              } else {
                this.$refs.moduleTableCustom.add2(module);
              }
            });
          })
          .catch(() => {})
          .finally(() => {});
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm("ruleForm");
      this.clearData();
      this.$emit("selectData");
    },
    async save() {
      let params = this.ruleForm;
      let proList = [];
      try {
        proList = await this.$refs.proTableCustom.getData();
      } catch {
        this.activeName = "first";
        return;
      }
      let moduleList = [];
      try {
        moduleList = await this.$refs.moduleTableCustom.getData();
      } catch {
        this.activeName = "second";
        return;
      }
      let detail = [];
      if (proList.length != 1) return this.error("产品有且只能有一条数据");
      if (moduleList.length == 0) return this.error("模块至少有一条数据");
      proList.forEach((item) => {
        detail.push({
          brand_id: item.brand_id.value,
          software_version: item.software_version.value,
          measurement_unit: item.measurement_unit.value,
          quotation_number: item.quotation_number.value,
          quotation_unit: item.quotation_unit.value,
          qriginal_port_number: item.qriginal_port_number.value,
          new_port_number: item.new_port_number.value,
          remark: item.remark.value,
          quotation_rate: item.quotation_rate.value,
          prepayment_ratio: item.prepayment_ratio.value,
          years_maintenance_fee: item.years_maintenance_fee.value,
          maintain_start_time: item.maintain_start_time.value,
          new_maintain_stop_time: item.new_maintain_stop_time.value,
          original_maintain_stop_time: item.original_maintain_stop_time.value,
          payment_day: item.payment_day.value,
          detail_type: 1,
        });
      });
      moduleList.forEach((item) => {
        detail.push({
          brand_id: item.brand_id.value,
          measurement_unit: item.measurement_unit.value,
          quotation_unit: item.quotation_unit.value,
          remark: item.remark.value,
          detail_type: 2,
        });
      });
      params.detail = detail;
      this.loading = true;
      if (params.quotation_id) {
        if (params.audit_state == 1)
          return this.error("该单据已审核，不允许修改！");
        API.update(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {})
          .finally(() => {
            this.loading = false;
          });
      } else {
        API.add(params)
          .then(() => {
            this.dialogCancel();
          })
          .catch(() => {})
          .finally(() => {
            this.loading = false;
          });
      }
    },
    back() {
      this.$confirm("此操作将回退该条记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            quotation_id: this.ruleForm.quotation_id,
            auditState: 3,
          };

          API.updateAudit(params)
            .then(() => {
              this.dialogCancel();
            })
            .catch(() => {})
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {});
    },
    getBrand() {
      return new Promise((resolve, reject) => {
        brandAPI
          .query()
          .then((data) => {
            data.data.forEach((item) => {
              if (item.brand_classify == 1)
                this.proList.push({
                  label: item.brand_type,
                  value: item.brand_id,
                });
              else if (item.brand_classify == 2)
                this.moduleList.push({
                  label: item.brand_type,
                  value: item.brand_id,
                });
            });
            resolve(1);
          })
          .catch(() => {
            reject();
          });
      });
    },
    getParam() {
      paramAPI
        .query()
        .then((data) => {
          data.data.forEach((item) => {
            if (item.parameter_type == 5)
              this.quotationRateList.push({
                label: item.content,
                value: item.content,
              });
            else if (item.parameter_type == 1)
              this.prepaymentRatioList.push({
                label: item.content,
                value: item.content,
              });
            else if (item.parameter_type == 2)
              this.yearsFeeList.push({
                label: item.content,
                value: item.content,
              });
          });
        })
        .catch(() => {});
    },
    add() {
      if (!this.ruleForm.fk_customer_id) return this.error('请选择客户')
      if (this.activeName === "first") {
        let list = this.$refs.proTableCustom.getData2();
        if (list.length === 0) this.$refs.proTableCustom.add2(this.proObj);
        else this.error("只能新增一条产品信息");
      } else if (this.activeName === "second")
        this.$refs.moduleTableCustom.add();
    },
    //选择介绍人
    chooseIntroducer() {
      this.$refs.dealCustomerList.Show();
    },
    //获取介绍人
    getInfo(info = {}) {
      this.ruleForm.introducer_name = info.customer_name;
      this.ruleForm.introducer = info.customer_id;
    },
    //选择客户
    chooseCustomer() {
      this.$refs.customerList.Show();
    },
    getCustomerInfo(info = {}) {
      this.ruleForm.customer_name = info.customer_name;
      this.ruleForm.fk_customer_id = info.customer_id;
      this.ruleForm.fk_sell_employee_id = info.fk_sale_employee_id;
      this.ruleForm.fk_sell_employee_name = info.fk_sale_employee_id_name;
      this.ruleForm.fk_sell_department_name = info.department_name;
      this.ruleForm.link_man = info.link_man;
      this.ruleForm.customer_address = info.link_address;
      this.ruleForm.fax = info.fax;
      this.ruleForm.province = info.province;
      if (info.province) {
        this.getCityList();
      }
      this.ruleForm.city = info.city;
      this.ruleForm.link_qq = info.qq;
      if (info.phone) this.ruleForm.phone = info.phone;
      else this.ruleForm.phone = info.telephone;
      this.ruleForm.introducer_name = info.introducer_format;
      this.ruleForm.introducer = info.introducer;

      this.proObj.qriginal_port_number.value = info.port_number; // 端口数
      this.$refs.proTableCustom.add2(this.proObj);
    },
    //选择销货单位
    chooseCompany() {
      this.$refs.salesUnitList.Show();
    },
    getCampanyInfo(info = {}) {
      this.ruleForm.sales_unit_id = info.sales_unit_id;
      this.ruleForm.sales_unit_id_format = info.company_name;
    },
    //选择介绍合同
    chooseContract() {
      this.$refs.contractList.Show();
    },
    getContractInfo(info = {}) {
      this.ruleForm.introducer_contract_format = info.contract_no;
      this.ruleForm.introducer_contract_id = info.customer_contract_id;
    },
    //获取省列表
    getProvinceList() {
      comAPI
        .queryAreaCode({
          level: 1,
        })
        .then((data) => {
          this.provinceList = data.data;
        })
        .catch(() => {});
    },
    //更换省
    changeProvince() {
      this.cityList = [];
      this.ruleForm.city = "";
      this.getCityList();
    },
    //获取市列表
    getCityList() {
      if (!this.ruleForm.province) return;
      comAPI
        .queryAreaCode({
          level: 2,
          province: this.ruleForm.province,
        })
        .then((data) => {
          this.cityList = data.data;
        })
        .catch(() => {});
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_name: "",
        fk_customer_id: "",
        train_type: "",
        pay_type: "",
        sell_type: "",
        sales_unit_id: "",
        link_man: "",
        fk_sell_employee_id: "",
        phone: "",
        introducer_name: "",
        introducer: "",
        province: "",
        city: "",
        remark: "",
        customer_address: "",
        fax: "",
        software_no: "",
        link_qq: "",
        sales_unit_id_format: "",
        introducer_contract_format: "",
        introducer_contract_id: "",
      };
    },
  },
  computed: {
    ...mapGetters([
      "userInfo",
      "buttonPermissions",
      "sell_type",
      "contract_pay_type",
      "contract_train_type",
      "software_version",
      "measurement_unit",
    ]),
  },
};
