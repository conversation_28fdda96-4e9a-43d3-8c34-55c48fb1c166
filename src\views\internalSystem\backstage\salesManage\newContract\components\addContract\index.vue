<template>
  <div
    class="body-p10"
    style="overflow-y:auto;overflow-x: hidden;"
    v-if="dialogVisible"
  >
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button
        v-if="
          (isEdit && isOwn && ruleForm.customer_contract_id) ||
            !ruleForm.customer_contract_id
        "
        type="primary"
        @click="submitForm('ruleForm')"
        :loading="loading"
        >保 存</el-button
      >
      <el-button
        v-permit="'SEND_CONTRACT_NEW'"
        v-if="
          ruleForm.customer_contract_id && ruleForm.audit_state == 4 && isOwn
        "
        type="primary"
        @click="send"
        :loading="loading"
        >发出</el-button
      >
      <el-button
        v-if="
          ruleForm.customer_contract_id &&
            ruleForm.audit_state != 0 &&
            ruleForm.audit_state != 4
        "
        type="primary"
        v-permit="'AUDIT_BACK_CONTRACT_NEW'"
        @click="back"
        :loading="loading"
        >回退</el-button
      >
      <el-button
        v-permit="'FILE_CONTRACT_NEW'"
        v-if="ruleForm.customer_contract_id"
        type="primary"
        @click="addFile"
        >附件</el-button
      >
      <el-dropdown
        @command="handleCommand"
        class="ml5"
        v-permit="'PRINT_CONTRACT_NEW'"
        v-if="ruleForm.customer_contract_id"
      >
        <el-button type="primary">打印合同</el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="update">修改模板</el-dropdown-item>
          <el-dropdown-item command="select">选择模板</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="mt10"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.customer_name"
              placeholder="请点击选择客户"
              @focus="chooseCustomer"
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人" prop="link_man">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.link_man"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人QQ" prop="link_qq">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.link_qq"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销货单位" prop="sales_unit_id_format">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.sales_unit_id_format"
              @focus="chooseCompany"
              placeholder="请点击选择销货单位"
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所属部门" prop="fk_operator_department_name">
            <el-input
              v-model="ruleForm.fk_operator_department_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="制单员" prop="add_user_name">
            <el-input
              v-model="ruleForm.add_user_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="付款方式" prop="pay_type">
            <el-select
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.pay_type"
              placeholder="请选择付款方式"
              filterable
              clearable
            >
              <el-option
                v-for="item in contract_pay_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="培训方式" prop="train_type">
            <el-select
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.train_type"
              placeholder="请选择培训方式"
              filterable
              clearable
            >
              <el-option
                v-for="item in contract_train_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售类型" prop="sale_type">
            <el-select
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.sale_type"
              placeholder="请选择销售类型"
              filterable
              clearable
              @change="sellTypeChange"
            >
              <el-option
                v-for="item in sale_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="8">
          <el-form-item label="所在省份" prop="province">
            <el-select
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.province"
              placeholder="请选择省份"
              @change="changeProvince"
              filterable
              clearable
            >
              <el-option
                v-for="item in provinceList"
                :key="item.province"
                :label="item.origin_place"
                :value="item.province"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8">
          <el-form-item label="所在城市" prop="city">
            <el-select
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.city"
              placeholder="选择省份获取可选城市"
              filterable
              clearable
            >
              <el-option
                v-for="item in cityList"
                :key="item.city"
                :label="item.origin_place"
                :value="item.city"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="8">
          <el-form-item label="详细地址" prop="address">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.address"
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->

        <el-col :span="8">
          <el-form-item label="介绍人" prop="introducer_format">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.introducer_format"
              placeholder="请点击选择介绍人"
              @focus="chooseIntroducer"
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="介绍合同" prop="introducer_contract_format">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.introducer_contract_format"
              placeholder="请点击选择介绍合同"
              @focus="chooseContract"
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="续费合同" prop="renewal_contract_format">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.renewal_contract_format"
              placeholder="请点击选择续费合同"
              @focus="chooseRenewContract"
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="8">
          <el-form-item label="销售员" prop="fk_sell_employee_name">
            <el-input v-model="ruleForm.fk_sell_employee_name" disabled clearable></el-input>
          </el-form-item>
        </el-col>
      

        <el-col :span="8">
          <el-form-item label="销售员部门" prop="fk_sell_department_name">
            <el-input v-model="ruleForm.fk_sell_department_name" disabled clearable></el-input>
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="客户传真" prop="fax">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.fax"  clearable></el-input>
          </el-form-item>
        </el-col>
    
      
        <el-col :span="8">
          <el-form-item label="手机" prop="phone">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.phone"  clearable></el-input>
          </el-form-item>
        </el-col> 
        -->

        <el-col :span="8">
          <el-form-item label="是否开票" prop="is_open_bill">
            <el-select
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.is_open_bill"
              placeholder="选择是否开票"
              filterable
              clearable
            >
              <el-option
                v-for="item in is_open_bill"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实施人员" prop="implement_id">
            <el-select
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.implement_id"
              placeholder="请选择实施人员"
              filterable
              clearable
            >
              <el-option
                v-for="item in employeeList"
                :key="item.employeeId"
                :label="item.employee_number + '-' + item.employee_name"
                :value="item.employeeId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="培训人员" prop="train_id">
            <el-select
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.train_id"
              placeholder="请选择培训人员"
              filterable
              clearable
            >
              <el-option
                v-for="item in employeeList"
                :key="item.employeeId"
                :label="item.employee_number + '-' + item.employee_name"
                :value="item.employeeId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="单据备注" prop="remark">
            <el-input
              :disabled="!isEdit || !isOwn"
              v-model="ruleForm.remark"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="推荐员工" prop="fk_recommend_employee_id">
            <el-select
              :disabled="!isEdit||!isOwn"
              v-model="ruleForm.fk_recommend_employee_id"
              placeholder="请选择推荐员工"
              filterable
              clearable
            >
              <el-option
                v-for="item in employeeList"
                :key="item.employeeId"
                :label="item.employee_name"
                :value="item.employeeId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->

        <el-col :span="8" v-if="ruleForm.customer_contract_id">
          <el-form-item label="审核状态" prop="audit_state_name">
            <el-input
              disabled
              v-model="ruleForm.audit_state_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="ruleForm.customer_contract_id">
          <el-form-item label="审核备注" prop="audit_remark">
            <el-input
              disabled
              v-model="ruleForm.audit_remark"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-button
        v-if="
          (isEdit && isOwn && ruleForm.customer_contract_id) ||
            !ruleForm.customer_contract_id
        "
        type="primary"
        @click="add"
        >新增</el-button
      >
      <el-button
        v-if="
          (isEdit && isOwn && ruleForm.customer_contract_id) ||
            !ruleForm.customer_contract_id
        "
        type="primary"
        @click="callIn"
        >调入报价单</el-button
      >
      <el-tabs v-model="activeName">
        <el-tab-pane label="合同产品" name="first">
          <table-custom
            ref="proTableCustom"
            :obj="proObj"
            :tableCol="proTableCol"
            @del="del"
            :isDel="
              !!(isEdit && isOwn && ruleForm.customer_contract_id) ||
                !ruleForm.customer_contract_id
            "
          />
        </el-tab-pane>
        <el-tab-pane label="功能模块" name="second">
          <table-custom
            ref="moduleTableCustom"
            :obj="moduleObj"
            :tableCol="moduleTableCol"
            @del="del"
            :isDel="
              !!(isEdit && isOwn && ruleForm.customer_contract_id) ||
                !ruleForm.customer_contract_id
            "
          />
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <customer-list
      ref="dealCustomerList"
      dialogTitle="成交客户列表"
      :customerStage="3"
      @getInfo="getInfo"
    />
    <customer-list
      ref="customerList"
      dialogTitle="客户列表"
      :isJudge="true"
      @getInfo="getCustomerInfo"
    />
    <sales-unit-list
      ref="salesUnitList"
      dialogTitle="销货单位列表"
      @getInfo="getCampanyInfo"
    />
    <contract-list
      ref="contractList"
      dialogTitle="合同列表"
      :isJudge="true"
      @getInfo="getContractInfo"
    />
    <contract-list
      ref="renewContractList"
      dialogTitle="合同列表"
      :isOwn="true"
      @getInfo="getRenewContractInfo"
    />
    <quotation-list
      ref="quotationList"
      dialogTitle="报价单列表"
      :customerId="ruleForm.fk_customer_id ? ruleForm.fk_customer_id : 0"
      @getInfo="getQuotationInfo"
    />
    <file-list
      ref="fileList"
      :fileId="ruleForm.customer_contract_id"
      fileType="contract"
    />

    <el-dialog
      title="打印合同"
      :visible.sync="dialogVisibleTem"
      append-to-body
      @close="dialogCancelTem"
      width="660px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <el-form
        :model="temForm"
        :rules="temRules"
        ref="temForm"
        label-width="100px"
      >
        <el-form-item label="合同模板" prop="contract_template_id">
          <el-select
            v-model="temForm.contract_template_id"
            placeholder="请选择合同模板"
            filterable
            clearable
          >
            <el-option
              v-for="item in temList"
              :key="item.contract_template_id"
              :label="item.contract_template_name"
              :value="item.contract_template_id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTem('temForm')">确定</el-button>
        <el-button @click="dialogCancelTem">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script src="./index.js"></script>

<style lang="scss" scoped>
.el-dropdown {
  vertical-align: top;
}

.el-dropdown + .el-dropdown {
  margin-left: 15px;
}
</style>
