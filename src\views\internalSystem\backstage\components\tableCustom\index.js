import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import BankList from "@/views/internalSystem/backstage/components/bankList/index.vue";
import CostList from "@/views/internalSystem/backstage/components/costList/index.vue";
import EmployeeList from "@/views/internalSystem/backstage/components/employeeList/index.vue";
import { dateFormat } from "@/common/internalSystem/common.js";
import { sortTable } from "@/common/global/vxeTableDrag.js";
import brandAPI from "@/api/internalSystem/basicManage/brand";
import { checkMaintenanceFee } from "@/utils/calculate.js";
import customerBrandAPI from "@/api/internalSystem/customerManage/customerBrand/index.js";
import moment from "moment";
export default {
  name: "tableCustom",
  components: {
    MyDate,
    BankList,
    CostList,
    EmployeeList,
  },
  props: {
    tableCol: {
      type: Array,
      default() {
        return [
          {
            label: "模块名称",
            prop: "name",
          },
          {
            label: "计量单位",
            prop: "unit",
          },
          {
            label: "报价单位(元)",
            prop: "price",
          },
        ];
      },
    },
    obj: {
      type: Object,
      default() {
        return {
          name: {
            value: "",
            type: "select",
            option: [
              {
                label: "选项一",
                value: "1",
              },
            ],
          },
          unit: {
            value: "",
            type: "select",
            option: [
              {
                label: "选项一",
                value: "1",
              },
            ],
          },
          price: {
            value: "",
            type: "input",
          },
        };
      },
    },
    isDel: {
      type: Boolean,
      default: true,
    },
    itemFlag: {
      type: String,
      default: "",
    },
    special: {
      type: String,
      default: "",
    },
    quotationRateList: {
      type: Array,
      default() {
        return [];
      },
    },
    ruleForm: {
      type: Object,
      default() {
        return null;
      },
    },
    tableHeight: {
      type: [Number, String],
      default: "100%",
    },
    tableId: {
      default: "tableCustom",
    },
    isSaveField: {
      type: Boolean,
      default: true,
    },
    parentComponentName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      form: {
        tableData: [],
      },
      rules: {
        input: [
          {
            required: true,
            message: "请填写",
            trigger: "blur",
          },
        ],
        select: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        number: [
          {
            required: true,
            message: "请输入合法的整数",
          },
          {
            pattern: /^[1-9]\d*$/,
            message: "必须为整数",
            trigger: "change",
          },
        ],
        float: [
          {
            required: true,
            message: "请输入合法的数字",
          },
          {
            pattern: /^(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*))$/,
            message: "请输入合法的数字",
          },
        ],
        dialog: [
          {
            required: true,
            message: "请选择",
          },
        ],
      },
      index: 0,
      loading: false,
      editRules: {},
      headerMenus: [
        [
          {
            code: "fixedLeft",
            name: "固定左边",
            visible: true,
            disabled: false,
          },
          {
            code: "fixedRight",
            name: "固定右边",
            visible: true,
            disabled: false,
          },
          {
            code: "unfixed",
            name: "取消固定",
            visible: true,
            disabled: false,
          },
        ],
      ],
      isFixed: {},
      sortable: "",
      currentFieldList: [],
    };
  },
  mounted() {
    this.columnDrop();
    let internalSystemFixed = JSON.parse(
      localStorage.getItem("internalSystemFixed")
    );
    if (internalSystemFixed && internalSystemFixed[this.$route.name])
      this.isFixed = internalSystemFixed[this.$route.name];
    this.$nextTick(() => {
      this.$refs.xTable.refreshColumn();
    });
  },
  beforeDestroy() {
    if (this.sortable) {
      this.sortable.destroy();
    }
  },
  methods: {
    selectValue(obj) {
      obj.currentTarget.select();
    },
    columnDrop() {
      this.$nextTick(() => {
        let xTable = this.$refs.xTable;
        this.sortable = sortTable(xTable);
      });
    },
    contextMenuClickEvent({ menu, column }) {
      let value = null;
      switch (menu.code) {
        case "fixedLeft":
          // 示例
          value = "left";
          break;
        case "fixedRight":
          // 示例
          value = "right";
          break;
        case "unfixed":
          // 示例
          value = null;
          break;
      }
      this.$set(this.isFixed, column.property, value);
      let internalSystemFixed =
        JSON.parse(localStorage.getItem("internalSystemFixed")) || {};
      internalSystemFixed[this.$route.name] = this.isFixed;
      localStorage.setItem(
        "internalSystemFixed",
        JSON.stringify(internalSystemFixed)
      );
      this.$nextTick(() => {
        this.$refs.xTable.refreshColumn();
      });
    },
    /**
     * 合同产品同步功能模块
     */
    // proSameModule(proList) {
    //   this.form.tableData = [];
    //   let item = {};
    //   proList.forEach((element) => {
    //     // console.log(element)
    //     item = JSON.parse(JSON.stringify(this.obj));
    //     // console.log(item);
    //     item.contract_amount.value = element.contract_amount.value; //合同金额
    //     item.measurement_unit.value = element.measurement_unit.value; //计量单位 measurement_unit
    //     item.fk_brand_id.value = element.fk_brand_id.value; //模块名称 fk_brand_id
    //     this.form.tableData.push(item);
    //   });
    // },

    //新增
    add() {
      this.form.tableData.push(JSON.parse(JSON.stringify(this.obj)));
    },
    //回填
    add2(obj) {
      this.form.tableData.push(JSON.parse(JSON.stringify(obj)));
    },
    //删除
    del(index) {
      let info = this.form.tableData[index.rowIndex];
      this.$emit("del", info);
      this.form.tableData.splice(index.rowIndex, 1);
    },
    //删除
    delBy(id, data) {
      let delIndex = "";
      this.form.tableData.map((item, index) => {
        if (item[id].value === data) {
          delIndex = index;
        }
      });
      this.form.tableData.splice(delIndex, 1);
    },
    empty() {
      this.form.tableData = [];
    },
    //下拉框显示
    getSelectShowData(arr, value, colData) {
      if (!Array.isArray(arr) || !arr.length) return value;
      let name = "value";
      if (arr[0].value === undefined) {
        name = "id";
      }
      let selectObj = arr.find((item) => item[name] === value);
      if (!selectObj) return value;
      return selectObj.label + (colData.isJoin && selectObj.label ? "%" : "");
    },
    //校验方法
    validate({ row, column, cellValue }) {
      return new Promise((resolve, reject) => {
        // setTimeout(() => {

          //增对赠送端口，单价可以为0
          if (
            column.property === "contract_price" ||
            column.property === "contract_amount"
          ) {
            if (row["detail_sell_type"].value === 8) {
              resolve();
            }
          }
          if (column.property === "open_ticket_unit") {
            if (parseFloat(row["open_ticket_unit"].value) === 0) {
              resolve();
            }
          }

          if (cellValue.value === "" || cellValue.value === undefined) {
            let errMsg = ["dialog", "select"].includes(
              row[column.property].type
            )
              ? "请选择"
              : "请输入";

            errMsg += column.title;

            reject(new Error("此项为必填"));
          } else {
            if (row[column.property].type === "number") {
              let pattern = /^[1-9]\d*$/;
              pattern.test(cellValue.value)
                ? resolve()
                : reject(new Error("此项必须为整数"));
            } else if (row[column.property].type === "float") {
              let pattern = /^(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*))$/;
              pattern.test(cellValue.value)
                ? resolve()
                : reject(new Error("此项必须为合法的数字"));
            } else {
              resolve();
            }
          }
        // }, 100);
      });
    },
    tableValid(xTable, tableName = "表格") {
      return new Promise((resolve, reject) => {
        xTable.fullValidate((errMap) => {
          if (errMap) {
            let msgList = [];

            Object.values(errMap).forEach((errList) => {
              errList.forEach((params) => {
                let { rowIndex, column, rules } = params;

                rules.forEach((rule) => {
                  msgList.push(
                    `${tableName}第 ${rowIndex + 1} 行 ${
                      column.title
                    } 校验错误：${rule.message}`
                  );
                });
              });
            });
            let msgTopsOne = msgList[0]; //只报第一条的消息
            this.$message({
              type: "error",
              dangerouslyUseHTMLString: true,
              duration: 5000,
              showClose: true,
              offset: 40,
              message: msgTopsOne,
            });
            reject("表格必填项校验失败");
          } else {
            resolve();
          }
        });
      });
    },
    //键盘操作
    keyDownMethods(data) {
      const keyCodes = ["Tab", "Enter", "NumpadEnter"];
      if (keyCodes.includes(data.$event.code)) {
        this.$nextTick(() => {
          // setTimeout(() => {
            if (keyCodes.includes(data.$event.code)) {
              let cellInfo = this.$refs.xTable.getSelectedCell();

              if (!cellInfo || !cellInfo.row || !cellInfo.column) {
                // this.$refs.xTable.insertAt(this.tableRecord, -1);
                return;
              }
              let { row, column } = cellInfo;
              this.$refs.xTable.setActiveCell(row, column.property);
            }
          // }, 100);
        });
      }
    },
    //生成校验
    getData() {
      return new Promise((resolve, reject) => {
        this.$refs.xTable.clearActived();
        let editRules = {};
        let tableData = this.$refs.xTable.getTableData().fullData;
        if (!tableData.length) return true;

        this.tableCol
          .filter((item) => item.need)
          .forEach((item) => {
            editRules[item.prop] = [
              {
                validator: this.validate,
                trigger: ["change", "blur"],
              },
            ];
          });

        this.editRules = editRules;
        this.$nextTick(() => {
          this.tableValid(this.$refs.xTable)
            .then(() => {
              resolve(this.form.tableData);
            })
            .catch(() => {
              reject("表格必填项校验失败");
            });
        });
      });
    },
    //获取数据
    getData3() {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate((valid) => {
          if (valid) {
            resolve(this.form.tableData);
          } else {
            reject("表单验证未通过");
          }
        });
      });
    },
    //获取数据
    getData2() {
      return this.form.tableData;
    },
    priceFn(e, item, obj, prop) {
      e = e || 0;
      switch (item.fn) {
        case "f1":
          if (obj.open_ticket_number.value) {
            let tax = (parseFloat(e) * obj.invoice_tax_rate.value) / 100;
            obj.tax.value = tax * obj.open_ticket_number.value;
            obj.buckle_open_ticket_unit.value = parseFloat(e) - tax;
            obj.open_ticket_money.value =
              obj.buckle_open_ticket_unit.value * obj.open_ticket_number.value;
            obj.leved_total.value =
              obj.open_ticket_money.value + tax * obj.open_ticket_number.value;
          }
          break;
        case "f2":
          if (obj.open_ticket_unit.value) {
            let tax =
              (obj.invoice_tax_rate.value * obj.open_ticket_unit.value) / 100;
            obj.tax.value = tax * parseFloat(e);
            obj.buckle_open_ticket_unit.value =
              obj.open_ticket_unit.value - tax;
            obj.open_ticket_money.value =
              obj.buckle_open_ticket_unit.value * parseFloat(e);
            obj.leved_total.value =
              obj.open_ticket_money.value + tax * parseFloat(e);
          }
          break;
        case "f3": //合同数量
          obj.contract_amount.value = this.checkContractAmount(obj);
          obj.maintenance_fee.value = this.checkMaintenanceFee(obj);
            if(obj['detail_sell_type']  &&  (obj['detail_sell_type'].value == 5 || obj['detail_sell_type'].value == 3) ){
               // 按合同数
               let d = new Date();
               d = new Date(obj.maintain_start_time.value);

               d.setFullYear(d.getFullYear() + (Number(obj['contract_count'].value) || 3));
               obj.new_maintain_stop_time.value = dateFormat(
                 "yyyy-MM-dd",
                 new Date(d.getTime() ) // 去掉少一天 - 1000 * 60 * 60 * 24
               );
            }
          
          break;
        default:
          break;
      }
    },
    //选择
    choose(type, row) {
      this.index = row.rowIndex;
      if (type === "bank") {
        this.$refs.bankList.Show();
      } else if (type === "cost") {
        this.$refs.costList.Show();
      } else if (type === "employee") {
        this.$refs.employeeList.Show();
      }
    },
    getBankInfo(info = {}) {
      this.form.tableData[this.index].bank_name.value = info.bank_name;
      this.form.tableData[this.index].fk_bank_id.value =
        info.financial_bank_accout_id;
    },
    getCostInfo(info = {}) {
      this.form.tableData[this.index].cost_project_no.value = info.project_no;
      this.form.tableData[this.index].cost_project_name.value =
        info.project_name;
      this.form.tableData[this.index].cost_detail_no.value =
        info.cost_detail_no;
      this.form.tableData[this.index].cost_detail_name.value =
        info.cost_detail_name;
    },
    getEmployeeInfo(info = {}) {
      this.form.tableData[this.index].employee_name.value = info.employee_name;
      this.form.tableData[this.index].department_name.value =
        info.department_name;
    },
    clear() {
      this.form.tableData = [];
    },
    /**
     * 原有端口数赋值
     * @param {*} params
     */
    async putCustomerPort(params) {
      if (this.form.tableData && this.form.tableData.length > 0) {
        let { data } = await brandAPI.getPortBrand();
        for (let i = 0; i < this.form.tableData.length; i++) {
          if (data.includes(this.form.tableData[i].fk_brand_id.value)) {
            this.form.tableData[i].original_port_count.value =
              params.port_number;
          } else {
            this.form.tableData[i].original_port_count.value = 0;
          }
        }
        this.form.tableData = [...this.form.tableData];
      }
    },
    /**
     * 产品授权码赋值
     */
    async putBrandSoftwareNo(params) {
      if (this.form.tableData && this.form.tableData.length > 0) {
        let { data } = await customerBrandAPI.getBrandSoftwareNo({
          fk_customer_id: params.fk_customer_id,
        });

        for (let i = 0; i < this.form.tableData.length; i++) {
          if (data[this.form.tableData[i]["fk_brand_id"].value]) {
            this.form.tableData[i]["software_no"].value =
              data[this.form.tableData[i]["fk_brand_id"].value]["software_no"];
          }
        }
        this.form.tableData = [...this.form.tableData];
      }
    },
    async blurInput(e, item, row, prop) {
      // console.log("prop === " , prop);
      // console.log("row === " , row);
      switch (prop) {
        case "contract_price": //如果改单价
          row.contract_amount.value = this.checkContractAmount(row);
          row.maintenance_fee.value = this.checkMaintenanceFee(row);
          break;
        // case "contract_amount":
        //   row.contract_amount.value = this.checkContractAmount(row);
        //   row.maintenance_fee.value = this.checkMaintenanceFee(row);
        //   break;

        default:
          break;
      }
    },
    /**
     *
     * @param {*} option 字典集合
     * @param {*} value 下拉所选的值
     * @param {*} prop  列名
     * @param {*} item  当前列
     */
    async selectChange(option, value, prop, item) {
      let row = this.form.tableData[item.rowIndex];
      // console.log(option, " == option");
      // console.log(value, " == value");
      // console.log(prop, " == prop");
      // console.log(item, " == item");
      // console.log(row, " == row");
      let d = new Date();
      //选择不同的下拉框，有不同的修改
      if (prop === "fk_brand_id") {
        //选择产品
        let brandData = null;

        /**
         * 1、如果重来没有购买过，就查产品信息表
         * 2、如果有买过，则查询客户产品表的信息
         * 3、如果正好是子模块，开始时间，结束时间取值主产品
         */

        if (value) {
          brandData = await brandAPI.getInfo({
            brand_id: value,
            customer_id: this.ruleForm.fk_customer_id,
          });
        } else {
          return;
        }
        // 01=产品信息  02=没有父产品  03=有父产品
        let brandFlag = brandData.data["flag"];

        //赋值基础值

        row.invoice_tax_rate.value = brandData.data.rate
          ? brandData.data.invoice_tax_rate + ""
          : ""; //税率
        if (brandFlag === "01") {
          row.detail_sell_type.value = brandData.data.detail_sell_type; //默认
          row.contract_price.value = brandData.data.brand_sell_price; //合同单价
          row.contract_amount.value = (
            Number(row.contract_price.value) * Number(row.contract_count.value)
          ).toFixed(2); //合同金额
          row.year_maintain_cost.value = this.checkYearMaintainCost(
            brandData.data.detail_sell_type
          );
          row.measurement_unit.value = this.checkMeasurementUnit(
            brandData.data.detail_sell_type
          );
          row.maintain_start_time.value = dateFormat("yyyy-MM-dd", d);
          d.setFullYear(d.getFullYear() + 1);
          row.new_maintain_stop_time.value = dateFormat(
            "yyyy-MM-dd",
            new Date(d.getTime() ) // 去掉少一天 - 1000 * 60 * 60 * 24
          );
        } else if (["02"].includes(brandFlag)) {
          row.detail_sell_type.value = 1; //软件维护费
          row.contract_price.value = brandData.data.maintenance_fee; //合同单价
          row.contract_amount.value = brandData.data.maintenance_fee; //维护费
          row.software_no.value = brandData.data.software_no; //
          row.year_maintain_cost.value = 100; //brandData.data.year_maintain_cost//年维护费比例

          row.measurement_unit.value = 4;
          row.maintain_start_time.value = dateFormat(
            "yyyy-MM-dd",
            new Date(brandData.data.maintain_start_time)
          );
          row.new_maintain_stop_time.value = dateFormat(
            "yyyy-MM-dd",
            new Date(brandData.data.new_maintain_stop_time)
          );
        } else if (["03"].includes(brandFlag)) {
          row.detail_sell_type.value = brandData.data.detail_sell_type; //默认
          row.contract_price.value = brandData.data.maintenance_fee; //合同单价
          row.contract_amount.value = brandData.data.maintenance_fee; //维护费
          row.software_no.value = brandData.data.software_no; //
          row.year_maintain_cost.value = this.checkYearMaintainCost(
            brandData.data.detail_sell_type
          );
          row.measurement_unit.value = this.checkMeasurementUnit(
            brandData.data.detail_sell_type
          );
          row.maintain_start_time.value = dateFormat(
            "yyyy-MM-dd",
            new Date(brandData.data.maintain_start_time)
          );
          row.new_maintain_stop_time.value = dateFormat(
            "yyyy-MM-dd",
            new Date(brandData.data.new_maintain_stop_time)
          );
        }
        row.maintenance_fee.value = this.checkMaintenanceFee(row);

        // row.detail_sell_type.value = brandData.data.detail_sell_type

        switch (value) {
          //金万维
          case 71:
            //当天开始顺延一年
            // row.maintain_start_time.value = dateFormat("yyyy-MM-dd", d);
            // d.setFullYear(d.getFullYear() + 1);
            // row.new_maintain_stop_time.value = dateFormat(
            //   "yyyy-MM-dd",
            //   new Date(d.getTime() - 1000 * 60 * 60 * 24)
            // );
            break;

          case 47: //吉勤企业管理平台（专业版）[吉勤企业管理平台]
          case 48: //新阳企业管理系统V1.0
            // row.software_version.value = 1;
            break;
          //如果是子模块，就要查询
          default:
            break;
        }
        //说明是子模块，有上级产品，查上级产品的结束时间
        // let parentId = option.filter((item) => item.value === value)[0]
        // .parentId;
        // if (parentId !== 0) {
        //   let parentBrandObj = await brandApi.getParentBrandObj({
        //     customer_id: this.ruleForm.fk_customer_id,
        //     parentId: parentId
        //   })
        //   console.log(parentBrandObj, ' === parentBrandObj');
        //   if (parentBrandObj.data && parentBrandObj.data.length === 1) {

        // row.maintain_start_time.value = dateFormat("yyyy-MM-dd", new Date())
        // row.new_maintain_stop_time.value = dateFormat("yyyy-MM-dd", new Date(parentBrandObj.data[0].new_maintain_stop_time))
        //   }
        // }

        //初始化原有端口数 除了主产品，其他原有端口数都是0
        let { data } = await brandAPI.getPortBrand();
        if (!data.includes(value)) {
          row.original_port_count.value = 0;
        } else {
          row.original_port_count.value = this.ruleForm.port_number;
        }
      } else if (prop === "year_maintain_cost") {
        // console.log(row.year_maintain_cost.value)
        // console.log(row.maintain_start_time.value)
        // console.log(row.new_maintain_stop_time.value)
        //如果没有时间，就默认当天
        if (!row.maintain_start_time.value) {
          row.maintain_start_time.value = dateFormat("yyyy-MM-dd", d);
        }

        row.measurement_unit.value = this.check5(value);
        row.maintenance_fee.value = this.checkMaintenanceFee(row);
      } else if (prop === "detail_sell_type") {
        row.measurement_unit.value = this.checkMeasurementUnit(value);
        row.year_maintain_cost.value = this.checkYearMaintainCost(value);
        row.maintenance_fee.value = this.checkMaintenanceFee(row);
        row.invoice_tax_rate.value = this.checkInvoiceTaxRate(value);
        this.checkNewMaintainStopTime(row);

        d = new Date(row.maintain_start_time.value);
        switch (value) {
          case 5:
            // 按合同

            d.setFullYear(d.getFullYear() + (Number(row['contract_count'].value) || 3));
            row.new_maintain_stop_time.value = dateFormat(
              "yyyy-MM-dd",
              new Date(d.getTime()) // 去掉少一天- 1000 * 60 * 60 * 24
            );
            break;

          default:
            // d.setFullYear(d.getFullYear() + 1);
            // row.new_maintain_stop_time.value = dateFormat("yyyy-MM-dd", new Date(d.getTime() - 1000 * 60 * 60 * 24))
            break;
        }
      } else if (prop === "measurement_unit") {
        // row.measurement_unit.value = this.check4(value)
        row.year_maintain_cost.value = this.check4(value);
        row.maintenance_fee.value = this.checkMaintenanceFee(row);
      }
    },
    /**
     * 根据销售类型，判断计量单位
     */
    checkMeasurementUnit(value) {
      if (!value) return null;
      //
      if ([1].includes(value)) {
        return 2;
      } else if ([3, 5].includes(value)) {
        return 4;
      } else if ([12].includes(value)) {
        return 3;
      } else if ([4, 8].includes(value)) {
        return 1;
      } else if ([13].includes(value)) {
        return 5;
      } else {
        return 2;
      }
    },
    /**
     * 根据销售类型，年维护比例
     */
    checkYearMaintainCost(value) {
      if (!value) return null;
      //
      if ([1].includes(value)) {
        return "15";
      } else if ([9, 5, 3, 4, 13].includes(value)) {
        return "100";
      } else {
        return "15";
      }
    },
    /**
     * 计算年维护费
     */
    checkMaintenanceFee(row) {
      //如果是软件销售/软件租用
      // let money = 0
      // if([1,5].includes(row.detail_sell_type.value)){
      //   if (
      //     Number(row.contract_amount.value) &&
      //     Number(row.contract_count.value)
      //   ) {
      //     money =  (Number(row.contract_amount.value) / Number(row.contract_count.value)  * Number(row.year_maintain_cost.value) / 100)
      //   }

      // }else{
      //   if (
      //     Number(row.contract_amount.value) &&
      //     Number(row.year_maintain_cost.value)
      //   ) {
      //     money =  (
      //       (Number(row.contract_amount.value) *
      //         Number(row.year_maintain_cost.value)) /
      //       100
      //     )
      //   }
      // }
      // if (Number(money) > 0 && Number(money)  < 500) {
      //   return Number(500).toFixed(2);
      // }
      // return Number(money).toFixed(2);

      return checkMaintenanceFee(row);
    },
    /**
     * 计算合同金额
     */
    checkContractAmount(row) {
      if (
        Number(row.contract_price.value) &&
        Number(row.contract_count.value)
      ) {
        return (
          Number(row.contract_price.value) * Number(row.contract_count.value)
        ).toFixed(2);
      }
    },
    /**
     * 通过计量单位 计算年维护比例
     */
    check4(value) {
      if (!value) return null;
      //
      if ([4].includes(value)) {
        return "100";
      } else {
        return "15";
      }
    },
    /**
     * 通过年维护费比例 计算计量单位
     */
    check5(value) {
      if (!value) return null;
      //
      if (["100"].includes(value)) {
        return 4;
      } else {
        return 2;
      }
    },
    /**
     * 通过销售类型 返回发票税率
     */
    checkInvoiceTaxRate(value) {
      if (!value) return null;
      //
      if ([1, 4, 5].includes(value)) {
        return "13";
      } else if ([2, 3, 11, 12].includes(value)) {
        return "6";
      } else {
        return "13";
      }
    },
    /**
     * 通过销售类型 返回新维护结束日期
     */
    checkNewMaintainStopTime(row) {
      if (row.detail_sell_type.value === 4 || row.detail_sell_type.value === 2) {
        row.new_maintain_stop_time.value = row.maintain_start_time.value
      }
    },
    //获取用户的字段设置
    getUserFieldList() {
      //默认没数据的时候就用原始数据
      this.currentFieldList = this.tableCol;
    },
  },
  computed: {
    showTableHead() {
      //如果不用过滤直接返回原数组
      if (!this.isSaveField) return this.tableCol;
      //当前排序的col
      let fieldArr = this.currentFieldList;
      //数据的字段数组  这边过滤掉没有的
      const fieldFilter = fieldArr.map((item) => item.prop);
      //预设的原数组
      let filterList = this.tableCol.filter((item) => {
        return fieldFilter.includes(item.prop);
      });
      //新数组排序替换掉旧数组排序 没有就默认放后面 改变的永远是原数组
      filterList.forEach((item, index) => {
        fieldArr.forEach((sItem, sIndex) => {
          if (item.prop === sItem.prop) {
            // item.width = sItem.width;
            // item.fixed = sItem.fixed;
            item.sort = sIndex;
          }
        });
      });

      return filterList.sort((a, b) => +a.sort - +b.sort);
    },
  },
  watch: {
    tableCol: {
      immediate: true,
      handler: function() {
        if (this.isSaveField) this.getUserFieldList();
      },
    },
    "form.tableData": {
      handler(newVal, oldVal) {
        if (this.parentComponentName === "openTicket") {
          let totalMoney = 0;
          if (this.form.tableData && this.form.tableData.length > 0) {
            this.form.tableData.map((item) => {
              totalMoney +=
              Number(item["leved_total"]["value"])
                // Number(item["open_ticket_number"]["value"]) *
                // Number(item["open_ticket_unit"]["value"]);
            });
          }
          this.$emit("getTotalMoney", totalMoney.toFixed(2));
        } else if (this.parentComponentName === "contract") {

          let totalMoney = 0;
          if (this.form.tableData && this.form.tableData.length > 0) {
            this.form.tableData.map((item) => {
 
              totalMoney += Number(item["contract_amount"]["value"]) || 0;
            });
          }
          this.$emit("getTotalMoney", totalMoney.toFixed(2));
        }
      },
      deep: true,
    },
  },
};
