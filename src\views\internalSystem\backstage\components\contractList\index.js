import API from "@/api/internalSystem/salesManage/contract";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import { getOptions } from "@/common/internalSystem/common.js";
import { mapGetters, mapMutations } from "vuex";
export default {
  name: "contractList",
  components: {
    TableView,
    Pagination,
    MyDate,
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        customer_name: "",
        customer_contract_id: null,
        sell_type: "",
        fk_sell_employee_id: "",
        startTime: "",
        endTime: "",
      },
      tableList: [],
      sellTypeList: [],
      employeeList: [],
      param: {},
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "合同列表",
    },
    isJudge: {
      type: Boolean,
      default: false,
    },
    not_complete_open_ticket: {
      type: Boolean,
      default: false,
    },
    has_receivables: {
      type: Boolean,
      default: false,
    },
    type: {
      type: Number,
      default: 1,
    },
    customer_no: {
      type: String,
      default: null,
    },
    contract_ids: {
      type: Array,
      default: () => [],
    },
    not_complete_receivables: {
      type: Boolean,
      default: false,
    },
    isReceive: {
      type: Boolean,
      default: false,
    },
    isOwn: {
      type: Boolean,
      default: false,
    },
    not_has_open_ticket: {
      type: Boolean,
      default: false,
    },
    not_has_open_ticket_no_audit: {
      type: Boolean,
      default: false,
    },
    invoice_tax_rate: {
      type: Boolean,
      default: false,
    },
    //是否收全款
    isAllReceive: {
      type: Boolean,
      default: false,
    },

    //查询合同的出库金额还没全部出库
    is_not_outbound_sing: {
      type: Boolean,
      default: false,
    },
    //可授权列表-未收全款，至少有一次出库
    is_has_authorization_sing: {
      type: Boolean,
      default: false,
    },
    has_contract_anexo: {
      type: Boolean,
      default: false,
    },
    data_state: {
      type: Number,
      default: null,
    },
    // 是否查自己的合同，默认是
    oneselfFlag: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    Show() {
      if (this.oneselfFlag == true && !['总经理','财务经理','财务专员'].includes(this.cookiesUserInfo.role_name)) {
        this.formSearch.fk_sell_employee_id = this.cookiesUserInfo.userId;
      }
      this.tableList = [
        {
          name: "编号",
          value: "contract_no",
          width: 112,
        },
        {
          name: "客户名称",
          value: "customer_name",
          width: 130,
        },
        {
          name: "销货单位",
          value: "companyName",
          width: 130,
        },
        {
          name: "销售类型",
          value: "detail_sell_type_name",
          width: 82,
        },
        {
          name: "合同金额",
          value: "contract_amount",
          width: 70,
        },
        {
          name: "已收款金额",
          value: "receivables_amount",
          width: 80,
        },
        {
          name: "未收款金额",
          value: "not_receivables_amount",
          width: 82,
        },
        // {
        //   name: "已出库金额",
        //   value: "outbound_amount",
        //   width: 82,
        // },
        {
          name: "已开票金额",
          value: "open_ticket_amount",
          width: 82,
        },
        {
          name: "未开票金额",
          value: "not_open_ticket_amount",
          width: 82,
        },
        {
          name: "附件情况",
          value: "contract_anexo_status",
          width: 82,
        },
        // {
        //   name: "销货单位",
        //   value: "sales_unit_id_format",
        //   width: 180,
        // },
        {
          name: "付款方式",
          value: "pay_type_name",
          width: 100,
        },
        {
          name: "培训方式",
          value: "train_type_name",
          width: 70,
        },
        {
          name: "操作员工",
          value: "add_user_name",
          width: 70,
          isHide: this.isReceive,
        },
        {
          name: "操作部门",
          value: "fk_operator_department_name",
          width: 70,
          isHide: this.isReceive,
        },
        {
          name: "手机",
          value: "phone",
          width: 70,
          isHide: this.isReceive,
        },
        {
          name: "联系人",
          value: "link_man",
          width: 60,
          isHide: this.isReceive,
        },
        {
          name: "介绍人",
          value: "introducer_format",
          width: 70,
          isHide: this.isReceive,
        },
        {
          name: "介绍合同",
          value: "introducer_contract_format",
          width: 80,
          isHide: this.isReceive,
        },
        {
          name: "销售员",
          value: "fk_sell_employee_name",
          width: 60,
        },
        {
          name: "销售部门",
          value: "fk_sell_department_name",
          width: 70,
        },
        {
          name: "单据日期",
          value: "update_time",
          width: 96,
        },
        {
          name: "客户地址",
          value: "address",
          width: 80,
          isHide: this.isReceive,
        },
        // {
        //   name: "联系人QQ号",
        //   value: "link_qq",
        //   width: 90,
        //   isHide: this.isReceive,
        // },
        {
          name: "软件序列号",
          value: "software_no",
          width: 90,
          isHide: this.isReceive,
        },
      ];
      this.dialogVisible = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          this.sellTypeList = getOptions("t_customer_contract", "sell_type");
          this.getList();
          this.$store.dispatch("getEmployee").then((res) => {
            this.employeeList = res;
          });
        });
      // }, 300);
    },
    getList(f = false) {
      this.loading = true;
      this.param = Object.assign(
        this.formSearch,
        this.$refs.con_pagination.obtain()
      );
      if (f) this.param.pageNum = 1;
      this.param.not_complete_open_ticket = this.not_complete_open_ticket;
      this.param.has_receivables = this.has_receivables;
      this.param.not_complete_receivables = this.not_complete_receivables;
      this.param.not_has_open_ticket = this.not_has_open_ticket;
      this.param.not_has_open_ticket_no_audit = this.not_has_open_ticket_no_audit;
      this.param.is_not_outbound_sing = this.is_not_outbound_sing;
      this.param.is_has_authorization_sing = this.is_has_authorization_sing;
      this.param.has_contract_anexo = this.has_contract_anexo;
      this.param.data_state = this.data_state;
      if (this.invoice_tax_rate) this.param.invoice_tax_rate = 1;
      // let isJurisdiction = true;
      // if (this.isJudge) {
      // isJurisdiction = this.buttonPermissions.length ? (this.existence(this.buttonPermissions, 'ALL_CONTRACT_NEW')) : false
      // }
      if (this.isOwn) {
        this.param.fk_sell_employee_id = this.userInfo.employeeId;
      }
      delete this.param.not_has_contract_anexo 
      delete this.param.has_contract_anexo 
      if(this.formSearch.contract_anexo){
        if(this.formSearch.contract_anexo == 2){
          this.param.not_has_contract_anexo = true
       }
        if(this.formSearch.contract_anexo == 1){
          this.param.has_contract_anexo = true
        }
      }
      delete this.param.invoice_tax_rate_eq_zero 
      delete this.param.invoice_tax_rate_gt_zero 
      if(this.formSearch.invoice_tax){
        if(this.formSearch.invoice_tax == 2){
          this.param.invoice_tax_rate_eq_zero = true
       }
        if(this.formSearch.invoice_tax == 1){
          this.param.invoice_tax_rate_gt_zero = true
        }
      }

      if (this.isAllReceive) {
        this.param.isAllReceive = this.isAllReceive;
      }
      // param.isJurisdiction = isJurisdiction ? 1 : 0;

      API.query(this.param)
        .then((res) => {
          this.tableData = res.data;
          this.$refs.con_pagination.setTotal(res.totalCount);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //提交
    submitForm() {
      /**
        // console.log(this.selectRecords);
      if (this.selectRecords.length === 0) {

        return this.error("请至少选择一条记录")
      }
      if (this.selectRecords.length > 1) {
        return this.error("只能选择一条记录")
      }

      let f1 = true;

      this.selectRecords.forEach(item => {
        if (this.contract_ids.some(eItem => {
            return eItem == item.customer_contract_id
          })) {
          f1 = false;
          return;
        }
      });

      let cur_customer_no;
      if (!this.customer_no){
        cur_customer_no = this.selectRecords[0].customer_no;
      }else{
        cur_customer_no = this.customer_no;
      }


      if (this.selectRecords[0].customer_no != cur_customer_no) {
        return this.error("请选择同一客户的记录");
      }
      if (!f1)
        return this.error("不可重复调入合同");
      this.$emit("getInfo", this.selectRecords[0]);

       */

      if (this.type === 1) {
        if (this.selectRecords.length != 1) return this.error("请选择一条记录");
        this.$emit("getInfo", this.selectRecords[0]);
      } else if (this.type === 2) {
        if (this.selectRecords.length === 0)
          return this.error("请至少选择一条记录");
        let cur_customer_no;
        if (!this.customer_no) {
          cur_customer_no = this.selectRecords[0].customer_no;
        } else {
          cur_customer_no = this.customer_no;
        }
        let f = true;
        let f1 = true;

        this.selectRecords.forEach((item) => {
          if (item.customer_no != cur_customer_no) {
            f = false;
            return;
          }
          if (
            this.contract_ids.some((eItem) => {
              return eItem == item.customer_contract_id;
            })
          ) {
            f1 = false;
            return;
          }
        });
        if (!f) return this.error("请选择同一客户的记录");
        if (!f1) return this.error("不可重复调入合同");

        let sales_unit_ids = this.selectRecords.map(item => item.sales_unit_id)
        let unique_sales_unit_ids = [...new Set(sales_unit_ids)]
        if (unique_sales_unit_ids.length > 1) return this.error("不同销货单位禁止调入");
        this.$emit("getInfo", this.selectRecords);
      }else if(this.type === 3){
        this.$emit("getInfo", this.selectRecords);
      }
      this.dialogCancel();
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.selectRecords = [];
    },
    // async getTotalOpenMoney(fk_customer_id) {
    //   console.log("getTotalMoney === " + fk_customer_id);
    //   this.param.not_complete_open_ticket = this.not_complete_open_ticket;
    //   this.param.has_receivables = this.has_receivables;
    //   this.param.not_complete_receivables = this.not_complete_receivables;
    //   this.param.not_has_open_ticket = this.not_has_open_ticket;
    //   this.param.not_has_open_ticket_no_audit = this.not_has_open_ticket_no_audit;
    //   this.param.is_not_outbound_sing = this.is_not_outbound_sing;
    //   this.param.is_has_authorization_sing = this.is_has_authorization_sing;
    //   this.param.has_contract_anexo = this.has_contract_anexo;
    //   this.param.data_state = this.data_state;
    //   this.param.invoice_tax_rate = 1;

    //   if (this.isOwn) {
    //     this.param.fk_sell_employee_id = this.userInfo.employeeId;
    //   }

    //   if (this.isAllReceive) {
    //     this.param.isAllReceive = this.isAllReceive;
    //   }
    //   const { data } = await API.query({
    //     ...this.param,
    //     fk_customer_id: fk_customer_id,
    //     pageNum: 1,
    //     pageSize: 99,
    //   });
    //   let totalOpenMoney = 0;
    //   if (data && data.length > 0) {
    //     data.map((item) => {
    //       totalOpenMoney += Number(item["contract_amount"]);
    //     });
    //   }
    //   totalOpenMoney = totalOpenMoney.toFixed(2);

    // },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
    rowDblclick(row, column, event) {
      this.selectRecords = [];
      this.selectRecords.push(row);
      this.submitForm();
    },
  },
  computed: {
    ...mapGetters(["buttonPermissions", "userInfo", "sell_type","cookiesUserInfo"]),
  },
};
