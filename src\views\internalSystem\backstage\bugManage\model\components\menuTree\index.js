import API from "@/api/internalSystem/bugManage/module/index.js";
import AddMenu from "./../addMenu/index.vue";
export default {
  data() {
    return {
      filterText: ``,
      menuTreeList: [],
      checkedKey: [],
      defaultProps: {
        children: "children",
        label: "module_name",
        disabled: true,
      },
      showCheckbox: false,
      module_id: "", //模块Id
      parentId: "", //模块父Id
      parentName: "", //模块父名称
      brand_id: "",
    };
  },

  mounted() {},
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    GetCheckedKeys() {
      //获取模块树选中的节点
      return this.$refs.tree.getCheckedKeys();
    },
    checkFid(data = [], module_id = 0) {
      for (let i = 0; i < data.length; i++) {
        let list = data[i];
        if (list.module_id === module_id) return true;
        if (list.children && list.children.length)
          return this.checkFid(list.children, module_id);
        return false;
      }
    },
    //设置选中模块树
    SetCheckedKeys(data = []) {
      this.showCheckbox = true;
      this.$refs.tree.setCheckedKeys(data);
    },
    getMenuTree(brand_id) {
      // 获取模块列表
      if (!brand_id) return;
      this.brand_id = brand_id;
      API.query({
        brand_id,
      })
        .then((data) => {
          this.menuTreeList = this.forInt(data.data);
        })
        .catch(() => {});
    },
    getList(){
      API.query({
        brand_id:this.brand_id,
      })
        .then((data) => {
          this.menuTreeList = this.forInt(data.data);
        })
        .catch(() => {});
    },
    forInt(data = []) {
      // 遍历把id转为Int类型，后面要求后端直接返回int类型
      let _this = this;
      data.forEach((list) => {
        list.module_id = _this.stringToInt(list.module_id);
        if (list.children && list.children.length) _this.forInt(list.children);
      });
      return data;
    },
    stringToInt(id = "0") {
      return parseInt(id);
    },
    filterNode(value, data) {
      // 筛选查询
      if (!value) return true;
      return data.module_name.indexOf(value) !== -1;
    },
    //打开添加模块会话框
    addMenu(params=null) {

      if (!this.brand_id) return this.error("请先选择左侧产品");
      if (!params) {
        params = {
          module_id: 0,
          module_name: "无上级模块",
          level: 0,
        };
      }
   
      let menuObject = {
        parent_id: params.module_id,
        parent_name: params.module_name,
        module_id: "",
        level: params.level + 1,
      };
      this.$refs.AddMenu.Show(menuObject);
    },
    //打开编辑模块会话框
    editMenu(params) {
      let menuObject = {
        parentId: "",
        parentName: "",
        module_id: params.module_id,
        level: "",
      };
      this.$refs.AddMenu.Show(menuObject);
    },

    //删除模块
    delMenu(params) {
      this.$confirm("此操作将删除该模块, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let delIds = [];
          delIds.push(params.module_id);
          API.remove({
            module_id: delIds,
          })
            .then(() => {
              this.getList();
            })
            .catch(() => {})
            .finally(() => {});
        })
        .catch(() => {});
    },
    clickDeal(currentObj, treeStatus) {
      // 用于：父子节点严格互不关联时，父节点勾选变化时通知子节点同步变化，实现单向关联。
      if (treeStatus) {
        // 子节点只要被选中父节点就被选中
        this.selectedParent(currentObj);
      } else {
        // 未选中 处理子节点全部未选中
        if (currentObj.children.length !== 0) {
          this.sameChildrenNode(currentObj, false);
        }
      }
    },
    // 统一处理子节点为相同的勾选状态
    sameChildrenNode(currentObj, treeStatus) {
      this.$refs.tree.setChecked(currentObj.module_id, treeStatus);
      for (let i = 0; i < currentObj.children.length; i++) {
        this.sameChildrenNode(currentObj.children[i], treeStatus);
      }
    },
    // 统一处理父节点为选中
    selectedParent(currentObj) {
      let currentNode = this.$refs.tree.getNode(currentObj);
      if (currentNode.parent.key !== undefined) {
        this.$refs.tree.setChecked(currentNode.parent, true);
        this.selectedParent(currentNode.parent);
      }
    },
    //全选
    chooseAll(data, treeStatus) {
      this.sameChildrenNode(data, treeStatus);
    },
  },
  components: {
    AddMenu,
  },
};
