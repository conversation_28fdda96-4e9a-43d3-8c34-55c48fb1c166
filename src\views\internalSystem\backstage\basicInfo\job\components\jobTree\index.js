import API from "@/api/internalSystem/basicInfo/role/roleApi.js";
import AddJob from "../addJob/index.vue";
export default {
  data() {
    return {
      filterText: ``,
      jobTreeList: [],
      checkedKey: [],
      defaultProps: {
        children: "children",
        label: "roleName"
      },
      jobId:""
    };
  },

  mounted() {
    this.getJobTree();
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    filterNode(value, data) {
      // 筛选查询
      if (!value) return true;
      return data.roleName.indexOf(value) !== -1;
    },
    //打开添加岗位会话框
    addJob() {
      let jobObject = {
        roleId: ""
      };
      this.$refs.addJob.Show(jobObject);
    },
    //打开编辑岗位会话框
    editJob(params) {
      let jobObject = {
        roleId: params.roleId
      };
      this.$refs.addJob.Show(jobObject);
    },
    //删除岗位
    delJob(params) {
      this.$confirm("此操作将删除该岗位, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let delIds = [];
          delIds.push(params.roleId);
          API.remove({
            roleId: delIds
          })
            .then(() => {
              this.getJobTree();
            })
            .catch(() => {})
            .finally(() => {});
        })
        .catch(() => {
        });
    },
    //获取岗位树
    getJobTree() {
      API.query({})
        .then(data => {
          this.jobTreeList = data.data;
        })
        .catch(() => {})
        .finally(() => {});
    },
    setJob(data) {
     this.jobId=data.roleId
      let params = {
        roleId: data.roleId,
        roleName: data.roleName
      };
      this.$emit("setJob", params);
    }
  },
  components: {
    AddJob
  }
};
