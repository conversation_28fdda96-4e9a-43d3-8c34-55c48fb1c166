
import Sortable from 'sortablejs';
import { Message } from "element-ui";
// import { cloneDeep } from "lodash";
/**
 * 
 * @param {*} xTable 表格实例
 * @param {*} path 路径
 * @param {*} isSaveField 是否需要保存
 * @returns 
 */
function sortTable(xTable,path = "",isSaveField=true){
  if(!xTable) return
    Sortable.create(xTable.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'), {
        handle: '.vxe-header--column:not(.col--fixed)',
        onEnd: ({ item, newIndex, oldIndex }) => {
          let { fullColumn, tableColumn } = xTable.getTableColumn()
          let targetThElem = item
          let wrapperElem = targetThElem.parentNode
          let newColumn = fullColumn[newIndex]
          if (newColumn.fixed) {
            // 错误的移动
            if (newIndex > oldIndex) {
              wrapperElem.insertBefore(targetThElem, wrapperElem.children[oldIndex])
            } else {
              wrapperElem.insertBefore(wrapperElem.children[oldIndex], targetThElem)
            }
            xTable.loadColumn(fullColumn) 
            return Message.error("固定列不允许拖动！")
          }
          // 转换真实索引
          let oldColumnIndex = xTable.getColumnIndex(tableColumn[oldIndex])
          let newColumnIndex = xTable.getColumnIndex(tableColumn[newIndex])
          // 移动到目标列
          let currRow = fullColumn.splice(oldColumnIndex, 1)[0]
          fullColumn.splice(newColumnIndex, 0, currRow)

          /**保存的逻辑，接口没好先注释 */
          // if (oldColumnIndex != newColumnIndex) {
          //   let menuFields = fullColumn
          //     .filter((item) => !item.type)
          //     .map((item, index) => {
          //       return {
          //         width: item.width,
          //         fixed: item.fixed,
          //         field: item.property,
          //         sort: index + 1,
          //       };
          //     });
          //   let newMenuFields = [];
          //   menuFields.forEach((item) => {
          //     let cItem = cloneDeep(item);
          //     if(cItem.field)
          //     newMenuFields.push(cloneDeep(cItem));
          //     console.log(cItem.field)
          //     if (cItem.field&&cItem.field.includes("Name")) {
          //       newMenuFields.push(cItem);
          //     }
          //   });
          //   if (path&&isFilterField)
          //     API
          //       .saveUserField({ menuFields: newMenuFields, path })
          //       .then(() => {
                  
          //       });
          // }

          xTable.loadColumn(fullColumn)
        }
      })
}


export  {
    sortTable
}