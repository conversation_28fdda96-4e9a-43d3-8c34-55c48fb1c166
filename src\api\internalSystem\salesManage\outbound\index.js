import Axios from "@/api/index";
import environment from "@/api/environment";

export default {
  // 查询
  query: params => {
    return Axios.post(`${environment.internalSystemAPI}outbound/query`, params)
  },
    // 查询全部金额
    queryAll: params => {
      return Axios.post(`${environment.internalSystemAPI}outbound/queryAll`, params)
    },
  // 查询明细
  detailList: params => {
    return Axios.post(`${environment.internalSystemAPI}outbound/detailList`, params)
  },
  // 新增
  add: params => {
    return Axios.post(`${environment.internalSystemAPI}outbound/add`, params)
  },
  // 删除
  remove: params => {
    return Axios.post(`${environment.internalSystemAPI}outbound/remove`, params)
  },
  // 编辑
  update: params => {
    return Axios.post(`${environment.internalSystemAPI}outbound/update`, params)
  },
  // 获取单条信息
  getInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}outbound/getInfo`, params)
  },
  // 同步端口
  updateEndState: params => {
    return Axios.post(`${environment.internalSystemAPI}outbound/updateEndState`, params)
  },
  // 根据合同id，查找未出库的产品明细
  getDetails: params => {
    return Axios.post(`${environment.internalSystemAPI}outbound/getDetails`, params)
  }
};