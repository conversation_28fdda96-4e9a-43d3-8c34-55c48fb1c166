import API from '@/api/internalSystem/financialManage/bankAccout'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddBankAccout from "./components/addBankAccout/index.vue";
import DetailBankAccout from "./components/detailBankAccout/index.vue";
import BankCashFlow from "./components/bankCashFlow/index.vue"
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  mapGetters
} from "vuex";
export default {
  name: "bankAccout",
  data() {
    return {
      title: "银行账户管理",
      loading: false,
      tableData: [],
      tableList: [{
          name: "银行编码",
          value: "bank_code",
          width: 80
        },
        {
          name: "银行名称",
          value: "bank_name"
        },
        {
          name: "户名",
          value: "accout_name",
          width: 70
        },
        {
          name: "账号",
          value: "bank_accout"
        },
        {
          name: "企业单位",
          value: "sales_unit_id_format"
        },
        {
          name: "余额",
          value: "remain",
          width: 120
        },
        {
          name: "备注",
          value: "remark"
        }
      ]
    };
  },

  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      let param = Object.assign(this.$refs.pagination.obtain());
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    add() {
      this.$refs.addBankAccout.Show();
    },
    //打开修改银行账户会话框
    modify(item) {
      let params = {
        financial_bank_accout_id: item.financial_bank_accout_id
      };
      API.getInfo(params)
        .then(data => {
          this.$refs.addBankAccout.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      let params = {
        financial_bank_accout_id: item.financial_bank_accout_id
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
    detail(item) {
      this.$refs.detailBankAccout.Show(item.financial_bank_accout_id);
    },
    //银行资金流转
    flow(){
      this.$refs.bankCashFlow.Show();
    }
  },

  components: {
    AddBankAccout,
    DetailBankAccout,
    BankCashFlow,
    Pagination,
    TableView,
    MyDate
  },
  computed: {
    ...mapGetters(["buttonPermissions"])
  }
};