<template>
  <div class="body-p10 congested-slid">
    <!-- <div>
      <div class="container">
        <div class="content">
          <div class="topHead mt10">
            <div class="midLeftItem boxItem blue noneLine" @click="auditList">
              <div class="midLeftItemCenter">
                <icon class="icon" name="homeSK" />
                <h4 class="midLeftItemCenterText">未处理任务</h4>
              </div>
            </div>
            <div class="midLeftItem boxItem blue noneLine">
              <div class="midLeftItemCenter center">
                <icon class="icon" name="inflowChronology" />
                <h4 class="midLeftItemCenterText">系统公告</h4>
              </div>
            </div>
            <div class="midLeftItem boxItem blue noneLine">
              <div class="midLeftItemCenter center">
                <icon class="icon" name="outflowChronology" />
                <h4 class="midLeftItemCenterText">系统预警</h4>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> -->
    <!-- <div class="header-info-list">
        <div class="info-item order cursor-pointer" @click="auditList">
          <div class="item-title">
            <p>未处理任务</p>
            <img
              class="img1"
              :src="Img.statisticsOrder"
              height="40"
              width="50"
            />
          </div>
          <p class="number">{{ auditNum || 0 }}<span class="unit">条</span></p>
        </div>
        <div class="info-item user">
          <div class="item-title">
            <p>系统公告</p>
            <img
              class="img1"
              :src="Img.statisticsUser"
              height="46"
              width="46"
            />
          </div>
          <p class="number">{{ userCount || 0 }}<span class="unit">条</span></p>
        </div>
        <div class="info-item up">
          <div class="item-title">
            <p>系统预警</p>
            <img class="img1" :src="Img.statisticsUp" height="46" width="46" />
          </div>
          <p class="number">
            {{ addUserCount || 0 }}<span class="unit">条</span>
          </p>
        </div>
      </div> -->
  <!-- @click="getDate" -->
    <!-- <div class="row-list">
      <div class="col-item col-8">
        <div class="msg-content">
        
          <div class="msg-title msg-title-backgroud-1" >经营情况</div>
          <ul class="msg-list">
            <el-row :gutter="10" style="margin: 12px 12px 12px 12px">
              <el-col :span="12"
                ><div class="grid-content bg-purple">
                  <div class="msg-item">销售合同金额</div>
                  <div>
                    <el-row>
                      <el-col :span="12"
                        ><div class="data1-top">
                          {{ businessData.contractAmountMon }}
                        </div>
                        <div class="data1-up">月合计</div></el-col
                      >
                      <el-col :span="12"
                        ><div class="data1-top">
                          {{ businessData.contractAmountYear }}
                        </div>
                        <div class="data1-up">年合计</div></el-col
                      >
                    </el-row>
                  </div>
                </div>
              </el-col>
              <el-col :span="12"
                ><div class="grid-content bg-purple">
                  <div class="msg-item">销售收款金额</div>
                  <div>
                    <el-row>
                      <el-col :span="12"
                        ><div class="data1-top">
                          {{ businessData.receivableAmountMon }}
                        </div>
                        <div class="data1-up">月合计</div></el-col
                      >
                      <el-col :span="12"
                        ><div class="data1-top">
                          {{ businessData.receivableAmountYear }}
                        </div>
                        <div class="data1-up">年合计</div></el-col
                      >
                    </el-row>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="10" style="margin: 12px 12px 12px 12px">
              <el-col :span="12"
                ><div class="grid-content bg-purple">
                  <div class="msg-item">新增客户成交</div>
                  <div>
                    <el-row>
                      <el-col :span="12"
                        ><div class="data1-top">
                          {{ businessData.dealCustomerMon }}
                        </div>
                        <div class="data1-up">月合计</div></el-col
                      >
                      <el-col :span="12"
                        ><div class="data1-top">
                          {{ businessData.dealCustomerYear }}
                        </div>
                        <div class="data1-up">年合计</div></el-col
                      >
                    </el-row>
                  </div>
                </div>
              </el-col>
              <el-col :span="12"
                ><div class="grid-content bg-purple">
                  <div class="msg-item">销售开票金额</div>
                  <div>
                    <el-row>
                      <el-col :span="12"
                        ><div class="data1-top">
                          {{ businessData.openTicketAmountMon }}
                        </div>
                        <div class="data1-up">月合计</div></el-col
                      >
                      <el-col :span="12"
                        ><div class="data1-top">
                          {{ businessData.openTicketAmountYear }}
                        </div>
                        <div class="data1-up">年合计</div></el-col
                      >
                    </el-row>
                  </div>
                </div>
              </el-col>
            </el-row>
          </ul>
        </div>
      </div>
      <div class="col-item col-8">
        <div class="msg-content">
          <div class="msg-title msg-title-backgroud-2">我的客户情况</div>
          <ul class="msg-list">
            <el-row :gutter="10" style="margin: 12px 12px 12px 12px">
              <el-col :span="8"
                ><div class="grid-content bg-purple">
                  <div class="msg-item2">正常维护客户</div>
                  <div>
                    <div class="data2-top">{{ customerData.zcwhkhNum }}</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8"
                ><div class="grid-content bg-purple">
                  <div class="msg-item2">正在实施客户</div>
                  <div>
                    <div class="data2-top">{{ customerData.zzsskhNum }}</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8"
                ><div class="grid-content bg-purple">
                  <div class="msg-item2">潜在客户</div>
                  <div>
                    <div class="data2-top">{{ customerData.qzkhNum }}</div>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="10" style="margin: 12px 12px 12px 12px">
              <el-col :span="8"
                ><div class="grid-content bg-purple">
                  <div class="msg-item2">按次维护客户</div>
                  <div>
                    <div class="data2-top">{{ customerData.acwhkhNum }}</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8"
                ><div class="grid-content bg-purple">
                  <div class="msg-item2">完成实施客户</div>
                  <div>
                    <div class="data2-top">{{ customerData.wcsskhNum }}</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8"
                ><div class="grid-content bg-purple">
                  <div class="msg-item2">潜在过期客户</div>
                  <div>
                    <div class="data2-top">{{ customerData.zqgqkhNum }}</div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </ul>
        </div>
      </div>
    </div>
    <div class="row-list">
      <div class="col-item col-8">
        <div class="msg-content">
          <div class="msg-title msg-title-backgroud-3">即将到期客户</div>
          <ul class="msg-list2">
            <el-table
              :data="noOverdueList"
              stripe
              border
              align="center"
              style="width: 98%; margin: 0px 8px; font-size: 13px; color: black"
              :header-cell-style="{ 'font-weight': 'bold', color: '#333333' }"
              show-overflow-tooltip
            >
              <el-table-column prop="customerName" label="客户名称" width="200">
                <template slot-scope="scope">
                  <el-popover
                    placement="bottom"
                    title=""
                    width="200"
                    :open-delay="300"
                    trigger="hover"
                    :content="scope.row.customerName"
                  >
                    <div slot="reference" class="ellipsis">
                      {{ scope.row.customerName }}
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column prop="brandName" label="产品服务" width="200">
                <template slot-scope="scope">
                  <el-popover
                    placement="bottom"
                    title=""
                    width="200"
                    :open-delay="300"
                    trigger="hover"
                    :content="scope.row.brandName"
                  >
                    <div slot="reference" class="ellipsis">
                      {{ scope.row.brandName }}
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column prop="sellType" label="服务类型" width="100">
                <template slot-scope="scope">
                  <div slot="reference" class="ellipsis">
                    {{ scope.row.sellType | takeSellType(scope.row.sellType) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="newMaintainStopTime" label="到期时间">
              </el-table-column>
              <el-table-column prop="date" label="操作">
                <template slot-scope="scope">
                  <el-button
                    @click="handleClick1(scope.row)"
                    type="text"
                    size="small"
                    >续费</el-button
                  >
                  <el-button
                    :disabled="!scope.row.email"
                    @click="handleClick2(scope.row, '01')"
                    type="text"
                    size="small"
                    >催缴</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </ul>
        </div>
      </div>
      <div class="col-item col-8">
        <div class="msg-content">
          <div class="msg-title msg-title-backgroud-4">已经超期客户</div>
          <ul class="msg-list2">
            <el-table
              :data="expired"
              stripe
              border
              align="center"
              style="width: 98%; margin: 0px 8px; font-size: 13px; color: black"
              :header-cell-style="{ 'font-weight': 'bold', color: '#333333' }"
              show-overflow-tooltip
            >
              <el-table-column prop="customerName" label="客户名称" width="200">
                <template slot-scope="scope">
                  <el-popover
                    placement="bottom"
                    title=""
                    width="200"
                    :open-delay="300"
                    trigger="hover"
                    :content="scope.row.customerName"
                  >
                    <div slot="reference" class="ellipsis">
                      {{ scope.row.customerName }}
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column prop="brandName" label="产品服务" width="200">
                <template slot-scope="scope">
                  <el-popover
                    placement="bottom"
                    title=""
                    width="200"
                    :open-delay="300"
                    trigger="hover"
                    :content="scope.row.brandName"
                  >
                    <div slot="reference" class="ellipsis">
                      {{ scope.row.brandName }}
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column prop="sellType" label="服务类型" width="100">
                <template slot-scope="scope">
                  <div slot="reference" class="ellipsis">
                    {{ scope.row.sellType | takeSellType(scope.row.sellType)  }}
       
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="newMaintainStopTime" label="到期时间">
              </el-table-column>
              <el-table-column prop="date" label="操作">
                <template slot-scope="scope">
                  <el-button
                    @click="handleClick1(scope.row)"
                    type="text"
                    size="small"
                    >续费</el-button
                  >
                  <el-button
                    :disabled="!scope.row.email"
                    @click="handleClick2(scope.row, '02')"
                    type="text"
                    size="small"
                    >催缴</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </ul>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import Img from "@/assets/images/mall_admin/index";
import API from "@/api/internalSystem/common/index.js";
// import Echarts from "vue-echarts";
// 引入折线图等组件
// import "echarts/lib/chart/line";
// 引入提示框和title组件
// import "echarts/lib/component/title";
// import "echarts/lib/component/tooltip";
// import "echarts/lib/component/legend";
export default {
  name: "StatisticsPage",
  components: {
    // eslint-disable-next-line vue/no-unused-components
    // "vue-charts": Echarts,
  },
  data() {
    return {
      Img,
      msgList: [],
      userCount: "",
      proSearchList: [],
      proBrowseList: [],
      addUserCount: "",
      noOverdueList: [], //即将到期客户
      expired: [], //将到期客户
      businessData: {}, //经营情况
      customerData: {}, //我的客户情况
      options: {
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          type: "category",
          data: [],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            label: {
              normal: {
                show: true,
                position: "top",
              },
            },
            data: [],
            type: "line",
          },
        ],
      },
      auditNum: "",
    };
  },
  filters: {
    takeSellType(sellType) {
      if ([1, 4, 6, 2, 7, 8, 3].includes(sellType)) {
        return "维护";
      } else if ([5, 9].includes(sellType)) {
        return "租用";
      }
      return "其他";
    },
  },
  mounted() {
    // this.getAuditCenter();
    this.getDate();
  },
  methods: {
    //续费
    handleClick1(row) {
   
      this.$router.push({
        // path: "/backstage/salesManage/contractAdd",
       path: "/backstage/salesManage/contract", 
        query: { row: row, sellType: [5, 9].includes(row.sellType) ? 5 : 3 },
      });
    },
    // 催缴
    handleClick2(row, flag) {
      API.remindCustomer({ row: row, flag: flag }).then(({ data }) => {
        this.success("邮箱发送成功");
      });
    },
    getDate() {
      // API.getHomeBusiness().then(({ data }) => {
      //   this.businessData = data;
      // });
      // API.getHomeCustomer().then(({ data }) => {
      //   this.customerData = data;
      // });
      // API.getBalanceDays().then(({ data }) => {
      //   this.expired = data.expired;
      //   this.noOverdueList = data.noOverdueList;
      // });
    },
    // getAuditCenter() {
    //   API.getAuditCount({}).then((res) => {
    //     this.auditNum = res.totalCount;
    //   });
    // },
    auditList() {
      // if (!this.auditNum) return this.error("无未审核单据");
      this.$router.push({
        path: "/backstage/customerManage/auditCenter",
      });
    },
  },
};
</script>

<style scoped lang="scss">
.echarts {
  width: 100%;
  height: 100%;
}

.body-p10 {
  padding: -15px !important;
  // background-color: #f5f5f5;
  overflow-y: scroll;
}

.row-list {
  display: flex;
  margin: 0 -30px 20px -10px;

  .col-item {
    position: relative;
    padding: 0 10px;
    box-sizing: border-box;

    &.col-8 {
      width: 49%;
    }

    &.col-16 {
      width: 66.66%;
    }
  }
}

.header-info-list {
  display: flex;
  margin: 0 -10px 20px -10px;

  .info-item {
    position: relative;
    width: 31.3%;
    margin: 0 10px;
    height: 140px;
    box-sizing: border-box;
    padding: 20px 30px 20px 30px;
    border-radius: 8px;
    color: white;

    // &.order {
    //   // background: linear-gradient(to right, #5773f4, #c080f6);
    //   // background: #f3f3f3;
    // }

    &.user {
      background: linear-gradient(to right, #6097e8, #6fc2e2);
    }

    &.up {
      background: linear-gradient(to right, #d85753, #ea9895);
    }

    .item-title {
      position :relative p {
        font-size: 18px;
      }

      .img1 {
        margin-right: 10px;
      }

      img {
        width: auto;
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
      }
    }
  }

  .number {
    position: absolute;
    left: 30px;
    bottom: 30px;
    font-size: 38px;
    letter-spacing: 0.2em;

    .unit {
      font-size: 14px;
    }
  }

  .money {
    position: absolute;
    right: 30px;
    bottom: 30px;
    font-size: 32px;
    letter-spacing: 0.2em;

    .unit {
      font-size: 14px;
    }
  }
}

.panel-content {
  font-size: 12px;
  color: #333;
  background-color: white;
  border-radius: 4px;
  box-shadow: 1px 1px 7px #d9d9d9;

  .panel-title {
    padding: 0 20px;
    border-bottom: 1px solid #e6e6e6;
    font-size: 15px;
    line-height: 50px;
    color: black;
    font-weight: bold;
  }

  .panel-list {
    height: 240px;
    overflow: auto;

    .panel-item {
      position: relative;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;

      &::after {
        display: block;
        content: "";
        height: 1px;
        position: absolute;
        left: 20px;
        right: 20px;
        bottom: 0;
        background-color: #e6e6e6;
      }

      .model {
        color: #333;

        .serial {
          color: #1a9fd3;
          font-weight: bold;
          margin-right: 15px;
        }
      }

      .num {
        color: #e65c5a;
      }
    }
  }
}

.msg-content {
  font-size: 12px;
  color: #333;
  background-color: white;
  border-radius: 4px;
  box-shadow: 1px 1px 7px #d9d9d9;

  .msg-title {
    padding: 0 20px;
    border-bottom: 1px solid #e6e6e6;
    font-size: 18px;
    line-height: 50px;
    color: black;
    font-weight: bold;
  }
  .msg-title-backgroud-1{
        background: rgb(104, 144, 255);
  }
    .msg-title-backgroud-2{
        background: rgb(237, 97, 255);
  }
    .msg-title-backgroud-3{
        background: rgb(129, 255, 182);
  }
    .msg-title-backgroud-4{
        background: rgb(255, 164, 148);
  }
  
  .msg-list2 {
    height: 280px;
    overflow: auto;
  }
  .msg-list {
    height: 240px;
    overflow: auto;

    .title {
      position: relative;
      padding: 14px 20px 2px 20px;
      // cursor: pointer; // 光标
      font-weight: bolder;
      font-size: 20px;
    }
    .msg-item {
      position: relative;
      padding: 10px 20px 2px 20px;
      // cursor: pointer; // 光标
      font-weight: bolder;
      font-size: 18px;

      // &:hover {
      //   background-color: #ebf2f7;
      // }
    }
    .msg-item2 {
      position: relative;
      padding: 22px 20px 0px 20px;
      font-weight: bolder;
      font-size: 16px;
      text-align: center;
      // &:hover {
      //   background-color: #ebf2f7;
      // }
    }
  }
}

.no-data {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 30%;
  }
}

.data1-top {
  margin: 10px 10px 0px 20px;
  font-size: 20px;
  color: #4e88a3; // #0093ff;
}

.data2-top {
  margin: 18px 0px 0px 0px;
  font-size: 20px;
  color: #4e88a3; // #0093ff;  #75b1cc
  text-align: center;
}

.data1-up {
  margin: 0px 20px;
  font-size: 14px;
  color: #7a7a7a;
}

.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 4px;
}
.bg-purple-dark {
  background: #99a9bf;
}
.bg-purple {
  background: #f2f5fa;
}
.bg-purple-interval {
  background: #ffffff;
}
.bg-purple-light {
  background: #e5e9f2;
}
.grid-content {
  border-radius: 4px;
  min-height: 36px;
  height: 100px;
}
.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}
@import "@/assets/css/element/font-color.scss";
</style>

<style  lang="scss">
.icon {
  color: #3d97ff;
  width: 35px;
  height: 35px;
  cursor: pointer;
}
.container {
  width: 100%;
  height: 96px;
  // background-color: #f8f8f8;
  padding-top: 0px;
  padding-bottom: 10px;
  // overflow-y: auto;
  box-sizing: border-box;
  .content {
    margin: 0 0 0 12px;
    max-width: 48%;
  }
  //  @media screen and (max-width: 1920px) {
  //   .content {
  //     max-width: 1400px;
  //   }
  // }
  // @media screen and (max-width: 1366px) {
  //   .content {
  //     max-width: 1000px;
  //   }
  // }
  @media screen and (max-width: 1366px) {
    .content {
      max-width: 95%;
    }
  }
  .topHead {
    display: flex;
  }
  .boxItem {
    flex: 1;
    height: 80px;
    border-radius: 10px;
    margin-left: 10px;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    border: 1px solid #ccc;
    background-color: #fff;

    .center {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      cursor: pointer;
    }
  }
  .boxItem::after {
    content: "";
    position: absolute;
    height: 6px;
    width: 100%;
    background-color: #fd9f7e;
    bottom: 0;
  }

  .boxItem:hover {
    color: #fff;
    background-color: #fd9f7e;
  }
  .blue::after {
    background-color: #3d97ff;
  }
  .blue:hover {
    background-color: #3d97ff;
  }
  .green::after {
    background-color: #41b348;
  }
  .green:hover {
    background-color: #41b348;
  }
  .noneLine::after {
    background-color: transparent;
  }
  .left {
    flex: 1;
    height: 100%;
  }
  .right {
    flex: 2;
    height: 100%;
    display: flex;
  }
  //第二排样式
  .mid {
    width: 100%;
    margin-top: 10px;
    height: 200px;
    display: flex;
    box-sizing: border-box;
    .border {
      border: 1px solid #ccc;
    }
    .left {
      display: flex;
      flex-direction: column;
    }

    .right {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
    }
    .rightMain-bottom {
      flex: 1;
      display: flex;
    }
    .rightMain {
      margin-left: 10px;

      border-radius: 10px;
      padding: 20px;
      background-color: #fff;
      box-sizing: border-box;
      flex: 1;
      .head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .headLeft {
          font-weight: bold;
        }
        .headRight {
          .iconBtn {
            display: inline-block;
            border: 1px solid #ccc;
            cursor: pointer;
            border-radius: 4px;
            height: 16px;
            user-select: none;
            &:active {
              border-color: #777;
            }
          }
          .disable {
            cursor: not-allowed;
            border-color: #eee;
            color: #ccc;
            &:active {
              border-color: #eee !important;
            }
          }

          .text {
            margin: 0 10px;
            font-size: 14px;
            font-weight: bold;
          }
        }
      }

      .util {
        margin-top: 10px;
        font-size: 12px;
        color: #999;
        text-align: right;
      }
      .content {
        display: flex;
        justify-content: space-between;
        margin-top: 37px;
        .contentItem {
          font-weight: bold;
          text-align: center;
          font-size: 14px;

          .contentItemName {
            margin-top: 15px;
          }
        }
      }
    }
  }
  .midLeftItem {
    flex: 1;
    margin-left: 10px;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    &:hover {
      .icon {
        background-color: #3d97ff;
        color: #fff;
      }
    }
    .midLeftItemCenterText {
      font-size: 13px;
      margin-top: 5px;

      cursor: pointer;
    }

    //资金支出颜色
    .expenditure {
      border: 1px solid #3d97ff;
      .transverse,
      .vertical {
        background-color: #3d97ff;
      }
      &:hover {
        background-color: #3d97ff;
        .transverse,
        .vertical {
          background-color: #fff;
        }
      }
    }
    &:hover {
      .midLeftItemCenterText {
        color: #fff;
      }
    }
  }
  //十字
  .crossContent {
    position: relative;
    width: 60px;
    height: 60px;
    border: 1px solid #41b348;
    border-radius: 6px;
    margin: 0 auto;
    cursor: pointer;
    div {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .transverse {
      height: 40px;
      width: 5px;
      background-color: #41b348;
      border-radius: 6px;
    }
    .vertical {
      height: 5px;
      width: 40px;
      background-color: #41b348;
      border-radius: 6px;
    }
    &:hover {
      background-color: #41b348;
      .transverse,
      .vertical {
        background-color: #fff;
      }
    }
  }
}
</style>