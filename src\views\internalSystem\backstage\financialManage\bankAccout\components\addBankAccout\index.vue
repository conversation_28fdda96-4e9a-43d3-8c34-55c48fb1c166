<template>
  <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" append-to-body @close="dialogCancel" width="660px"
    :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
      <el-form-item label="银行编码" prop="bank_code">
        <el-input v-model="ruleForm.bank_code" clearable></el-input>
      </el-form-item>
      <el-form-item label="银行名称" prop="bank_name">
        <el-input v-model="ruleForm.bank_name" clearable></el-input>
      </el-form-item>
      <el-form-item label="户名" prop="accout_name">
        <el-input v-model="ruleForm.accout_name" clearable></el-input>
      </el-form-item>
      <el-form-item label="账号" prop="bank_accout">
        <el-input v-model="ruleForm.bank_accout" clearable></el-input>
      </el-form-item>
      <el-form-item label="余额" prop="remain">
        <el-input v-model="ruleForm.remain"  clearable></el-input>
      </el-form-item>
      <el-form-item label="企业单位" prop="fk_sales_unit_id">
        <el-select v-model="ruleForm.fk_sales_unit_id" placeholder="请选择企业单位" filterable clearable>
          <el-option v-for="item in companyList" :key="item.sales_unit_id" :label="item.company_name"
            :value="item.sales_unit_id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="说明" prop="remark">
        <el-input v-model="ruleForm.remark" type="textarea" clearable></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm('ruleForm')">保 存</el-button>
      <el-button @click="dialogCancel">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script src="./index.js">

</script>