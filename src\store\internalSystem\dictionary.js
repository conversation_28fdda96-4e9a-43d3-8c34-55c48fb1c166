import API from "@/api/internalSystem/common/index.js";
import { Loading } from "element-ui";
const state = {
  baseData: [],
};
const getters = {
  baseData: (state) => state.baseData,
};
const mutations = {};
const actions = {
  GET_INFO: (store) => {
    let loadingInstance = Loading.service({
      lock: true,
      text: "Loading",
      spinner: "el-icon-loading",
      background: "rgba(0, 0, 0, 0.7)",
    });

    API.queryDataDictionary({})
      .then((res) => {
        store.state["baseData"] = res.data || [];

        loadingInstance.close();
      })
      .catch(() => {
        loadingInstance.close();
      });
  },
};
export default {
  state,
  getters,
  mutations,
  actions,
};
