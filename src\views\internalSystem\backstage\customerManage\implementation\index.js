import API from '@/api/internalSystem/customerManage/implementation'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddImplementation from "./components/addImplementation/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import AuditDetail from '@/mixins/auditDetail.js'
import {
  getOptions,
} from "@/common/internalSystem/common.js"
import {
  mapGetters
} from "vuex";
export default {
  name: "implementation",
  mixins: [AuditDetail],
  data() {
    return {
      title: "客户实施单",
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        customer_name: "",
        fk_sell_employee_id: "",
        auditState: "",
        startTime: "",
        endTime: ""
      },
      tableList: [{
          name: "审核状态",
          value: "auditStateFormat",
          width: 100
        },
        {
          name: "编号",
          value: "implementation_no",
          width: 130
        },
        {
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "联系人",
          value: "link_man",
          width: 80
        },
        {
          name: "联系电话",
          value: "phone",
          width: 120
        },
        {
          name: "销售合同单号",
          value: "contract_no",
          width: 160
        },
        {
          name: "销售员",
          value: "fk_sell_employee_name",
          width: 100
        },
        {
          name: "制单人",
          value: "voucher_name",
          width: 100
        },
        {
          name: "实施人",
          value: "implementation_user_name",
          width: 100
        },
        {
          name: "实施时间",
          value: "implementation_time",
          width: 120
        },
        {
          name: "结束时间",
          value: "over_time",
          width: 120
        },
        {
          name: "制单时间",
          value: "update_time",
          width: 120
        }
      ],
      auditStateList: [],
      employeeList: [],
      isAdd: false
    };
  },
  created() {
    if (!["总经理"].includes(this.cookiesUserInfo.role_name)) {
      this.formSearch.fk_sell_employee_id = this.cookiesUserInfo.userId;
    }
  },
  mounted() {
    this.getList();
    this.$store.dispatch('getEmployee').then(res => {
      this.employeeList = res;
    });
  },
  methods: {
    getList(f = false) {
      this.auditStateList = getOptions('t_implementation', 'auditState');
      this.isAdd = false;
      this.isAudit = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
          if (f)
            param.pageNum = 1;
          // let isJurisdiction = this.buttonPermissions.length ? (this.existence(this.buttonPermissions, 'ALL_CUSTOMER_IMPLEMENT_NEW')) : false
          // param.isJurisdiction = isJurisdiction ? 1 : 0;
          param.isJurisdiction = this.permissionToCheck('ALL_CUSTOMER_IMPLEMENT_NEW') ? 1 : 0;
          API.query(param).then(res => {
            this.tableData = res.data;
            this.$refs.pagination.setTotal(res.totalCount);
          }).finally(() => {
            this.loading = false;
          });
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addImplementation.Show();
    },
    modify(item) {
      let params = {
        implementation_id: item.implementation_id
      };
      API.getInfo(params)
        .then(data => {
          this.isAdd = true;
          this.$refs.addImplementation.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      if (item.auditState == 1)
        return this.error("该单据已审核，不允许删除");
      let params = {
        implementation_id: item.implementation_id
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    }
  },

  components: {
    AddImplementation,
    Pagination,
    TableView,
    MyDate
  },
  computed: {
    ...mapGetters(["buttonPermissions","implementation_audit","cookiesUserInfo"])
  }
};