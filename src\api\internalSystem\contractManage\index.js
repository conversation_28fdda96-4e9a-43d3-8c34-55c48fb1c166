import Axios from "@/api";
import environment from "@/api/environment";

export default {
  //授权码合同
  unSetCodeContractList: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}contractManage/unSetCodeContractList`,
      params
    );
  },
  //设置授权码
  setSoftwareNo: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}contractManage/setSoftwareNo`,
      params
    );
  },
  
  //完成实施
  setImplementContract: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}contractManage/setImplementContract`,
      params
    );
  },

  //未培训合同
  unTrainContractList: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}contractManage/unTrainContractList`,
      params
    );
  },
  
  //设置培训完成，更改培训人
  setTrainContract: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}contractManage/setTrainContract`,
      params
    );
  },
  //未实施合同单
  unImplementContractList: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}contractManage/unImplementContractList`,
      params
    );
  },
//授权实施单
setImplementCodeContract: (params) => {
  return Axios.post(
    `${environment.internalSystemAPI}contractManage/setImplementCodeContract`,
    params
  );
},
//快到期
nearExpiredContractList: (params) => {
  return Axios.post(
    `${environment.internalSystemAPI}contractManage/nearExpiredContractList`,
    params
  );
},
//确认回访
returnVisit: (params) => {
  return Axios.post(
    `${environment.internalSystemAPI}contractManage/returnVisit`,
    params
  );
},
//回访列表
returnVisitList: (params) => {
  return Axios.post(
    `${environment.internalSystemAPI}contractManage/returnVisitList`,
    params
  );
},




//到期
expiredContractList: (params) => {
  return Axios.post(
    `${environment.internalSystemAPI}contractManage/expiredContractList`,
    params
  );
},
//到期续约
renewContract: (params) => {
  return Axios.post(
    `${environment.internalSystemAPI}contractManage/renewContract`,
    params
  );
},


};
