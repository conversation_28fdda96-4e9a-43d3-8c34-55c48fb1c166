<textarea onmousedown="rightEvent(this,event)" class="textareas_td" style="font-size: 30px; margin-bottom: 2px; height: 26px; font-weight: 700;" onchange="makeExpandingArea(this)">软 件 购 买 合 同 书</textarea>
      
<table width="100%" border="2" cellspacing="0" cellpadding="0" style="border-collapse: collapse;">
      <tbody><tr>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">需方(甲方):</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{contract.customerName}}</textarea></td>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">合同编号：</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{contract.contractNo}}</textarea></td>
      </tr>
      <tr>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">供方(乙方):</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{salesUnit.companyName}}</textarea></td>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">签订地点：</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{salesUnit.operateCityName}}</textarea></td>
      </tr>
  </tbody></table>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 42px; font-size: 16px;" onchange="makeExpandingArea(this)">甲乙双方经友好协商一致，甲方向乙方购买"{{contractDetail.brandName}}"，根据《中华人民共和国合同法》及其他法律法规签订本合同，并由双方共同恪守.条款如下：  </textarea>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 20px; font-size: 17px; font-weight: 700;" rows="1" onchange="makeExpandingArea(this)">一、软件产品名称、规格、价格：  </textarea>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 20px; font-size: 15px;" rows="1" onchange="makeExpandingArea(this)">乙方向甲方销售的软件产品为"{{contractDetail.brandName}}"， 签订时间：{{contract.newTime}}  </textarea>
  <script>
  </script>
  <table width="100%" border="2" cellspacing="0" cellpadding="0" style="border-collapse: collapse;">
      <tbody><tr>
          <td style="width:50px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">序号</textarea></td>
          <td style="width:350px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">产品名称</textarea></td>
          <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">版本</textarea></td>
          <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">计量单位</textarea></td>
          <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">合同数量</textarea></td>
          <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">合同单价(元)</textarea></td>
          <td style="width:90px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">合同金额(元)</textarea></td>
      </tr>
      <start>
      <tr>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">1</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="{{contractDetail.rows}}" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{contractDetail.brandName}}</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{contractDetail.brandVersion}}</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108);">{{contractDetail.measurementUnitName}}</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{contractDetail.contractCount}}</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{contractDetail.contractPrice}}</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{contractDetail.contractAmount}}</textarea></td>
      </tr>
      </start>
      <tr>
          <td colspan="2"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">共计人民币（大写）：</textarea></td>
          <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
          <td colspan="5"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{contract.upCase}}</textarea> </td>
          <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
          <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
          <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
          <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
      </tr>
      <tr>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">备注</textarea></td>
          <td colspan="6"><textarea onmousedown="rightEvent(this,event)" style="text-align: left; font-size: 16px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108); height: 30px;line-height: 30px;" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"> {{contract.remark}} </textarea></td>
          <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
          <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
          <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
          <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
          <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
      </tr>
  </tbody></table>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 20px; font-weight: 700; font-size: 17px;margin-top:4px;" rows="1" onchange="makeExpandingArea(this)">二、付款方式：  </textarea>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 40px; font-size: 16px;" rows="1" onchange="makeExpandingArea(this)">甲方应在合同生效后壹个工作日内向乙方支付{{contractDetail.prepaymentRate}}%款项,共计:人民币：￥{{contractDetail.preAmount}}元(人民币大写:{{contractDetail.upCasePreAmount}},此合同含增值税专用发票,全款到账授权正式版本)   </textarea>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 20px; font-weight: 700; font-size: 17px;" rows="1" onchange="makeExpandingArea(this)">三、版权归属及保密条款：  </textarea>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 61px; font-size: 16px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108);" onchange="makeExpandingArea(this)">本软件之版权归乙方，甲方经由乙方授权使用本软件，其数据版权归甲方。乙方对在本合同签订及履行过程中知悉的甲方的商业秘密(采购价格，采购渠道和供应商单位信息,销售价格，销售客户信息等所有数据)负责保密，保证不向任何第三方进行透露并使用.同时甲方需对本合同价格、软件功能签订的条款等保密，不得向其他用户透入。如其中一方违约则造成的所有损失由违约方承担，包括（但不限于）法院诉讼的费用，合理的律师酬金和费用，所有损失和损害等。   </textarea>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 20px; font-weight: 700; font-size: 17px;" rows="1" onchange="makeExpandingArea(this)">四、相关服务：（服务内容请根据公司政策调整）  </textarea>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 140px; font-size: 16px;" onchange="makeExpandingArea(this)">1、安装调试：软件的安装、调试均由乙方负责，除合同条款商定的费用以外，免收软件的安装、调试费用.
2、培训：乙方为甲方提供软件操作的免费培训，培训的方式为：远程培训，程度达到公司员工熟练操作。
   注意:免费维护期过后的维护费用见维护协议。
3、乙方对系统提供技术支持，免费维护期满后双方须签订有偿的《软件服务协议》。
   A、乙方在国家正常工作日内随时为甲方以电话、传真、邮件等方式免费提供所买产品的服务与技术支持维护。
   B、非正常使用造成软件损坏，乙方负责有偿保修;
   C、具体内容：维护软件现有功能的正常使用，免费升级大众化功能。  
4、超出上述范围的服务另外收取费用。 </textarea>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 40px; font-weight: 700; font-size: 17px;" onchange="makeExpandingArea(this)" rows="1">五、许可软件升级乙方为甲方提供有偿许可软件升级服务。具体按照福州吉勤信息科技有限公司规定的全国统一办法执行。    </textarea>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 20px; font-weight: 700; font-size: 17px;" rows="1" onchange="makeExpandingArea(this)">六、争议解决方式：    </textarea>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 56px; font-size: 16px; font-weight: 400; text-decoration: none solid rgb(103, 106, 108);" onchange="makeExpandingArea(this)">1、双方合同履行过程中发生争议，双方应协商解决或请求调解，否则应提交合同签定地仲裁机关仲裁。
2、甲乙双方确定：以上合同签定，以乙方所在地为准。 
3、未尽事宜双方友好协商解决。    </textarea>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 20px; font-weight: 700; font-size: 17px;" onchange="makeExpandingArea(this)" rows="1">七、本合同正本一式贰份（传真纸有效），甲乙双方各执壹份，经双方签字盖章后生效。    </textarea>
  <textarea onmousedown="rightEvent(this,event)" class="textareas" style="width: 100%; height: 20px; font-size: 16px;" onchange="makeExpandingArea(this)" rows="1">付款资料如下:    </textarea>
  <table width="100%" border="2" cellspacing="0" cellpadding="0" style="border-collapse: collapse;">
      <tbody><tr>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 32px;">开 户 名：</textarea></td>
          <td colspan="3"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{salesUnit.companyName}}</textarea></td>
          <td width="100px;" style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
          <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
      </tr>
      <tr>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 32px;">开 户 行：</textarea></td>
          <td colspan="3"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{salesUnit.openingBank}}</textarea></td>
          <td width="100px;" style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
          <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
      </tr>
      <tr>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 32px;">帐     户：</textarea></td>
          <td colspan="3"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{salesUnit.bankAccount}}</textarea></td>
          <td width="100px;" style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
          <td style="display: none"><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)"></textarea></td>
      </tr>
      <tr>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 32px;">甲方盖章：</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{contract.customerName}}</textarea></td>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 32px;">乙方盖章：</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{salesUnit.companyName}}</textarea></td>
      </tr>
      <tr>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 32px;">签定地址：</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 68px;">{{contract.address}}
</textarea></td>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 32px;">签定地址：</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 68px;">{{salesUnit.operateAddress}}
</textarea></td>
      </tr>
      <tr>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 32px;">电     话：</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 36px;">{{contract.customerTelephone}}</textarea></td>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 32px;">电     话：</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 36px;">{{contract.sellTel}}</textarea></td>
      </tr>
      <tr>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 32px;">传     真：</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{contract.fax}}</textarea></td>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 32px;">传     真：</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{salesUnit.fax}}</textarea></td>
      </tr>
      <tr>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 32px;">代理人签字</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{contract.linkMan}}</textarea></td>
          <td width="100px;"><textarea onmousedown="rightEvent(this,event)" class="textareas_td_t" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px; height: 32px;">代理人签 字</textarea></td>
          <td><textarea onmousedown="rightEvent(this,event)" class="textareas_td" rows="1" onchange="makeExpandingArea(this)" style="font-size: 16px;">{{contract.sellEmployeeName}}</textarea></td>
      </tr>
  </tbody></table>