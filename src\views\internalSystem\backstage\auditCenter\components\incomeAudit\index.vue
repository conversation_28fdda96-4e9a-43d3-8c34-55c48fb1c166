<template>
  <div v-if="dialogVisible">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="openAudit">审 核</el-button>
    </div>
    <el-form :model="ruleForm" ref="ruleForm" label-width="100px" class="mt10">
      <el-row>
        <el-col :span="8">
          <el-form-item label="收入编号">
            <el-input
              v-model="ruleForm.income_code"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="收入类型" prop="income_type">
            <el-select
              disabled
              v-model="ruleForm.income_type"
              placeholder="请选择收入类型"
              class="inputBox"
              filterable
              clearable
              style="width: 100%;"
            >
              <el-option
                v-for="item in income_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="收入银行" prop="fk_bank_account_id">
            <el-select
              disabled
              v-model="ruleForm.fk_bank_account_id"
              placeholder="请选择收入银行"
              filterable
              clearable
              style="width: 100%;"
            >
              <el-option
                v-for="item in bankList"
                :key="item.financial_bank_accout_id"
                :label="item.bank_name"
                :value="item.financial_bank_accout_id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="收入金额" prop="amount">
            <el-input
              disabled
              v-model="ruleForm.amount"
              
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="收入日期" prop="income_time">
            <my-date
              disabled
              v-model="ruleForm.income_time"
              hint="请选择收入日期"
              :isAllDate="false"
            ></my-date>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="收入明细" prop="content">
            <el-input
              disabled
              v-model="ruleForm.content"
              type="textarea"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog
      title="审核"
      :visible.sync="dialogVisibleAudit"
      append-to-body
      @close="dialogCancelAudit"
      width="660px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      v-dialogDrag
    >
      <el-form
        :model="auditForm"
        :rules="rules"
        ref="auditForm"
        label-width="100px"
      >
        <!-- <el-form-item label="审核状态" prop="auditState">
          <el-select
            v-model="auditForm.auditState"
            placeholder="请选择审核状态"
            filterable
            clearable
            style="width: 100%;"
          >
            <template v-for="item in contract_auditStateList">
              <el-option
                v-if="item.id != ruleForm.audit_state && item.id != 3"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </template>
          </el-select>
        </el-form-item> -->
        <el-form-item label="审核状态" prop="auditState">
          <el-radio-group v-model="auditForm.auditState">
            <template v-for="item in contract_auditStateList">
              <el-radio
              v-if="item.id != ruleForm.audit_state && item.id != 3"
                :key="item.id"
                :label="item.value"
                :value="item.value"
                border
                style="height: 30px;"
                ><span style="font-size:16px;">{{ item.label }}</span></el-radio
              >
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="auditRemark">
          <el-input
            v-model="auditForm.auditRemark"
            type="textarea"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm('auditForm')"
          :loading="loading"
          >保 存</el-button
        >
        <el-button @click="dialogCancelAudit">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script src="./index.js">
</script>

<style lang="scss" scoped>
@import "@/assets/css/element/font-color.scss";
</style>
