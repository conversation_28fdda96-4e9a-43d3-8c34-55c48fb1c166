<template>
  <div>
    <div class="box">
      <div class="box-head">提Bug</div>
      <div style="margin-top: 6px; margin-left: 10px">
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </div>
      <div class="box-content">
        <el-form
          :model="head"
          :rules="rules"
          :status-icon="true"
          ref="headForm"
          label-width="80px"
          class="head-ruleForm"
        >
          <el-row :gutter="10" class="mt10">
            <el-col :span="12">
              <el-form-item label="所属产品" prop="brand_id">
                <el-select
                  v-model="head.brand_id"
                  @change="brandChange"
                  placeholder="请选择所属产品"
                >
                  <el-option
                    v-for="(item, index) in systemList"
                    :key="index"
                    :label="item.brand_type"
                    :value="item.brand_id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属模块" prop="module_id">
                <!-- <el-select v-model="head.product" placeholder="请选择所属模块">
                  <el-option
                    v-for="(item, index) in list"
                    :key="index"
                    :label="item.title"
                    :value="item.id"
                  >
                  </el-option>
                </el-select> -->

                <el-cascader
                  clearable
                  :props="cascaderProps"
                  placeholder="请选择所属模块"
                  v-model="head.module_id"
                  :options="moduleList"
                ></el-cascader>
              </el-form-item>
            </el-col>

            <!-- <el-col :span="12">
              <el-form-item label="影响版本" prop="customer_name">
                <div class="innerFlex">
                  <el-select
                    class="flex1"
                    v-model="head.multiple"
                    multiple
                    placeholder="请选择所属模块"
                  >
                    <el-option
                      v-for="(item, index) in list"
                      :key="index"
                      :label="item.title"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                  <span class="blue">刷新</span>
                  <span class="border">所有</span>
                </div>
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="当前指派" prop="designated_person_id">
                <div class="innerFlex">
                  <el-select
                    class="flex1"
                    v-model="head.designated_person_id"
                    placeholder="请选择需要指派的员工"
                  >
                    <el-option
                      v-for="(item, index) in userList"
                      :key="index"
                      :label="item.employee_name"
                      :value="item.employeeId"
                    >
                    </el-option>
                  </el-select>

                  <!-- <span class="border">所有用户</span> -->
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="截止时间" prop="deadline">
                <el-date-picker
                  style="width: 100%"
                  v-model="head.deadline"
                  type="date"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="Bug类型" prop="problem_type">
                <div class="innerFlex">
                  <el-select
                    class="flex1"
                    v-model="head.problem_type"
                    placeholder=""
                  >
                    <el-option
                      v-for="(item, index) in problem_type"
                      :key="index"
                      :label="item.label"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>

                  <span>操作系统</span>
                  <el-select
                    class="flex1"
                    v-model="head.operating_system"
                    placeholder=""
                  >
                    <el-option
                      v-for="(item, index) in operating_system"
                      :key="index"
                      :label="item.label"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                  <span>浏览器</span>
                  <el-select
                    class="flex1"
                    v-model="head.problem_browser"
                    placeholder=""
                  >
                    <el-option
                      v-for="(item, index) in problem_browser"
                      :key="index"
                      :label="item.label"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="Bug标题" prop="problem_title">
                <div class="innerFlex">
                  <el-input
                    v-model="head.problem_title"
                    class="flex1"
                  ></el-input>

                  <span>严重程度</span>

                  <el-select
                    class="flex70px"
                    v-model="head.problem_severity"
                    placeholder=""
                    ref="severity"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in 4"
                      :key="index"
                      :label="item"
                      :value="item"
                    >
                      <span class="radus">{{ item }}</span>
                    </el-option>
                  </el-select>
                  <span>优先级</span>

                  <el-select
                    class="flex70px"
                    v-model="head.problem_priority"
                    placeholder=""
                    clearable
                  >
                    <el-option value="">
                      <span class="radus wenhao">?</span>
                    </el-option>
                    <el-option
                      v-for="(item, index) in 4"
                      :key="index"
                      :label="item"
                      :value="item"
                    >
                      <span class="radus">{{ item }}</span>
                    </el-option>
                  </el-select>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="重现步骤" prop="problem_content">
                <RichText
                  v-if="reset"
                  :value="head.problem_content"
                  @input="richChange"
                />
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="附件" prop="customer_name">
                <div
                  class="innerFlex"
                  v-for="(item, index) in fileList"
                  :key="index"
                  :class="[index === 0 ? '' : 'mt10']"
                >
                  <!-- <el-input class="flex1" v-model="item.file_name"></el-input> -->
                  <!-- <div class="flex1"> <input type="file" name="" id=""> -->
                  <div class="flex1">
                    <el-upload
                      class="flex1"
                      style="width: 100%"
                      action=""
                      :show-file-list="false"
                      :http-request="(file) => ossFile(file, index)"
                    >
                      <el-input
                        readonly
                        placeholder="点击上传文件"
                        v-model="item.file_name"
                      ></el-input>
                    </el-upload>
                  </div>

                  <el-input
                    class="flex1"
                    placeholder="附件标题"
                    v-model="item.problem_file_name"
                  ></el-input>
                  <span @click="fileAddRow">
                    <i class="el-icon-plus"></i>
                  </span>
                  <span class="border" @click="fileDeleteRow(index)">
                    <i class="el-icon-close"></i>
                  </span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import CommonAPI from "@/api/internalSystem/bugManage/common/index.js";
import API from "@/api/internalSystem/bugManage/bugContent/index.js";
// import RichText from "@/components/RichText/RichText.vue";
import RichText from "@/components/internalSystem/RichText/RichText.vue";
import { mapGetters, mapMutations } from "vuex";
import ossApi from "@/api/aliyun/oss.js";
export default {
  data() {
    return {
      fileList: [{ file_name: "", problem_file_name: "", file_url: "" }],
      systemList: [], //产品系统列表
      moduleList: [],
      userList: [],
      cascaderProps: {
        label: "module_name",
        value: "module_id",
        checkStrictly: true,
      },
      reset: false,
      head: {
        add_time: "",
        add_user_id: "",
        add_user_name: "",
        brand_id: 47,
        brand_name: "",
        deadline: "",
        designated_person_id: "",
        designated_person_name: "",
        is_confirm: "",
        is_confirm_name: "",
        module_id: "",
        module_name: "",
        operating_system: "",
        operating_system_name: "",
        problem_browser: "",
        problem_browser_name: "",
        problem_content: "",
        problem_id: "",
        problem_priority: "",
        problem_severity: "",
        problem_state: "",
        problem_state_name: "",
        problem_title: "",
        problem_type: "",
        problem_type_name: "",
        solution: null,
        solution_name: null,
        solve_person_id: null,
        solve_person_name: null,
        solve_time: null,
        update_time: "",
        update_user_id: "",
        update_user_name: "",
      },
      rules: {
        brand_id: [
          { required: true, message: "请选择产品", trigger: "change" },
        ],
        module_id: [
          { required: true, message: "请选择模块", trigger: "change" },
        ],
      },
      returnRoutePath: "",
    };
  },
  mounted() {
    this.getSystemList();
    this.getUserList();
  },
  created() {},
  activated() {
    this.reset = false;

    this.$route.meta.title = "添加产品问题";
    this.getRouteData();

    this.$nextTick(() => {
      this.reset = true;
      if (this.head.brand_id) {
        this.getmoudelList();
      }
    });
  },
  methods: {
    getRouteData() {
      Object.assign(this.$data.head, this.$options.data().head);
      this.$nextTick(() => {
        this.$refs.headForm.resetFields();
      });
      let { problem_id, type, returnRoutePath } = this.$route.query;

      if (!problem_id) return;
      if (type === "edit") this.$route.meta.title = "编辑产品问题";
      this.returnRoutePath = returnRoutePath;
      this.getDetail(problem_id, type);
    },
    getDetail(problem_id, type = "") {
      let params = {};

      params.problem_id = problem_id;
      this.head = {
        module_id: "",
        problem_content: "",
        problem_priority: "",
      };
      API.getBugInfo(params).then((res) => {
        if (type === "copy") res.data.problem_id = "";
        this.head = res.data;
        this.getmoudelList(false);
      });
    },
    getSystemList() {
      CommonAPI.getSystemQuery({ brand_classify: 1 }).then((res) => {
        this.systemList = res.data;
      });
    },
    brandChange() {
      if (this.head.brand_id) this.getmoudelList();
    },
    //富文本取值
    richChange(value) {
      this.head.problem_content = value;
    },
    getmoudelList(clear = true) {
      if (clear) this.head.module_id = "";
      CommonAPI.getmoudelList({ brand_id: this.head.brand_id }).then((res) => {
        this.moduleList = res.data || [];
      });
    },
    getUserList() {
      CommonAPI.getUserList().then((res) => {
        this.userList = res.data;
      });
    },

    fileAddRow() {
      this.fileList.push({
        file_name: "",
        problem_file_name: "",
        file_url: "",
      });
    },
    fileDeleteRow(index) {
      this.fileList.splice(index, 1);
      if (!this.fileList.length) this.fileAddRow();
    },
    ossFile(file, index) {
      ossApi
        .uploadFile(file.file)
        .then((res) => {

          this.fileList[index].file_name = file.file.name;
          this.fileList[index].file_url = res.url;
          this.fileList[index].file_size = file.file.size;
          this.fileList[index].file_suffix = file.file.name.substr(
            file.file.name.lastIndexOf(".") + 1
          );

          this.$forceUpdate();
        })
        .catch(() => {});
    },
    save() {
      const head = this.head;
      //将数组转换成取数组最后一个值
      if (Array.isArray(head.module_id)) {
        head.module_id = head.module_id.length
          ? head.module_id[head.module_id.length - 1]
          : "";
      }
      let APIName = head.problem_id ? "updateBug" : "addBug";
      let params = Object.assign({}, head);
      params.file = this.fileList;
      if (params.file.length === 1 && !params.file[0].file_url) {
        params.file = [];
      }
      API[APIName](params).then((res) => {
        this.success("保存成功");
        if (this.returnRoutePath) {
          this.$router.push({
            path: this.returnRoutePath,
            query: {
              problem_id: res.data,
            },
          });
        } else {
          this.$router.push({
            name: "bugList",
          });
        }
        this.$nextTick(() => {
          this.SET_CLOSEROUTER("/backstage/bugManage/bugContent/addBug");
        });
      });
    },
    close() {
      this.$router.push({
        name: "bugList",
      });
      this.$nextTick(() => {
        this.SET_CLOSEROUTER("/backstage/bugManage/bugContent/addBug");
      });
    },
    ...mapMutations(["SET_CLOSEROUTER"]),
  },
  components: {
    RichText,
  },
  computed: {
    ...mapGetters([
      "problem_type",
      "operating_system",
      "problem_browser",
      "problem_state",
    ]),
  },
};
</script>

<style lang="scss" scoped>
.blue {
  color: #03c;
}

.radus {
  border-radius: 50%;
  height: 18px;
  width: 18px;
  background-color: #db7c12;
  color: #fff;
  display: inline-block;
  display: flex;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  align-items: center;
  box-sizing: border-box;
}

.wenhao {
  border: 2px solid #ccc;
  background-color: #fff;
  color: #ccc;
  font-weight: bold;
}
.box {
  // width: 1200px;
  margin: 4px auto;
  border: 1px solid #ccc;
  box-sizing: border-box;
  color: #000;
  font-size: 14px;
  .box-head {
    display: flex;
    padding: 5px 20px;
    font-weight: bold;
    border-bottom: 1px solid #ccc;
  }

  .box-content {
    padding: 10px 40px 10px 20px;

    .innerFlex {
      display: flex;
      .flex {
        flex: 1;
      }
      .flex50px {
        flex: 0 0 50px;
      }
      .flex70px {
        flex: 0 0 70px;
      }
      span {
        flex: 0 0 auto;
        background-color: #f5f5f5;
        padding: 0 5px;
        border: 1px solid #ccc;
        border-left: 0;
        border-right: 0;
        font-size: 12px;
        height: 32px;
        box-sizing: border-box;
        cursor: pointer;
      }
      .border {
        border: 1px solid #ccc;
      }
    }
  }
}
</style>
