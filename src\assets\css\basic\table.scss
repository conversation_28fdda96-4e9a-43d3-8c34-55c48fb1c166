#basic,
#console,
#notice,
#omRon,
#electronicOrder,
#orderGoods {
  .table-list-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    .table-headform {
      flex: 0 0 auto;
      height: auto;
      width: 100%;
      box-sizing: border-box;
      margin-left: auto;
      margin-right: auto;
      user-select: none;
    }

    .basicButtonGroup{
      background-color: #fff;
      margin-top: 15px;
      padding: 10px 20px ;
    }
    .tableList {

      flex: 1;
      height: 100%;
      width: calc(100vw - (180px + 30px));
      margin-left: auto;
      margin-right: auto;
      margin-top: 15px;
      padding: 20px 20px 10px;
      box-sizing: border-box;
      // user-select: none;
      background-color: #fff;

      .table-contain {
        flex: 1;
      }
    }

    .table-pagination {
      flex: 0 0 40px;
      height: 40px;
      background-color: #fff;
      padding: 0 20px;
    }
  }
  
}