<template>
  <div class="body-p10">
    <el-form
      :inline="true"
      :model="formSearch"
      size="small"
      v-if="!isAdd && !isAudit"
    >
      <el-form-item label="查询条件">
        <el-input
          v-model="formSearch.questionTitle"
          placeholder="请输入搜索问题"
          clearable
        />
      </el-form-item>

      <el-form-item>
        <my-date v-model="formSearch.startTime" hint="请选择开始时间"></my-date>
      </el-form-item>
      <el-form-item>
        <my-date v-model="formSearch.endTime" hint="请选择结束时间"></my-date>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading"
          >查询</el-button
        >
        <el-button type="success" @click="hotQuery()">查看搜索热词 </el-button>
      </el-form-item>
    </el-form>

    <TableView
      :tableList="tableList"
      :tableData="tableData"
      v-if="!isAdd && !isAudit"
    ></TableView>
    <Pagination
      ref="pagination"
      @success="getList"
      v-show="!isAdd && !isAudit"
    />
    <el-dialog title="查看搜索热词" :visible.sync="dialogVisible" width="30%">
      <el-table
        :data="hotQueryTableData"
        stripe
        border
        style="width: 100%; font-size: 14px"
        align="center"
      >
        <el-table-column prop="question_title" label="关键词" align="center">
        </el-table-column>
        <el-table-column
          prop="query_number"
          label="搜索次数"
          width="100"
          align="center"
        >
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" type="primary"
          >关闭</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss">

</style>