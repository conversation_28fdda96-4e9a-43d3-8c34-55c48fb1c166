import API from "@/api/internalSystem/contractManage/index.js";

import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import Dialog from "./components/dialog/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import { mapGetters } from "vuex";
import contractManageMixns from "@/mixins/contractManage.js"
export default {
  name: "implementation",
  mixins:[contractManageMixns],
  data() {
    return {
      title: "实施合同",
      loading: false,
      selectRecords: [],
      tableData: [],
      isShowDetail:false,
      employeeList:[],
      formSearch: {
      
      },
      tableListCop: [
        {
          name: "合同编号",
          value: "contract_no",
          width: 120,
        },
        {
          name: "客户名称",
          value: "customer_name",
        },
        {
          name: "软件名称",
          value: "gender_name",
          width: 80,
        },
        {
          name: "版本号",
          value: "telephone",
          width: 150,
        },
        {
          name: "维护起始时间",
          value: "maintain_start_time",
          width: 130,
        },
        {
          name: "维护结束时间",
          value: "maintain_stop_time",
          width: 180,
        },
        {
          name: "授权码",
          value: "department_name",
        },
        {
          name: "培训情况",
          value: "department_name2",
        },
      ],
      tableList: [
        {
          name: "完成情况",
          value: "state_name",
          width: 70,
        },
        {
          name: "实施状态",
          value: "work_state_name",
          width: 70,
        },
        {
          name: "审核状态",
          value: "audit_state_name",
          width: 82,
        },
        {
          name: "编号",
          value: "contract_no",
          width: 116,
        },
        {
          name: "客户名称",
          value: "customer_name",
          width: 200,
        },
        {
          name: "销货单位",
          value: "sales_unit_id_format",
        },
        {
          name: "维护起始时间",
          value: "maintain_start_time",
          width: 96,
        },
        {
          name: "维护结束时间",
          value: "maintain_stop_time",
          width: 96,
        },
        {
          name: "合同金额",
          value: "contract_amount",
          width: 72,
        },
        {
          name: "已收款金额",
          value: "receivables_amount",
          width: 82,
        },
        {
          name: "未收款金额",
          value: "not_receivables_amount",
          width: 82,
        },
        {
          name: "已开票金额",
          value: "open_ticket_amount",
          width: 82,
        },
        {
          name: "未开票金额",
          value: "not_open_ticket_amount",
          width: 82,
        },
        {
          name: "单据备注",
          value: "remark",
        },
        {
          name: "付款方式",
          value: "pay_type_name",
          width: 106,
        },
        {
          name: "培训方式",
          value: "train_type_name",
          width: 70,
        },
        {
          name: "销售类型",
          value: "sell_type_name",
          width: 82,
        },
        {
          name: "推荐员工",
          value: "fk_recommend_employee_name",
          width: 70,
        },
        {
          name: "操作员工",
          value: "add_user_name",
          width: 70,
        },
        {
          name: "操作部门",
          value: "fk_operator_department_name",
          width: 70,
        },
        {
          name: "手机",
          value: "phone",
          width: 100,
        },
        {
          name: "客户传真",
          value: "fax",
          width: 100,
        },
        {
          name: "联系人",
          value: "link_man",
          width: 70,
        },
        {
          name: "介绍人",
          value: "introducer_format",
        },
        {
          name: "介绍合同",
          value: "introducer_contract_format",
        },
        {
          name: "销售员",
          value: "fk_sell_employee_name",
          width: 70,
        },
        {
          name: "销售部门",
          value: "fk_sell_department_name",
          width: 70,
        },
        {
          name: "单据日期",
          value: "add_time",
          width: 90,
        },
        {
          name: "客户地址",
          value: "address",
        },
        {
          name: "联系人QQ",
          value: "link_qq",
          width: 110,
        },
        {
          name: "软件序列号",
          value: "software_no",
          width: 100,
        },
      ],
    };
  },

  mounted() {
    this.$store.dispatch("getEmployee").then((res) => {
      this.employeeList = res;
    });
    this.getList();
  },
  methods: {
   async getList(f = false) {
     this.isShowDetail=false;
     await this.$nextTick(()=>{})
      this.loading = true;
      let param = Object.assign(
        this.formSearch,
        this.$refs.pagination.obtain()
      );
      if (f) param.pageNum = 1;
      API.unImplementContractList(param)
        .then((res) => {
          this.selectRecords=[];
          this.tableData = res.data;
          this.$refs.pagination.setTotal(res.totalCount);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    canUse() {
      if (!this.selectRecords.length) {
        this.warning("请先勾选1条数据");
        return false;
      }
      if (this.selectRecords.length > 1) {
        this.warning("请只勾选1条数据");
        return false;
      }

    

      return true;
    },
    confirm() {
      
      
      if(this.canUse() ){
        if (!this.selectRecords[0].implement_id) {
          this.warning("该条数据没有实施人，请先更改实施人");
          return false;
        }
        this.customizeConfirm("是否确认实施？", (value) => {
          this.success(value);
          let data = this.selectRecords[0];
          let params = {
            implement_state: 1,
            customer_contract_id: data.customer_contract_id,
            implement_id: data.implement_id,
          };

          let apiName=data.work_state===3?'setImplementCodeContract':'setImplementContract'
          API[apiName](params).then(() => {
            this.success("保存成功");
            this.getList();
          });


          // setImplementCodeContract
        });
      }
        
    },
    // 更换实施人
    replacement() {
  
      if(this.canUse()){
        let params={}
        params.customer_contract_id=this.selectRecords[0].customer_contract_id;
        params.implement_id=this.selectRecords[0].implement_id;
        this.$refs.dialog.Show(params);
      }
       
    },
  

    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
  },

  components: {
    Dialog,
    Pagination,
    TableView,
    MyDate,
  
  },
  computed: {
    ...mapGetters(["in_position","sale_type","customer_contract_audit"]),
  },
};
