// import { mapMutations } from "vuex";
// import { mapGetters } from "vuex";
import StatisticBar from "@/views/trade_erp/backstage/components/statisticBar/index.vue";
import ButtonGroup from "@/views/trade_erp/backstage/components/buttonGroup/index.vue";
import headForm from "./components/headForm/index.vue";
import MainTable from "./components/mainTable/index.vue";
// import 这里填你需要的api和地址 from "@/api/trade_erp/financial/bankTransfer/bankTransferApi";
// import commonApi from "@/api/trade_erp/commonApi/index.js";

export default {
  data() {
    return {
      isEdit: false,
      isShowHeadForm:true,
      djType: "FYBX",
      loading: false,
      remarks: "",
      //表尾合计
      statisticBar: [
        {
          name: "金额",
          amount: "0.00",
          field: "bfCalje"
        }
      ]
    };
  },
  activated() {},
  methods: {
    addRow() {
      this.$refs.mainTable.addRow();
    },
    insertRow() {
      this.$refs.mainTable.insertRow();
    },
    deleteRow() {
      this.$refs.mainTable.deleteRow();
    },
    //新增
    init() {},
    //修改
    modify() {},
    //删除
    delOrder() {},
    //上下单
    getUpDown() {},
    //获取详情
    getDetail() {},
    //查询
    select() {},
    save() {},
    hiddenHeadForm() {
      this.isShowHeadForm = !this.isShowHeadForm;
    },
    showHeadForm() {
      this.isShowHeadForm = true;
    },
  },
  components: {
    ButtonGroup,
    headForm,
    MainTable,
    StatisticBar
  }
};
