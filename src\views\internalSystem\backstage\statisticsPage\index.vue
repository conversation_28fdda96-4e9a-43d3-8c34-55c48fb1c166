<template>
  <div :class="isAudit === false ? 'container' : 'view-box'">
    <!-- <el-button @click="getList"> 查询</el-button> -->
    <el-row class="head" :gutter="10" v-show="!isAudit">
      <el-col :span="6">
        <div class="box headBox">
          <HeadBox
            title="本月合同金额"
            :number="businessData.contractAmountMon"
            iconColor="#34CB89"
            icon="home-head-2"
            routerName="contract"
            :params="headBoxParams"
          />
        </div>
      </el-col>
      <el-col :span="6">
        <div class="box headBox">
          <HeadBox
            title="本月收款金额"
            :number="businessData.receivableAmountMon"
            iconColor="#F05050"
            hoverColor="#F05050"
            icon="home-head-1"
            routerName="receivables"
            :params="headBoxParams"
          /></div
      ></el-col>
      <el-col :span="6">
        <div class="box headBox">
          <HeadBox
            title="本月开票金额"
            :number="businessData.openTicketAmountMon"
            iconColor="#FEB533"
            hoverColor="#FEB533"
            routerName="openTicket"
            :params="headBoxParams"
          /></div
      ></el-col>
      <!-- <el-col :span="6">
        <div class="box headBox">
          <HeadBox
            title="本月出库金额"
            :number="businessData.outboundAmountMon"
            iconColor="#409EFF"
            hoverColor="#409EFF"
            icon="home-head-1"
            routerName="outbound"
            :params="headBoxParams"
          /></div
      ></el-col> -->
    </el-row>
    <el-row class="mt5 center" :gutter="10" v-show="!isAudit">
      <el-col :span="18">
        <div class="centerBox">
          <div class="centerBoxByBottom mt5" style="margin-bottom: 8px">
            <div class="box item">
              <div class="title">
                <div>最近需要关注客户</div>
                <el-form
                  :inline="true"
                  :model="formSearch"
                  style="margin-bottom: -20px;"
                >

                <el-form-item label="到期时间">
                  <MyDate
                    v-model="formSearch.startTime"
                    hint="开始时间"
                    style="width: 130px"
                    @changeTime="changeTime()"
                  ></MyDate>
                </el-form-item>
                <el-form-item label="">
                  <my-date
                    v-model="formSearch.endTime"
                    hint="结束时间"
                    style="width: 130px"
                    @changeTime="changeTime()"
                  ></my-date>
                </el-form-item>
                  <el-form-item label="销售员">
                    <el-select
                      v-model="formSearch.fk_sell_employee_id"
                      @change="changeValue(formSearch.fk_sell_employee_id)"
                      placeholder="请选择销售员"
                      filterable
                      clearable
                      style="width: 160px"
                      :disabled="!['总经理'].includes(userInfo.role_name) === true"
                    >
                      <el-option
                        v-for="item in employeeList"
                        :key="item.employeeId"
                        :label="item.employee_number + '-' + item.employee_name"
                        :value="item.employeeId"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-form>
              </div>
              <div class="mt5 flex1 flexD">
                <ShowTable
                  :showIndex="true"
                  :tableHeader="bottomHeader"
                  :tableData="allCustomer"
                  :cell-style="cellStyleFn"
                  :header-cell-style="headerRowStyle"
                  v-loading="loading"
                >
                  <el-table-column label="操作" width="90">
                    <template slot-scope="scope">
                      <el-button
                        @click="handleClick1(scope.row)"
                        type="text"
                        size="medium"
                        >续费</el-button
                      >
                      <el-button
                        :disabled="!scope.row.email"
                        @click="handleClick2(scope.row, '01')"
                        type="text"
                        size="medium"
                        >催缴</el-button
                      >
                    </template>
                  </el-table-column>
                </ShowTable>
              </div>
            </div>
            <!-- <div class="box ml10 item scroll-container">
              <div class="title">已过期客户</div>
              <div class="mt5 flex1 flexD">
                <ShowTable
                  :tableHeader="bottomHeader"
                  :tableData="expired"
                  :cell-style="cellStyleFn"
                  :header-cell-style="headerRowStyle"
                  v-loading="loading"
                >
                  <el-table-column label="操作" width='100'>
                    <template slot-scope="scope">
                      <el-button
                        @click="handleClick1(scope.row)"
                        type="text"
                        size="small"
                        >续费</el-button
                      >
                      <el-button
                        :disabled="!scope.row.email"
                        @click="handleClick2(scope.row, '01')"
                        type="text"
                        size="small"
                        >催缴</el-button
                      >
                    </template>
                  </el-table-column>
                </ShowTable>
              </div>
            </div> -->
          </div>
          <!-- <div class="centerBoxByTop box">
           <div class="headRow">
              <p class="title">
                待办任务总数：{{ auditNum }}条 --
                (暂时禁用，请去查看更多详情处理)
              </p>
              <div class="iconGroup">
                <span @click="getAuditCenter()"
                  >刷新
                  <i class="el-icon-refresh" :class="{ active: loading }"></i
                ></span>
                <span class="ml20" @click="auditList()"
                  >查看更多详情<i class="el-icon-caret-right"></i
                ></span>
              </div>
            </div>
          <div class="mt5 flex1 flexD">
              <ShowTable
                :tableHeader="tableHeader"
                :tableData="tableData"
                :cell-style="cellStyleFn"
                :header-cell-style="headerRowStyle"
                v-loading="loading"
              >
                <el-table-column label="操作" width="100">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="primary"
                      disabled
                      @click="dealTask(scope.row)"
                      >审核</el-button
                    >
                  </template>
                </el-table-column>
              </ShowTable>
            </div>
          </div> -->
        </div></el-col
      >
      <el-col :span="6" >
        <div class="centerBox">
          <div
            class="centerBoxByBottom mt5 item box"
            style="padding: 0px 10px 0px 10px"
          >
            <div class="headRow" style="margin-top: 14px">
              <!-- class="title" -->
              <span style="color: #000; font-weight: bold">
                待办任务总数：{{ auditNum }}条
              </span>
              <!-- <div class="iconGroup"> -->
              <!-- <span @click="getAuditCenter()"
                >刷新
                <i class="el-icon-refresh" :class="{ active: loading }"></i
              ></span> -->
              <span
                @click="auditList()"
                style="display: block; float: right; cursor: pointer"
                >跳转审核中心<i class="el-icon-caret-right"></i
              ></span>
              <!-- </div> -->
            </div>
            <div class="mt5 flex1 flexD">
              <ShowTable
                :showIndex="false"
                :tableHeader="tableHeader"
                :tableData="tableData"
                :cell-style="cellStyleFn"
                :header-cell-style="headerRowStyle"
                v-loading="loading"
              >
                <!-- <el-table-column label="操作" width="100">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="primary"
                      disabled
                      @click="dealTask(scope.row)"
                      >审核</el-button
                    >
                  </template>
                </el-table-column> -->
              </ShowTable>
            </div>
          </div>
          <div
            class="centerBoxByBottom mt5 item box"
            style="padding: 0px 10px 0px 10px"
          >
            <div class="headRow" style="margin-top: 14px">
              <span style="color: #000; font-weight: bold">
                即将转入公海客户：{{ tableData2.length || 0 }}条
              </span>
            </div>
            <div class="mt5 flex1 flexD">
              <ShowTable
                :showIndex="false"
                :tableHeader="tableHeader2"
                :tableData="tableData2"
                :cell-style="cellStyleFn"
                :header-cell-style="headerRowStyle"
                v-loading="loading"
              >
              </ShowTable>
            </div>
          </div>
        </div>
        </el-col>

    </el-row>

    <!-- 客户意向单审核 -->
    <!-- <intention-audit ref="intentionAudit" @selectData="getList" /> -->
    <!-- 客户回访单审核 -->
    <!-- <visiting-audit ref="visitingAudit" @selectData="getList" /> -->
    <!-- 客户修改单审核 -->
    <!-- <update-audit ref="updateAudit" @selectData="getList" /> -->
    <!-- 客户实施单审核 -->
    <!-- <imple-audit ref="impleAudit" @selectData="getList" /> -->
    <!-- 文案审核 -->
    <!-- <article-audit ref="articleAudit" @selectData="getList" /> -->
    <!-- 销售报价单审核 -->
    <!-- <quotation-audit ref="quotationAudit" @selectData="getList" /> -->
    <!-- 销售合同单审核 -->
    <!-- <contract-audit ref="contractAudit" @selectData="getList" /> -->
    <!-- 销售开票单审核 -->
    <!-- <ticket-audit ref="ticketAudit" @selectData="getList" /> -->
    <!-- 其他收入单审核 -->
    <!-- <income-audit ref="incomeAudit" @selectData="getList" /> -->
    <!-- 发票进项单审核 -->
    <!-- <invoice-income-audit ref="invoiceIncomeAudit" @selectData="getList" /> -->
    <!-- 费用报销单审核 -->
    <!-- <invoice-audit ref="invoiceAudit" @selectData="getList" /> -->
    <!-- 新版合同单审核 -->
    <!-- <new-contract-audit ref="newContractAudit" @selectData="getList" /> -->
  </div>
</template>

<script src="./index.js"></script>
<style lang="scss" scoped>
.el-table--small {
  font-size: 14px;
}
@import "@/assets/css/element/font-color.scss";
.flex1 {
  flex: 1;
}
.flexD {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.cusr {
  cursor: pointer;
}
.container {
  // background-color: #f5f6fa;
  display: flex;
  flex-direction: column;
  padding: 2px;
  .head {
    flex: 0 0 100px;
    margin-right: 0 !important;
  }
  .center {
    flex: 1;
    margin-right: 0 !important;
    overflow: hidden;
  }
  /deep/.el-col {
    height: 100%;
  }
  .box {
    background-color: #fff;
    height: 200px;
    height: 100%;
    width: 100%;
    overflow: auto;
    box-shadow: 0px 0px 3px #ccc;
    border-radius: 5px;
  }
  .headBox {
    height: 100px;
  }
  .centerBox {
    height: 100%;
    display: flex;
    flex-direction: column;
    .centerBoxByTop {
      flex: 2;
      padding: 20px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      flex-shrink: 0;
      margin-top: 10px;
      overflow: scroll;
      .headRow {
        display: flex;
        justify-content: space-between;
        .title {
          color: #000;
          font-weight: bold;
        }
        .iconGroup {
          font-size: 14px;
          cursor: pointer;
          user-select: none;

          .active {
            animation: spin 1s linear infinite;
          }
          @keyframes spin {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
        }
      }
    }
    @media screen and (max-width: 1300px) {
      .centerBoxByTop {
        padding: 5px;
      }
    }

    .centerBoxByBottom {
      flex: 2;
      display: flex;
      width: 100%;
      overflow: hidden;
      box-shadow: 0px 0px 3px #ccc;
    }
    @media screen and (max-height: 1000px) {
      .centerBoxByBottom {
        flex: 3;
      }
    }
  }
  .item {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px;
    box-sizing: border-box;
    background-color: #fff;
    .title {
      color: #409eff;
      display: flex;
      justify-content: space-between;
    }
    .itemContent {
      flex: 1;
      margin-top: 10px;
    }
    .customerList {
      display: flex;
      flex-wrap: wrap;

      .customerItem {
        width: calc(50% - 10px);
        height: 50px;

        margin-top: 5px;
        margin: 0 5px;
        display: flex;
        align-items: center;
        &:nth-child(1) {
          .iconBox {
            background-color: #409eff;
            .iconBoxRadio {
              color: #409eff;
            }
          }
        }
        &:nth-child(2) {
          .iconBox {
            background-color: #feb533;
            .iconBoxRadio {
              color: #feb533;
            }
          }
        }
        &:nth-child(3) {
          .iconBox {
            background-color: #ef4e4e;
            .iconBoxRadio {
              color: #ef4e4e;
            }
          }
        }
        &:nth-child(4) {
          .iconBox {
            background-color: #33cb88;
            .iconBoxRadio {
              color: #33cb88;
            }
          }
        }
        &:nth-child(5) {
          .iconBox {
            background-color: #775ee4;
            .iconBoxRadio {
              color: #775ee4;
            }
          }
        }
        &:nth-child(6) {
          .iconBox {
            background-color: #fe8a03;
            .iconBoxRadio {
              color: #fe8a03;
            }
          }
        }

        .iconBox {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: cornflowerblue;
          display: flex;
          align-items: center;
          justify-content: center;

          .iconBoxRadio {
            width: 30px;
            height: 30px;
            text-align: center;
            line-height: 30px;
            background-color: #fff;
            border-radius: 50%;
            font-size: 16px;
            font-weight: bold;
            color: cornflowerblue;
          }
        }
        .customerItemBody {
          font-size: 12px;
          margin-left: 8px;
        }
      }

      // @media screen and (min-height: 900px) {
      //   .customerItem {
      //     height: 80px;
      //     .iconBox {
      //       height: 60px;
      //       width: 60px;
      //       .iconBoxRadio {
      //         width: 50px;
      //         height: 50px;
      //         line-height: 50px;
      //         font-size: 20px;
      //       }
      //     }
      //     .customerItemBody {
      //       font-size: 16px;
      //     }
      //   }
      // }
      @media screen and (max-width: 1300px) {
        .customerItem {
          width: calc(100% - 10px);
          padding-left: 20px;
        }
      }
    }
  }
  .centerRightBox {
    //   height: ;
    width: 100%;
    height: 100%;
    background-color: #fff;
  }

  .bg {
    background: white !important;
  }
}
</style>
