import Axios from "@/api";
import environment from "@/api/environment";

export default {
  // 查询
  query: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/query`, params)
  },
  queryToAudit: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/queryToAudit`, params)
  },
  // 查询明细
  detailList: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/detailList`, params)
  },
  // 查询合同总金额
  queryAll: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/queryAll`, params)
  },
  // 新增
  add: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/add`, params)
  },
  // 删除
  remove: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/remove`, params)
  },
  // 编辑
  update: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/update`, params)
  },
  // 获取单条信息
  getInfo: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/getInfo`, params)
  },
  // 审核
  updateAudit: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/updateAudit`, params)
  },
  // 合同模板列表
  templateList: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/templateList`, params)
  },
  // 上传合同模板
  addTemplate: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/addTemplate`, params)
  },
  // 修改合同模板
  updateTemplate: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/updateTemplate`, params)
  },
  // 删除合同模板
  delTemplate: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/delTemplate`, params)
  },
  // 获取单条模板信息
  getTemplate: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/getTemplate`, params)
  },

  // 统计维护费
  // getAllMaintainCost: params => {
  //   return Axios.post(`${environment.internalSystemAPI}customerContract/getAllMaintainCost`, params)
  // },
  // 查找该客户所有的租用合同
  // getAllRent: params => {
  //   return Axios.post(`${environment.internalSystemAPI}customerContract/getAllRent`, params)
  // },
  // 查找首次销售的配置
  getFirstContractConfiguration: params => {
    return Axios.post(`${environment.internalSystemAPI}customerContract/getFirstContractConfiguration`, params)
  },
  // 查出主产品
  getMainProduct: params => {
    return Axios.post(`${environment.internalSystemAPI}customer/getMainProduct`, params)
  },
  // 查询是否已经有合同
  checkDealCustomer: params => {
    return Axios.post(`${environment.internalSystemAPI}customer/checkDealCustomer`, params)
  },
  // 查询客户产品表
  getCustomerBrandByCustomer: params => {
    return Axios.post(`${environment.internalSystemAPI}customer/getCustomerBrandByCustomer`, params)
  }



};