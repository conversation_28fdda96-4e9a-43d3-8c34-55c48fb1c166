<template>
  <div class="congested">
    <div class="p5 congested" :class="{'view-box':!isHead}">
      <keep-alive :include="routeList">
        <router-view class="congested flexColumn"></router-view>
      </keep-alive>
      <!-- <router-view class="congested flexColumn" v-if="!$route.meta.keepAlive" /> -->
      <!-- <router-view class="congested flexColumn" /> -->
    </div>
  </div>
</template>
<script>
  import {
    mapGetters
  } from "vuex";
  export default {
    computed: {
      ...mapGetters([
        "routeList"
      ]),
      isHead(){
        return this.$route.name==='statisticsPage'
      }
    }
  }
</script>
<style scoped lang="scss">
  .view-box {
    padding: 5px;
    box-sizing: border-box;
    background-color: white;
    overflow: hidden;
  }
  .p5{
    padding: 5px;
  }
</style>