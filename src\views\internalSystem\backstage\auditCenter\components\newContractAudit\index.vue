<template>
  <div class="body-p10" style="overflow-y:auto;overflow-x: hidden;" v-if="dialogVisible">
    <div>
      <el-button @click="dialogCancel">返 回</el-button>
      <el-button type="primary" @click="openAudit">审 核</el-button>
    </div>
    <el-form :model="ruleForm" ref="ruleForm" label-width="160px" class="mt10">
        <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="客户名称" prop="customer_name">
            <el-input
              disabled
              v-model="ruleForm.customer_name"
              placeholder="请点击选择客户"
           
              readonly
              clearable
            ></el-input>  
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人" prop="link_man">
            <el-input
              disabled
              v-model="ruleForm.link_man"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人QQ" prop="link_qq">
            <el-input
              disabled
              v-model="ruleForm.link_qq"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销货单位" prop="sales_unit_id_format">
            <el-input
              disabled
              v-model="ruleForm.sales_unit_id_format"
             
              placeholder="请点击选择销货单位"
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所属部门" prop="fk_operator_department_name">
            <el-input
              v-model="ruleForm.fk_operator_department_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="制单员" prop="add_user_name">
            <el-input
              v-model="ruleForm.add_user_name"
              disabled
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="付款方式" prop="pay_type">
            <el-select
              disabled
              v-model="ruleForm.pay_type"
              placeholder="请选择付款方式"
              filterable
              clearable
            >
              <el-option
                v-for="item in contract_pay_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="培训方式" prop="train_type">
            <el-select
              disabled
              v-model="ruleForm.train_type"
              placeholder="请选择培训方式"
              filterable
              clearable
            >
              <el-option
                v-for="item in contract_train_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售类型" prop="sell_type">
            <el-select
              disabled
              v-model="ruleForm.sell_type"
              placeholder="请选择销售类型"
              filterable
              clearable

            >
              <el-option
                v-for="item in sell_type"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="所在省份" prop="province">
          <el-input disabled v-model="ruleForm.province_name" clearable></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="所在城市" prop="city">
               <el-input disabled v-model="ruleForm.city_name" clearable></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="详细地址" prop="address">
            <el-input
              disabled
              v-model="ruleForm.address"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="介绍人" prop="introducer_format">
            <el-input
              disabled
              v-model="ruleForm.introducer_format"
              placeholder="请点击选择介绍人"
        
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="介绍合同" prop="introducer_contract_format">
            <el-input
              disabled
              v-model="ruleForm.introducer_contract_format"
              placeholder="请点击选择介绍合同"
          
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="续费合同" prop="c">
            <el-input
              disabled
              v-model="ruleForm.renewal_contract_format"
              placeholder="请点击选择续费合同"
           
              readonly
              clearable
            ></el-input>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="8">
          <el-form-item label="销售员" prop="fk_sell_employee_name">
            <el-input v-model="ruleForm.fk_sell_employee_name" disabled clearable></el-input>
          </el-form-item>
        </el-col>
      

        <el-col :span="8">
          <el-form-item label="销售员部门" prop="fk_sell_department_name">
            <el-input v-model="ruleForm.fk_sell_department_name" disabled clearable></el-input>
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="客户传真" prop="fax">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.fax"  clearable></el-input>
          </el-form-item>
        </el-col>
    
      
        <el-col :span="8">
          <el-form-item label="手机" prop="phone">
            <el-input :disabled="!isEdit||!isOwn" v-model="ruleForm.phone"  clearable></el-input>
          </el-form-item>
        </el-col> 
        -->

        <el-col :span="8">
          <el-form-item label="是否开票" prop="is_open_bill">
            <el-select
              disabled
              v-model="ruleForm.is_open_bill"
              placeholder="选择是否开票"
              filterable
              clearable
            >
              <el-option
                v-for="item in is_open_bill"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实施人员" prop="implement_id">
            <el-select
              disabled
              v-model="ruleForm.implement_id"
              placeholder="请选择实施人员"
              filterable
              clearable
            >
              <el-option
                v-for="item in employeeList"
                :key="item.employeeId"
                :label="item.employee_number + '-' + item.employee_name"
                :value="item.employeeId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="培训人员" prop="train_id">
            <el-select
              disabled
              v-model="ruleForm.train_id"
              placeholder="请选择培训人员"
              filterable
              clearable
            >
              <el-option
                v-for="item in employeeList"
                :key="item.employeeId"
                :label="item.employee_number + '-' + item.employee_name"
                :value="item.employeeId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="单据备注" prop="remark">
            <el-input
              disabled
              v-model="ruleForm.remark"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="推荐员工" prop="fk_recommend_employee_id">
            <el-select
              :disabled="!isEdit||!isOwn"
              v-model="ruleForm.fk_recommend_employee_id"
              placeholder="请选择推荐员工"
              filterable
              clearable
            >
              <el-option
                v-for="item in employeeList"
                :key="item.employeeId"
                :label="item.employee_name"
                :value="item.employeeId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->

        <el-col :span="8" v-if="ruleForm.customer_contract_id">
          <el-form-item label="审核状态" prop="audit_state_name">
            <el-input
              disabled
              v-model="ruleForm.audit_state_name"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="ruleForm.customer_contract_id">
          <el-form-item label="审核备注" prop="audit_remark">
            <el-input
              disabled
              v-model="ruleForm.audit_remark"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-tabs v-model="activeName">
        <el-tab-pane label="合同产品" name="first">
          <table-custom ref="proTableCustom" :obj="proObj" :tableCol="proTableCol" :isDel="false"/>
        </el-tab-pane>
        <el-tab-pane label="功能模块" name="second">
          <table-custom ref="moduleTableCustom" :obj="moduleObj" :tableCol="moduleTableCol" :isDel="false"/>
        </el-tab-pane>
      </el-tabs>

    </el-form>
    <el-dialog title="审核" :visible.sync="dialogVisibleAudit" append-to-body @close="dialogCancelAudit" width="660px"
      :close-on-click-modal="false" :destroy-on-close="true" v-dialogDrag>
      <el-form :model="auditForm" :rules="rules" ref="auditForm" label-width="100px">
        <!-- <el-form-item label="审核状态" prop="auditState">
          <el-select v-model="auditForm.auditState" placeholder="请选择审核状态" filterable clearable>
            <template v-for="item in contract_auditStateList">
              <el-option v-show="item.id!=ruleForm.audit_state&&item.id!=4" :key="item.id"
                :label="item.label" :value="item.id">
              </el-option>
            </template>
          </el-select>
        </el-form-item> -->
        <el-form-item label="审核状态" prop="auditState">
          <el-radio-group v-model="auditForm.auditState">
            <template v-for="item in contract_auditStateList">
              <el-radio
              v-show="item.id!=ruleForm.audit_state&&item.id!=4"
                :key="item.id"
                :label="item.value"
                :value="item.value"
                border
                style="height: 30px;"
                ><span style="font-size:16px;">{{ item.label }}</span></el-radio
              >
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" clearable></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('auditForm')" :loading="loading">保 存</el-button>
        <el-button @click="dialogCancelAudit">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script src="./index.js">

</script>

<style lang="scss" scoped>
	@import "@/assets/css/element/font-color.scss";

</style>