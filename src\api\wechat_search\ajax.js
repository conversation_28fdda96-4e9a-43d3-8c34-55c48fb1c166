import axios from "axios";
import { Toast } from 'vant';
import {errorLogSendEmail} from "../errorLogSendEmail.js";

const service = axios.create({
	timeout: 60 * 6000, // 响应时间
	withCredentials: true
});

service.interceptors.request.use(config => {
	config.headers["Content-Type"] =
			config.data && config.data.requestType == "file"
					? "multipart/form-data"
					: "application/json"
	if (!config.data) config.data = {}
	return config
});

service.interceptors.response.use(
		res => {
			if (res.status === 200) {
				res = res.data;
				switch (res.code) {
					case 0:
						res.msg
								? Toast(res.msg)
								: Toast('接口异常,请联系管理员。')
						return Promise.reject(res)
					case 1:
						if (!res.data) {
							Toast(res.msg)
						}
						if (res.hasOwnProperty('totalCount')) return res
						
						return res.data;
					default:
						return res
				}
			} else {
				Toast('接口返回识别码异常,请联系管理员')
				errorLogSendEmail(res)
				return Promise.reject(res)
			}
		},
		err => {
			// 判断请求异常信息中是否含有超时timeout字符串
			if (err.response) errorLogSendEmail(err.response)
			
			Toast('服务器开小差了,请稍后再试。')
			return Promise.reject(err)
		}
);

export default service
