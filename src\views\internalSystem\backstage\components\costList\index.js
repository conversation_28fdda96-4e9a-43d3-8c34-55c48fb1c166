import API from '@/api/internalSystem/basicManage/costDetails'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
export default {
  name: "costList",
  components: {
    TableView,
    Pagination
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      selectRecords: [],
      tableData: [],
      tableList: [{
          name: "费用明细编码",
          value: "cost_detail_no"
        },
        {
          name: "费用明细名称",
          value: "cost_detail_name"
        },
        {
          name: "费用项目编码",
          value: "project_no"
        },
        {
          name: "费用项目名称",
          value: "project_name"
        },
        {
          name: "费用明细说明",
          value: "cost_description"
        }
      ]
    };
  },
  props: {
    dialogTitle: {
      type: String,
      default: "费用明细列表"
    }
  },
  methods: {
    Show() {
      this.dialogVisible = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          this.getList();
        });
      // }, 300);
    },
    getList() {
      this.loading = true;
      let param = Object.assign(this.$refs.cost_pagination.obtain());
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.cost_pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    //提交
    submitForm() {
      if (this.selectRecords.length != 1)
        return this.error("请选择一条记录");
      this.$emit("getInfo", this.selectRecords[0]);
      this.dialogCancel();
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.selectRecords = [];
    },
    getSelectRecords(selectRecords = []) {
      this.selectRecords = selectRecords;
    },
  }
};