<template>
  <div>
    <editor id="tinymce" v-model="tinyValue" :init="init"></editor>
  </div>
</template>

<script>
import tinymce from "tinymce";
import "tinymce/skins/ui/oxide/skin.css";
import API from "@/api/internalSystem/common";
import oss from "@/api/aliyun/oss";
import "tinymce/themes/silver/theme";
import Editor from "@tinymce/tinymce-vue";
import "tinymce/icons/default/icons.min.js";
import zhCN from "@/assets/tinymce/langs/zh_CN";
import "tinymce/plugins/image"; // 插入上传图片插件
import "tinymce/plugins/table"; // 插入表格插件
import "tinymce/plugins/lists"; // 列表插件
import "tinymce/plugins/wordcount"; // 字数统计插件
import "tinymce/plugins/paste"; // 粘贴图片
import "tinymce/plugins/code"; // 代码
import "tinymce/plugins/link"; // 超链接
import "tinymce/plugins/hr"; // 分割线
import "tinymce/plugins/preview"; // 预览
import "tinymce/plugins/searchreplace"; // 查找替换
export default {
  name: "RichText",
  components: {
    editor: Editor,
  },
  props: {
    value: {
      type: String,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    height: {
      type: Number,
      default: 600,
    },
  },
  computed: {
    tinyValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  data() {
    return {
      init: {
        language_url: zhCN, // 语言包的路径
        language: "zh_CN", // 语言
        skin_url: "../src/assets/tinymce/skins/ui/oxide", // skin路径，这里不起效，手动引入css
        height: this.height, // 编辑器高度
        branding: false, // 是否禁用“Powered by TinyMCE”
        menubar: true, // 顶部菜单栏显示
        paste_data_images: true,
        file_picker_types: "file",
        // file_browser_callback_types: 'file image media',  // type of file
        plugins:
          "lists image table wordcount code link hr preview searchreplace",
        toolbar:
          "undo redo | formatselect | bold italic hr forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | lists image table | code searchreplace link preview | removeformat ",
        images_upload_handler: (blobInfo, succFun, failFun) => {
          let file = blobInfo.blob(); // 转化为易于理解的file对象
          // 文件验证 type size
          if (!this.checkFile(file, [], 2)) return;
          oss
            .uploadFile(file)
            .then((res) => {
              succFun(res.url);
            })
            .catch((err) => {
              failFun(err.message);
            });
        },
        file_picker_callback: function (callback, value, meta) {

          // // 要先模拟出一个input用于上传本地文件
          var input = document.createElement("input");
          input.setAttribute("type", "file");
          // 你可以给input加accept属性来限制上传的文件类型
          // 例如：input.setAttribute('accept', '.jpg,.png')
          input.setAttribute("accept", ".doc,.docx,.ppt,.pptx,.pdf,.xlsx");
          input.click();
          input.onchange = function () {
            var file = this.files[0];
            var reader = new FileReader();
            reader.onload = function () {
              var id = "blobid" + new Date().getTime();
              var blobCache =
                window.tinymce.activeEditor.editorUpload.blobCache;
              var base64 = reader.result.split(",")[1];
              var blobInfo = blobCache.create(id, file, base64);

              blobCache.add(blobInfo);

              oss
                .uploadFile(file)
                .then((res) => {
                  callback(res.url, {
                    text: file.name,
                    title: file.name,
                  });
                })
                .catch((err) => {
                });
            };
            reader.readAsDataURL(file);
          };
        },
      },
    };
  },
  mounted() {
    if (this.disabled) {
      tinymce.activeEditor.getBody().setAttribute("contenteditable", false);
    }
  },
  methods: {},
};
</script>

<style scoped  lang="scss">
/*在el-dialog中弹窗会被覆盖，加大z-index解决*/
/*    .tox-silver-sink {
     z-index: 99999 !important;
   }  */
.tox-silver-sink {
  z-index: 99999 !important;
}

/*@import "~tinymce/skins/ui/oxide/skin.min.css"*/
/*@import "~tinymce/skins/ui/oxide/content.min.css"*/
/*@import "~tinymce/skins/content/default/content.css"*/
</style>
