import API from "@/api/internalSystem/salesManage/contract";
import quoAPI from "@/api/internalSystem/salesManage/quotation";
import comAPI from "@/api/internalSystem/common/index.js";
import paramAPI from "@/api/internalSystem/basicManage/parameter";
import brandAPI from "@/api/internalSystem/basicManage/brand";
import CustomerList from "@/views/internalSystem/backstage/components/customerList/index.vue";
import SalesUnitList from "@/views/internalSystem/backstage/components/salesUnitList/index.vue";
import ContractList from "@/views/internalSystem/backstage/components/contractList/index.vue";
import QuotationList from "../quotationList/index.vue";
import FileList from "@/views/internalSystem/backstage/components/fileList/index.vue";
import TableCustom from "@/views/internalSystem/backstage/components/tableCustom/index2.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import { dateFormat } from "@/common/internalSystem/common.js";
import { mapGetters } from "vuex";

export default {
  name: "addContract",
  components: {
    CustomerList,
    SalesUnitList,
    ContractList,
    QuotationList,
    FileList,
    TableCustom,
    MyDate,
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        customer_name: "",
        fk_customer_id: "",
        train_type: 2,
        pay_type: 1,
        sell_type: "",
        sales_unit_id: "",
        link_man: "",
        fk_sell_employee_id: "",
        fk_sell_department_id: "",
        phone: "",
        introducer_format: "",
        introducer: "",
        province: "",
        city: "",
        remark: "",
        address: "",
        fax: "",
        link_qq: "",
        sales_unit_id_format: "",
        introducer_contract_format: "",
        introducer_contract_id: "",
        fk_recommend_employee_id: "",
        renewal_contract_format: "",
        renewal_contract_id: "",
      },
      rules: {
        customer_name: [
          {
            required: true,
            message: "请选择客户",
          },
        ],
        sales_unit_id_format: [
          {
            required: true,
            message: "请选择销货单位",
          },
        ],
        train_type: [
          {
            required: true,
            message: "请选择培训方式",
          },
        ],
        pay_type: [
          {
            required: true,
            message: "请选择付款方式",
            trigger: "change",
          },
        ],
        sell_type: [
          {
            required: true,
            message: "请选择销售类型",
            trigger: "change",
          },
        ],
        link_man: [
          {
            required: true,
            message: "请输入联系人",
          },
        ],
        phone: [
          {
            required: true,
            message: "请输入联系电话",
          },
        ],
        sales_unit_id: [
          {
            required: true,
            message: "请选择销货单位",
          },
        ],
        link_qq: [
          {
            required: true,
            message: "请输入联系人QQ",
          },
        ],
        address: [
          {
            required: true,
            message: "请输入详细地址",
          },
        ],
      },
      provinceList: [], //省
      cityList: [], //市
      softwareVersionList: [], //软件版本
      measurementUnitList: [], //计量单位
      paramsList: [],
      loading: false,
      activeName: "first",
      proList: [], //产品列表
      moduleList: [], //模块列表
      quotationRateList: [], //报价税率
      prepaymentRatioList: [], //预付款比例
      yearsFeeList: [], //年维护费比例
      employeeList: [],
      proTableCol: [
        {
          label: "id",
          prop: "quotation_id",
          isHide: true,
        },
        {
          label: "产品服务",
          prop: "fk_brand_id",
          need: true,
          width: 160,
        },
        {
          label: "软件版本",
          prop: "software_version",
          need: true,
          width: 160,
        },
        {
          label: "计量单位",
          prop: "measurement_unit",
          need: true,
          width: 160,
        },
        {
          label: "合同数量",
          prop: "contract_count",
          width: 160,
        },
        {
          label: "合同金额",
          prop: "contract_amount",
          need: true,
          width: 160,
        },
        {
          label: "发票税率",
          prop: "invoice_tax_rate",
          need: true,
          width: 160,
        },
        // {
        //   label: "预付款比例%",
        //   prop: "prepayment_rate",
        //   need: true,
        //   width: 160,
        // },
        {
          label: "年维护费比例%",
          prop: "year_maintain_cost",
          need: true,
          width: 160,
        },
        {
          label: "预付款期限",
          prop: "prepayment_time_limit",
          width: 160,
        },
        {
          label: "维护起始日期",
          prop: "maintain_start_time",
          need: true,
          width: 160,
        },
        {
          label: "新维护结束日期",
          prop: "new_maintain_stop_time",
          need: true,
          width: 160,
        },
        {
          label: "原维护结束日期",
          prop: "original_maintain_stop_time",
          width: 160,
        },
        {
          label: "原有端口数",
          prop: "original_port_count",
          width: 160
        },
        {
          label: "新增端口数",
          prop: "add_port_count",
          width: 160,
        },
        {
          label: "合同备注",
          prop: "remark",
          width: 160,
        },
      ],
      proObj: {},
      moduleTableCol: [
        {
          label: "id",
          prop: "quotation_id",
          isHide: true,
        },
        {
          label: "模块名称",
          prop: "fk_brand_id",
          need: true,
        },
        {
          label: "计量单位",
          prop: "measurement_unit",
          need: true,
        },
        {
          label: "合同金额",
          prop: "contract_amount",
          need: true,
        },
        {
          label: "合同备注",
          prop: "remark",
        },
        {
          label: "出库数量",
          prop: "stock_removal_count",
        },
      ],
      moduleObj: {},
      isEdit: false,
      isOwn: true,
      dialogVisibleTem: false,
      temForm: {
        contract_template_id: "",
      },
      temRules: {
        contract_template_id: [
          {
            required: true,
            message: "请选择合同模板",
            trigger: "change",
          },
        ],
      },
      temList: [],
      command: "",
      temObj: {
        1: ["软件购买合同书", "软件服务协议(首次)"],
        2: ["软件购买合同书(二次开发)"],
        3: ["软件服务协议(维护费<1500)", "软件服务协议(维护费>1500)"],
        4: ["软件购买合同书(增加端口)"],
        5: ["吉勤软件租用合同"],
        6: ["软件购买合同书(软件升级)"],
        7: ["软件购买合同书(二次销售)"],
        8: ["吉勤软件租用合同(金万维)"],
      },
    };
  },
  methods: {
    async Show(data = null) {
      this.proList = [];
      this.moduleList = [];
      this.quotationRateList = [];
      this.prepaymentRatioList = [];
      this.yearsFeeList = [];
      this.dialogVisible = true;
      this.getProvinceList();
      this.activeName = "first";
      this.$store.dispatch("getEmployee").then((res) => {
        this.employeeList = res;
      });
      this.ruleForm.add_user_name = this.userInfo.fullName;
      this.ruleForm.fk_operator_department_name = this.userInfo.department_name;
      this.isEdit = true;
      this.isOwn = true;
      await this.getBrand();
      await this.getParam();
      this.proObjRest();
      this.moduleObj = {
        quotation_id: {
          value: "",
          type: "input",
        },
        fk_brand_id: {
          value: "",
          type: "select",
          option: this.moduleList,
        },
        measurement_unit: {
          value: "",
          type: "select",
          option: this.measurement_unit,
        },
        contract_amount: {
          value: "",
          type: "float",
        },
        remark: {
          value: "",
          type: "input",
        },
        stock_removal_count: {
          value: "",
          type: "number",
        },
      };
      if (data) {
        this.ruleForm = data;
        this.isEdit =this.permissionToCheck('UPDATE_CONTRACT_NEW') && 
          (data.audit_state == 4 || data.audit_state == 2);
        this.isOwn = data.update_user_id === this.userInfo.userId ? true : false;
        this.getCityList();
        API.detailList({
          customer_contract_id: data.customer_contract_id,
        })
          .then((res) => {
            res.data.map((item) => {
              let pro = JSON.parse(JSON.stringify(this.proObj));
              // let module = JSON.parse(JSON.stringify(this.moduleObj));
              for (let v in item) {
                if (item.detail_type == 1) {
                  if (pro[v]) {
                    pro[v].value = item[v];
                    pro[v].disabled = !this.isEdit || !this.isOwn;
                  }
                } else {
                  // if (module[v]) {
                  //   module[v].value = item[v];
                  //   module[v].disabled = !this.isEdit || !this.isOwn;
                  // }
                }
              }
              if (item.detail_type == 1) {
                this.$refs.proTableCustom.add2(pro);
              } else {
                // this.$refs.moduleTableCustom.add2(module);
              }
            });
          })
          .catch(() => {})
          .finally(() => {});
      }
    },
    //提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.save();
        } else {
          return false;
        }
      });
    },
    dialogCancel() {
      this.dialogVisible = false;
      this.resetForm("ruleForm");
      this.clearData();
      this.$emit("selectData");
    },
    async save() {
      let params = this.ruleForm;
      // 8 = 赠送端口
      if (params.sell_type == "8") {
        if (!params.introducer) return this.error("请选择介绍人");
        if (!params.introducer_contract_id) return this.error("请选择介绍合同");
      }
      params.fk_operator_department_id = this.userInfo.department_id;
      let proList = [];
      try {
        proList = await this.$refs.proTableCustom.getData();
      } catch {
        this.activeName = "first";
        return;
      }
      // let moduleList = [];
      // try {
      //   moduleList = await this.$refs.moduleTableCustom.getData();
      // } catch {
      //   this.activeName = "second";
      //   return;
      // }

      let detail = [];
      if (proList.length == 0) return this.error("产品至少有一条数据");
      // if (moduleList.length == 0) return this.error("模块至少有一条数据");
      let quotation_ids = [];
      let f = false;
      let proMoney = 0,
        moduleMoney = 0;
      let unitFlag = true;
      for (let i = 0; i < proList.length; i++) {
        let item = proList[i];
        if (parseInt(item.year_maintain_cost.value) === 0)
          return this.error(`年维护费比例不能为0`);
        if (item.quotation_id.value)
          quotation_ids.push(item.quotation_id.value);
        if (
          (this.ruleForm.sell_type == 4 ||
            this.ruleForm.sell_type == 1 ||
            this.ruleForm.sell_type == 8 ) &&
          !item.add_port_count.value
        ) {

          f = true;
          this.error("请填写新增端口数！");
        }
        if (this.ruleForm.sell_type == "4" || this.ruleForm.sell_type == "8") {
          if (item.measurement_unit.value == "0") {
            unitFlag = false;
            return this.error("计量单位请选择个！");
          }
        }
        proMoney += parseFloat(item.contract_amount.value);
        detail.push({
          fk_brand_id: item.fk_brand_id.value,
          software_version: item.software_version.value,
          measurement_unit: item.measurement_unit.value,
          contract_count: item.contract_count.value,
          contract_amount: item.contract_amount.value,
          invoice_tax_rate: item.invoice_tax_rate.value,
          // prepayment_rate: item.prepayment_rate.value,
          year_maintain_cost: item.year_maintain_cost.value,
          prepayment_time_limit: item.prepayment_time_limit.value,
          maintain_start_time: item.maintain_start_time.value,
          new_maintain_stop_time: item.new_maintain_stop_time.value,
          original_maintain_stop_time: item.original_maintain_stop_time.value,
          original_port_count: item.original_port_count.value,
          add_port_count: item.add_port_count.value,
          remark: item.remark.value,
          detail_type: 1,
        });
      }
      // moduleList.forEach((item) => {
      //   if (this.ruleForm.sell_type == "4" || this.ruleForm.sell_type == "8") {
      //     if (item.measurement_unit.value == "0") {
      //       unitFlag = false;
      //       return this.error("计量单位请选择个！");
      //     }
      //   }
      //   if (item.quotation_id.value)
      //     quotation_ids.push(item.quotation_id.value);
      //   moduleMoney += parseFloat(item.contract_amount.value);
      //   detail.push({
      //     fk_brand_id: item.fk_brand_id.value,
      //     measurement_unit: item.measurement_unit.value,
      //     contract_amount: item.contract_amount.value,
      //     stock_removal_count: item.stock_removal_count.value,
      //     remark: item.remark.value,
      //     detail_type: 2,
      //   });
      // });

      if (f) return this.error("请填写新增端口数！");
      if (!unitFlag) return this.error("计量单位请选择个！");
      if (proMoney != moduleMoney) return this.error("产品与模块的金额不一致");
      quotation_ids = Array.from(new Set(quotation_ids));
      params.quotation_ids = quotation_ids.join(",");
      params.detail = detail;
      let maintain_start_time = new Date(detail[0].maintain_start_time);
      let maintain_stop_time = new Date(detail[0].new_maintain_stop_time);
      detail.forEach((item) => {
        let min = new Date(item.maintain_start_time);
        let max = new Date(item.new_maintain_stop_time);
        if (min < maintain_start_time) {
          maintain_start_time = min;
        }
        if (maintain_stop_time < max) {
          maintain_stop_time = max;
        }
      });
      params.maintain_start_time = dateFormat(
        "yyyy-MM-dd",
        maintain_start_time
      );
      params.maintain_stop_time = dateFormat("yyyy-MM-dd", maintain_stop_time);
      params.audit_state = 4;
      this.loading = true;
      if (params.customer_contract_id) {
        if (this.ruleForm.audit_state != 4 && this.ruleForm.audit_state != 2)
          return this.error("该单据已发出审核，不允许修改！");
        API.update(params)
          .then(() => {
            this.dialogCancel();
            this.$emit("getInfo", params.customer_contract_id);
          })
          .catch(() => {})
          .finally(() => {
            this.loading = false;
          });
      } else {
        API.add(params)
          .then((res) => {
            this.dialogCancel();
            this.$emit("getInfo", res.data);
          })
          .catch(() => {})
          .finally(() => {
            this.loading = false;
          });
      }
    },
    send() {
      this.$confirm("此操作将发出该条记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            customer_contract_id: this.ruleForm.customer_contract_id,
            auditState: 3,
          };
          API.updateAudit(params)
            .then(() => {
              this.dialogCancel();
            })
            .catch(() => {})
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {});
    },
    back() {
      this.$confirm("此操作将回退该条记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            customer_contract_id: this.ruleForm.customer_contract_id,
            auditState: 3,
          };
          API.updateAudit(params)
            .then(() => {
              this.dialogCancel();
            })
            .catch(() => {})
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {});
    },
    addFile() {
      this.$refs.fileList.Show();
    },
    getBrand() {
      return new Promise((resolve, reject) => {
        brandAPI
          .query()
          .then((data) => {
            data.data.forEach((item) => {
              // if (item.brand_classify == 1){
                this.proList.push({
                  label: item.brand_type,
                  value: item.brand_id,
                });
              // }
              // else if (item.brand_classify == 2){
              //   this.moduleList.push({
              //     label: item.brand_type,
              //     value: item.brand_id,
              //   });
              // }
         
            });
            resolve(1);
          })
          .catch(() => {
            reject();
          });
      });
    },
    handleCommand(command) {
      this.command = command;
      this.dialogVisibleTem = true;
      API.templateList()
        .then((data) => {
          let option = [];
          data.data.map((item) => {
            if (
              this.temObj[this.ruleForm.sell_type].some(
                (ele) => ele === item.contract_template_name
              )
            ) {
              option.push(item);
            }
          });
          this.temList = option;
        })
        .catch(() => {});
    },
    submitTem(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let url =
            "/internalSystem/#/print?type=" +
            this.command +
            "&contract_template_id=" +
            this.temForm.contract_template_id +
            "&customer_contract_id=" +
            this.ruleForm.customer_contract_id;
          window.open(url, "_blank");
          this.dialogCancelTem();
          this.temForm.contract_template_id = "";
        } else {
          return false;
        }
      });
    },
    dialogCancelTem() {
      this.dialogVisibleTem = false;
    },
    getParam() {
      paramAPI
        .query()
        .then((data) => {
          data.data.forEach((item) => {
            if (item.parameter_type == 5)
              this.quotationRateList.push({
                label: item.content,
                value: item.content,
              });
            else if (item.parameter_type == 1)
              this.prepaymentRatioList.push({
                label: item.content,
                value: item.content,
              });
            else if (item.parameter_type == 2)
              this.yearsFeeList.push({
                label: item.content,
                value: item.content,
              });
          });
        })
        .catch(() => {});
    },
    // 产品 同步 功能
    // async proSameModule(){
    //   let proList = [];
    //   try {
    //     proList = await this.$refs.proTableCustom.getData();
    //   } catch {
    //     this.activeName = "first";
    //     return;
    //   }
    //   if(proList.length !== 0){
    //     // this.$refs.moduleTableCustom.proSameModule(proList)
    //   }

    // },
    add() {
      if (!this.ruleForm.fk_customer_id) return this.error('请选择客户')
      if (this.activeName === "first") {
        // 3 = 软件服务费 初始化起始时间  结束时间
        if (this.ruleForm.sell_type == "3") {
          let d = new Date();
          let pro = JSON.parse(JSON.stringify(this.proObj));
          pro.maintain_start_time.value = dateFormat("yyyy-MM-dd", d);
          d.setFullYear(d.getFullYear() + 1);
          pro.new_maintain_stop_time.value = dateFormat("yyyy-MM-dd", d);
          this.$refs.proTableCustom.add2(pro);
        } else {
          this.$refs.proTableCustom.add2(this.proObj);
        }
      } else if (this.activeName === "second"){
        // this.$refs.moduleTableCustom.add();
      }

    },
    del(info = {}) {
      if (info.quotation_id.value) {
        let proList = this.$refs.proTableCustom.getData2();
        // let moduleList = this.$refs.moduleTableCustom.getData2();
        proList.forEach((item) => {
          if (item.quotation_id.value == info.quotation_id.value)
            this.$refs.proTableCustom.delBy(
              "quotation_id",
              info.quotation_id.value
            );
        });
        // moduleList.forEach((item) => {
        //   if (item.quotation_id.value == info.quotation_id.value)
        //     this.$refs.moduleTableCustom.delBy(
        //       "quotation_id",
        //       info.quotation_id.value
        //     );
        // });
      }
    },
    //选择介绍人
    chooseIntroducer() {
      this.$refs.dealCustomerList.Show();
    },
    //获取介绍人
    getInfo(info = {}) {
      this.ruleForm.introducer_format = info.customer_name;
      this.ruleForm.introducer = info.customer_id;
    },
    //选择客户
    chooseCustomer() {
      this.$refs.customerList.Show();
    },
    getCustomerInfo(info = {}) {
      this.ruleForm.customer_name = info.customer_name;
      this.ruleForm.fk_customer_id = info.customer_id;
      this.ruleForm.fk_sell_employee_id = info.fk_sale_employee_id;
      this.ruleForm.fk_sell_department_id = info.department_id;
      this.ruleForm.fk_sell_employee_name = info.fk_sale_employee_id_name;
      this.ruleForm.fk_sell_department_name = info.department_name;
      this.ruleForm.link_man = info.link_man;
      this.ruleForm.address = info.link_address;
      this.ruleForm.fax = info.fax;
      this.ruleForm.province = info.province;
      if (info.province) {
        this.getCityList();
      }
      this.ruleForm.city = info.city;
      this.ruleForm.link_qq = info.qq;
      if (info.phone) this.ruleForm.phone = info.phone;
      else this.ruleForm.phone = info.telephone;
      this.ruleForm.introducer_format = info.introducer_format;
      this.ruleForm.introducer = info.introducer;

      this.proObj.original_port_count.value = info.port_number; // 端口数
      let pro = JSON.parse(JSON.stringify(this.proObj));
      // console.log(info);
      // console.log(" info === ");
      pro.maintain_start_time.value = info.maintain_start_time;
      pro.new_maintain_stop_time.value = info.maintain_stop_time;
      pro.original_maintain_stop_time.value = info.maintain_stop_time;
      this.$refs.proTableCustom.add2(pro);
    },
    //选择销货单位
    chooseCompany() {
      this.$refs.salesUnitList.Show();
    },
    getCampanyInfo(info = {}) {
      this.ruleForm.sales_unit_id = info.sales_unit_id;
      this.ruleForm.sales_unit_id_format = info.company_name;
    },
    //选择介绍合同
    chooseContract() {
      this.$refs.contractList.Show();
    },
    getContractInfo(info = {}) {
      this.ruleForm.introducer_contract_format = info.contract_no;
      this.ruleForm.introducer_contract_id = info.customer_contract_id;
    },
    //选择续费合同
    chooseRenewContract() {
      this.$refs.renewContractList.Show();
    },
    getRenewContractInfo(info = {}) {
      this.ruleForm.renewal_contract_format = info.contract_no;
      this.ruleForm.renewal_contract_id = info.customer_contract_id;
      this.ruleForm.customer_name = info.customer_name;
      this.ruleForm.fk_customer_id = info.fk_customer_id;
      this.ruleForm.fk_sell_employee_id = info.fk_sell_employee_id;
      this.ruleForm.fk_sell_department_id = info.fk_sell_department_id;
      this.ruleForm.fk_sell_employee_name = info.fk_sell_employee_name;
      this.ruleForm.fk_sell_department_name = info.fk_sell_department_name;
      this.ruleForm.link_man = info.link_man;
      this.ruleForm.address = info.address;
      this.ruleForm.fax = info.fax;
      this.ruleForm.province = info.province;
      if (info.province) {
        this.getCityList();
      }
      this.ruleForm.city = info.city;
      this.ruleForm.link_qq = info.link_qq;
      this.ruleForm.phone = info.phone;
      this.ruleForm.introducer_format = info.introducer_format;
      this.ruleForm.introducer = info.introducer;
      this.ruleForm.introducer_contract_format =
        info.introducer_contract_format;
      this.ruleForm.introducer_contract_id = info.introducer_contract_id;
      this.ruleForm.sales_unit_id = info.sales_unit_id;
      this.ruleForm.sales_unit_id_format = info.sales_unit_id_format;
      this.ruleForm.sell_type = info.sell_type;
      this.ruleForm.pay_type = info.pay_type?info.pay_type:1;
      this.ruleForm.train_type = info.train_type?info.train_type:2;
      this.ruleForm.remark = info.remark;
      this.ruleForm.fk_recommend_employee_id = info.fk_recommend_employee_id;

      this.$refs.proTableCustom.clear();
      // this.$refs.moduleTableCustom.clear();
      API.detailList({
        customer_contract_id: info.customer_contract_id,
      })
        .then((res) => {
          res.data.map((item) => {
            let pro = JSON.parse(JSON.stringify(this.proObj));
            // let module = JSON.parse(JSON.stringify(this.moduleObj));
            for (let v in item) {
              if (item.detail_type == 1) {
                if (pro[v]) {
                  pro[v].value = item[v];
                }
              } else {
                // if (module[v]) {
                //   module[v].value = item[v];
                // }
              }
            }
            if (item.detail_type == 1 && item.new_maintain_stop_time) {
              pro.maintain_start_time.value = item.new_maintain_stop_time;
              let d = new Date(item.new_maintain_stop_time);
              d.setFullYear(d.getFullYear() + 1);
              pro.new_maintain_stop_time.value = dateFormat("yyyy-MM-dd", d);
              pro.original_maintain_stop_time.value =
                item.new_maintain_stop_time;
            }
            if (item.detail_type == 1) {
              this.$refs.proTableCustom.add2(pro);
            } else {
              // this.$refs.moduleTableCustom.add2(module);
            }
          });
        })
        .catch(() => {})
        .finally(() => {});
    },
    //调入报价单
    callIn() {
      this.$refs.quotationList.Show();
    },
    getQuotationInfo(info = {}) {
      this.ruleForm.customer_name = info.customer_name;
      this.ruleForm.fk_customer_id = info.fk_customer_id;
      this.ruleForm.train_type = info.train_type?info.train_type:2;
      this.ruleForm.pay_type = info.pay_type?info.pay_type:1;
      this.ruleForm.sell_type = info.sell_type;
      this.ruleForm.address = info.address;
      this.ruleForm.sales_unit_id = info.sales_unit_id;
      this.ruleForm.sales_unit_id_format = info.sales_unit_id_format;
      this.ruleForm.link_man = info.link_man;
      this.ruleForm.introducer = info.introducer;
      this.ruleForm.introducer_format = info.introducer_name;
      this.ruleForm.introducer_contract_id = info.introducer_contract_id;
      this.ruleForm.fk_sell_employee_id = info.fk_sell_employee_id;
      this.ruleForm.introducer_contract_format =
        info.introducer_contract_id_format;
      this.ruleForm.fax = info.fax;
      this.ruleForm.phone = info.phone;
      this.ruleForm.link_qq = info.link_qq;
      this.ruleForm.software_no = info.software_no;
      this.ruleForm.province = info.province;
      this.ruleForm.city = info.city;
      this.ruleForm.fk_sell_department_id = info.department_id;
      this.ruleForm.fk_sell_employee_name = info.fk_sell_employee_name;
      this.ruleForm.fk_sell_department_name = info.fk_sell_department_name;
      this.getCityList();
      quoAPI
        .detailList({
          quotation_id: info.quotation_id,
        })
        .then((res) => {
          res.data.map((item) => {
            let pro = JSON.parse(JSON.stringify(this.proObj));
            // let module = JSON.parse(JSON.stringify(this.moduleObj));
            if (item.detail_type == 1) {
              pro.quotation_id.value = info.quotation_id;
              pro.fk_brand_id.value = item.brand_id;
              pro.software_version.value = item.software_version;
              pro.measurement_unit.value = item.measurement_unit;
              pro.contract_count.value = item.quotation_number;
              pro.contract_amount.value = item.quotation_unit;
              pro.invoice_tax_rate.value = item.quotation_rate;
              pro.year_maintain_cost.value = item.years_maintenance_fee;
              // pro.prepayment_rate.value = item.prepayment_ratio;
              pro.maintain_start_time.value = item.maintain_start_time;
              pro.new_maintain_stop_time.value = item.new_maintain_stop_time;
              pro.original_maintain_stop_time.value =
                item.original_maintain_stop_time;
              pro.original_port_count.value = item.qriginal_port_number;
              pro.add_port_count.value = item.new_port_number;
              pro.remark.value = item.remark;
              this.$refs.proTableCustom.add2(pro);
            } else {
              // module.quotation_id.value = info.quotation_id;
              // module.fk_brand_id.value = item.brand_id;
              // module.measurement_unit.value = item.measurement_unit;
              // module.contract_amount.value = item.quotation_unit;
              // module.remark.value = item.remark;
              // this.$refs.moduleTableCustom.add2(module);
            }
          });
        })
        .catch(() => {})
        .finally(() => {});
    },
    //获取省列表
    getProvinceList() {
      comAPI
        .queryAreaCode({
          level: 1,
        })
        .then((data) => {
          this.provinceList = data.data;
        })
        .catch(() => {});
    },
    //更换省
    changeProvince() {
      this.cityList = [];
      this.ruleForm.city = "";
      this.getCityList();
    },
    //获取市列表
    getCityList() {
      if (!this.ruleForm.province) return;
      comAPI
        .queryAreaCode({
          level: 2,
          province: this.ruleForm.province,
        })
        .then((data) => {
          this.cityList = data.data;
        })
        .catch(() => {});
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //清空数据
    clearData() {
      this.ruleForm = {
        customer_name: "",
        fk_customer_id: "",
        train_type: 2,
        pay_type: 1,
        sell_type: "",
        sales_unit_id: "",
        link_man: "",
        fk_sell_employee_id: "",
        fk_sell_department_id: "",
        phone: "",
        introducer_format: "",
        introducer: "",
        province: "",
        city: "",
        remark: "",
        address: "",
        fax: "",
        software_no: "",
        link_qq: "",
        sales_unit_id_format: "",
        introducer_contract_format: "",
        introducer_contract_id: "",
        fk_recommend_employee_id: "",
      };
    },
    // 销售类型下拉改变
    sellTypeChange(val) {
      this.proObjRest()
      const item = this.sell_type.find(item => item.id === val)
      for (let key in item) {
        let value = item[key]
        if (key.includes('_value')&&item[key]) {
          let itemKey = key.replace('_value', '')
          if (this.proObj.hasOwnProperty(itemKey)) {
            this.proObj[itemKey].value = value
          }
        }
      }
    },
    // 重置行数据
    proObjRest() {
      this.proObj = {
        quotation_id: {
          value: "",
          type: "input",
        },
        fk_brand_id: {
          value: "",
          type: "select",
          option: this.proList,
        },
        software_version: {
          value: "",
          type: "select",
          option: this.software_version,
        },
        measurement_unit: {
          value: "",
          type: "select",
          option: this.measurement_unit,
        },
        contract_count: {
          value: "1",
          type: "float",
        },
        contract_amount: {
          value: "",
          type: "float",
        },
        invoice_tax_rate: {
          value: "",
          type: "select",
          option: this.quotationRateList,
        },
        // prepayment_rate: {
        //   value: "100",
        //   type: "select",
        //   option: this.prepaymentRatioList,
        // },
        year_maintain_cost: {
          value: "15",
          type: "select",
          option: this.yearsFeeList,
        },
        prepayment_time_limit: {
          value: "",
          type: "date",
        },
        maintain_start_time: {
          value: "",
          type: "date",
        },
        new_maintain_stop_time: {
          value: "",
          type: "date",
        },
        original_maintain_stop_time: {
          value: "",
          type: "date",
        },
        //原有端口数
        original_port_count: {
          value: '',
          type: "number",
          disabled: false
        },
        add_port_count: {
          value: 0,
          type: "number",
        },
        remark: {
          value: "",
          type: "input",
        },
      };
    }
  },
  computed: {
    ...mapGetters([
      "userInfo",
      "buttonPermissions",
      "contract_train_type",
      "measurement_unit",
      "sell_type",
      "contract_pay_type",
      "software_version",
    ]),
  },
};
