import API from '@/api/internalSystem/salesManage/authorization'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddAuthorization from "./components/addAuthorization/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import {
  mapGetters
} from "vuex";
export default {
  name: "authorization",
  data() {
    return {
      title: "销售授权单",
      loading: false,
      tableData: [],
      formSearch: {
        fk_sell_employee_id:"",
        customer_name:"",
        startTime: "",
        endTime: ""
      },
      tableList: [
        {
          name: "授权类型",
          value: "authorization_type"
        },
        {
          name: "编号",
          value: "authorization_no"
        },
        {
          name: "合同单据",
          value: "contract_no"
        },
        {
          name: "单据日期",
          value: "update_time"
        },
        {
          name: "客户编号",
          value: "customer_no"
        },
        {
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "产品服务",
          value: "brand_type"
        },
        {
          name: "销售类型",
          value: "sell_type"
        },
        {
          name: "授权端口(原有端口+新增端口)",
          value: "port_count"
        },
        {
          name: "维护结束时间",
          value: "authorization_maintain_stop_time"
        },
        {
          name: "授权码",
          value: "software_no"
        },
        {
          name: "销售员",
          value: "fk_sell_employee_name"
        },
        {
          name: "制单人",
          value: "add_user_name"
        },
        {
          name: "单据备注",
          value: "remark"
        },
      ],
      employeeList: [],
      isAdd: false
    };
  },
activated() {
  let params = this.$route.params;

    if (params.type === "home") {
      Object.assign(this.formSearch, params);
      this.$refs.addAuthorization.dialogCancel(true,false)
    }
},
  mounted() {
    if (!this.$route.params.type) this.getList();
    this.$store.dispatch('getEmployee').then(res => {
      this.employeeList = res;
    });
  },
  methods: {
    getList(f = false) {
      this.isAdd = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
          if (f)
            param.pageNum = 1;
          // let isJurisdiction = this.buttonPermissions.length ? (this.existence(this.buttonPermissions, 'ALL_AUTHORIZATION_NEW')) : false
          // param.isJurisdiction = isJurisdiction ? 1 : 0;
          param.isJurisdiction = this.permissionToCheck('ALL_AUTHORIZATION_NEW') ? 1 : 0;
          API.query(param).then(res => {
            this.tableData = res.data;
            this.$refs.pagination.setTotal(res.totalCount);
          }).finally(() => {
            this.loading = false;
          });
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addAuthorization.Show();
    },
    modify(item) {
      let params = {
        authorization_id: item.authorization_id
      };
      API.getInfo(params)
        .then(data => {
          this.isAdd = true;
          this.$refs.addAuthorization.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      if(item.data_state ===2){
        return this.error('该记录是旧数据，请上旧系统继续操作')
      }
      let params = {
        authorization_id: item.authorization_id,
        fk_contract_detail_ids:item.fk_contract_detail_ids,
        fk_customer_contract_id:item.fk_customer_contract_id
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    }
  },

  components: {
    AddAuthorization,
    Pagination,
    TableView,
    MyDate
  },
  computed: {
    ...mapGetters(["buttonPermissions"])
  }
};