import Axios from "@/api";
import environment from "@/api/environment";

export default {
  // 查询产品列表 brand_classify=1
  getSystemQuery: params => {
    return Axios.post(`${environment.internalSystemAPI}brand/query`, params)
  },
  //获取模块列表 需要传产品id brand_id
  getmoudelList: params => {
    return Axios.post(`${environment.internalSystemAPI}module/query`, params)
  },
  getUserList: params => {
    return Axios.post(`${environment.internalSystemAPI}employee/query`, params)
  },
};