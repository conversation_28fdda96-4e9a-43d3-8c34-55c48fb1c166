import API from '@/api/internalSystem/customerManage/customerIntention'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddIntention from "./components/addIntention/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from '@/views/internalSystem/backstage/components/myDate/index.vue'
import AuditDetail from '@/mixins/auditDetail.js'
import {
  mapGetters
} from "vuex";
export default {
  name: "customerIntention",
  mixins: [AuditDetail],
  data() {
    return {
      title: "客户意向单",
      loading: false,
      selectRecords: [],
      tableData: [],
      formSearch: {
        customer_name: "",
        fk_sale_employee_id: "",
        auditState: "",
        startTime: "",
        endTime: ""
      },
      tableList: [{
          name: "审核状态",
          value: "auditStateFormat",
          width: 82
        },
        {
          name: "编号",
          value: "customer_intention_no",
          width: 120
        },
        {
          name: "客户名称",
          value: "customer_name"
        },
        {
          name: "联系人",
          value: "link_man",
          width: 80
        },
        {
          name: "联系电话",
          value: "phone",
          width: 100
        },
        {
          name: "客户地区",
          value: "province",
          width: 80
        },
        {
          name: "客户阶段",
          value: "customer_stage_format",
          width: 72
        },
        {
          name: "客户行业",
          value: "belong_industry_format",
          width: 72
        },
        {
          name: "客户类别",
          value: "customer_type_format",
          width: 82
        },
        {
          name: "客户来源",
          value: "customer_source_format",
          width: 82
        },
 
        {
          name: "销售员",
          value: "fk_sale_employee_name",
          width: 72
        },
        {
          name: "联系时间",
          value: "contact_time",
          width: 100
        },
        {
          name: "录入日期",
          value: "update_time",
          width: 100
        }
      ],
      employeeList: [],
      isAdd: false
    };
  },

  mounted() {
    this.getList();
    this.$store.dispatch('getEmployee').then(res => {
      this.employeeList = res;
    });
  },
  created() {
    if (!["总经理"].includes(this.cookiesUserInfo.role_name)) {
      this.formSearch.fk_sale_employee_id = this.cookiesUserInfo.userId;
    }
  },
  methods: {
    getList(f = false) {
      this.isAdd = false;
      this.isAudit = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
          if (f)
            param.pageNum = 1;
          API.query(param).then(res => {
            this.tableData = res.data;
            this.$refs.pagination.setTotal(res.totalCount);
          }).finally(() => {
            this.loading = false;
          });
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addIntention.Show();
    },
    modify(item) {
      let params = {
        customer_intention_id: item.customer_intention_id
      };
      API.getInfo(params)
        .then(data => {
          this.isAdd = true;
          this.$refs.addIntention.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      if (item.auditState == 1)
        return this.error("该单据已审核，不允许删除");
      let params = {
        customer_intention_id: item.customer_intention_id
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    }
  },

  components: {
    AddIntention,
    Pagination,
    TableView,
    MyDate
  },
  computed: {
    ...mapGetters(["customer_intention_audit","cookiesUserInfo"])
  }
};