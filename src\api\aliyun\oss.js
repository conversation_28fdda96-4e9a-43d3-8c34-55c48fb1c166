import { Message } from "element-ui";
import environment from "@/store/environment";
import { randomString } from "@/utils/index";
import _moment from "moment";

export default {
  // 上传文件列表，里面包含已上传文件，未上传文件
  uploadFileList: (files = []) => {
    if (!window.OSS) {
      let message = `十分抱歉，文件上传sdk尚未载入，请联系管理员解决`;
      Message({
        showClose: true,
        message: message,
        type: "error"
      });
      return Promise.reject(message);
    }
    let date = _moment().format("YYYY_MM_DD");
    let client = new window.OSS({
      region: environment.state.region,
      accessKeyId: environment.state.accessKeyId,
      accessKeySecret: environment.state.accessKeySecret,
      bucket: environment.state.bucket
    });
    
    let uploadPut = async function uploadPut(client) {
      try {
        let resList = [];
        for (let i = 0; i < files.length; i++) {
          let file = files[i];
          if (file.raw) {
            let name = `${date}/${randomString(32)}_trade_erp_${file.raw.name}`;
            let res = await client.put(`${name}`, file.raw);
            if (!res.url) {
              Message({
                showClose: true,
                message: `十分抱歉，上传文件失败，请联系管理员解决`,
                type: "error"
              });
              return Promise.reject(res);
            }
            resList.push({
              name: res.name,
              url: res.url
            });
          } else if (file.url) {
            resList.push(file);
          }
        }
        return resList;
      } catch (e) {
        Message({
          showClose: true,
          message: `十分抱歉，上传文件失败，请联系管理员解决`,
          type: "error"
        });
        return Promise.reject(e.toString());
      }
    };
    return uploadPut(client);
  },
  // 上传单个文件
  uploadFile: (file,systemPath = null) => {
    if (!window.OSS) {
      let message = `十分抱歉，文件上传sdk尚未载入，请联系管理员解决`;
      Message({
        showClose: true,
        message: message,
        type: "error"
      });
      return Promise.reject(message);
    }
    //如果有传系统路径就用那个路径层级，没用的话默认用时间
    let path = systemPath ? systemPath : _moment().format("YYYY_MM_DD");
    let name = `${path}/${randomString(8)}_${file.size}_system_${file.name}`;
    let client = new window.OSS({
      region: environment.state.region,
      accessKeyId: environment.state.accessKeyId,
      accessKeySecret: environment.state.accessKeySecret,
      bucket: environment.state.bucket
    });

    let res = null;
    let uploadPut = async function uploadPut(client, name, file) {
      try {
        res = await client.put(`${name}`, file);
        if (!res.url) {
          Message({
            showClose: true,
            message: `十分抱歉，上传文件失败，请联系管理员解决`,
            type: "error"
          });
          return Promise.reject(res);
        }
        //把http改成https
        let flag = res.url.indexOf("https");
        if (flag === -1) {
          res.url = res.url.replace(/^http/, "https");
        }
        return res;
      } catch (e) {
        Message({
          showClose: true,
          message: `十分抱歉，上传文件失败，请联系管理员解决`,
          type: "error"
        });
        return Promise.reject(e.toString());
      }
    };
    return uploadPut(client, name, file);
  }
};
