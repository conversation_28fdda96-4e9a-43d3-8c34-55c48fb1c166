<template>
  <div class="body-p10">
    <el-form :inline="true" :model="formSearch" size="small">
      <el-form-item label="查询条件">
        <el-input v-model="formSearch.customerName" placeholder="请输入客户名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="formSearch.fk_sale_employee_id" placeholder="请选择销售员" class="inputBox" filterable clearable>
          <el-option v-for="item in employeeList" :key="item.employeeId" :label="item.employee_number+'-'+item.employee_name"
            :value="item.employeeId">
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item>
        <el-select v-model="formSearch.customerStage" placeholder="请选择客户阶段" class="inputBox" filterable clearable>
          <el-option v-for="item in customerStageList" :key="item.sysValue" :label="item.sysName"
            :value="item.sysValue">
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="getList(true)" :loading="loading">查询</el-button>
      </el-form-item>
    </el-form>
    <table-view :tableList="tableList" :tableData="tableData"></table-view>
    <Pagination ref="pagination" @success="getList"/>
  </div>
</template>

<script src="./index.js"></script>