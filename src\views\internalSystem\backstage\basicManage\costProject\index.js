import API from '@/api/internalSystem/basicManage/costProject'
import Pagination from '@/components/internalSystem/Pagination/Pagination.vue'
import AddCostProject from "./components/addCostProject/index.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import {
  mapGetters
} from "vuex";
export default {
  name: "costProject",
  data() {
    return {
      title: "费用项目",
      loading: false,
      tableData: [],
      formSearch: {
        project_name: ""
      },
      tableList: [{
          name: "费用项目编码",
          value: "project_no"
        },
        {
          name: "费用项目名称",
          value: "project_name"
        },
        {
          name: "备注",
          value: "remark"
        }
      ],
      costProjectList: []
    };
  },

  mounted() {
    this.getList();
  },
  methods: {
    getList(f = false) {
      this.loading = true;
      let param = Object.assign(this.formSearch, this.$refs.pagination.obtain());
      if (f)
        param.pageNum = 1;
      API.query(param).then(res => {
        this.tableData = res.data;
        this.$refs.pagination.setTotal(res.totalCount);
      }).finally(() => {
        this.loading = false;
      });
    },
    add() {
      this.$refs.AddCostProject.Show();
    },
    //打开修改费用项目会话框
    modify(item) {
      let params = {
        financial_cost_project_id: item.financial_cost_project_id
      };
      API.getInfo(params)
        .then(data => {
          this.$refs.AddCostProject.Show(data.data);
        })
        .catch(() => {});
    },
    del(item) {
      let params = {
        financial_cost_project_id: item.financial_cost_project_id
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    }
  },

  components: {
    AddCostProject,
    Pagination,
    TableView
  },
  computed: {
    ...mapGetters(["buttonPermissions"])
  }
};