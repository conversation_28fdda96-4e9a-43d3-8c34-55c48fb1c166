@import "./colour.scss";

$--el-dialog__header_height: 60px;

#erp {

  .el-dialog__header {
    padding: 0;
    overflow: hidden;
    height: $--el-dialog__header_height;

    span {
      vertical-align: top;
      margin-left: 20px;
      line-height: $--el-dialog__header_height;
    }
  }

  // 弹窗的标题
  .el-dialog__body {
    padding-top: 8px !important;

    &.gray_background_color {
      background-color: #ebebeb;
    }

    .el-form-item__label {
      line-height: 36px;
    }

    .dialog-box {
      max-height: 50vh;
      overflow-y: auto;

      &.is-flex-box,
      .is-flex-box {
        overflow-x: hidden;

        .is-flex {
          display: flex;
          margin-bottom: 20px;

          label {
            width: auto;
            flex: 0 0 auto;
            line-height: 32px;
            font-size: 14px;
            letter-spacing: 3px;

            .required {
              font-size: 12px;
              color: $--color-danger;
              line-height: 32px;
              font-weight: bold;
            }
          }

          .all {
            width: 100%;
            height: 100%;
            flex: 1;
            margin-left: 5px;
          }
        }
      }
    }

    .image-list {

      .el-icon-zoom-in,
      .el-icon-delete {
        line-height: 146px;
      }
    }
  }
}

//以项目名称命名
#erp {

  // 弹窗的底部按钮
  .el-dialog__footer {
    padding: 0 20px 0 0;
    height: $--el-dialog__header_height;

    .dialog-footer {
      height: 60px;
      display: inline-block;
      button {
        margin-top: 10px;
      }
    }
  }
}


.roleClassification {
  .el-tree-node__content {
    height: 40px;
  }

  .el-tree-node__expand-icon {
    margin-top: 3px;
  }

  .el-tree__empty-text {
    font-size: 26px;
    margin-left: -200px;
  }
}

.staff-tree {
  .el-tree__empty-text {
    margin-left: 0;
  }
}


.singleDiagram {
  display: inline-block;
  $el-upload-size: 100px;

  // 单个图片的样式
  .el-upload {
    width: $el-upload-size;
    height: $el-upload-size;
    overflow: hidden;
    border-radius: 5px;
    border: 1px dashed rgba(0, 0, 0, .2);
    line-height: $el-upload-size;

    img {
      height: 100%;
    }
  }

  // 照片墙的样式
  .el-upload-list {
    .el-upload-list__item {
      width: $el-upload-size;
      height: $el-upload-size;
    }

    .el-upload-list__item-actions {

      .el-upload-list__item-preview,
      .el-upload-list__item-delete {
        line-height: $el-upload-size;
      }
    }
  }
}

.singleDiagram-max {
  $el-upload-size: 200px;

  // 单个图片的样式
  .el-upload {
    width: $el-upload-size;
    height: $el-upload-size;
    overflow: hidden;
    border-radius: 5px;
    border: 1px dashed rgba(0, 0, 0, .2);
    line-height: $el-upload-size;

    img {
      height: 100%;
    }
  }

  // 照片墙的样式
  .el-upload-list {
    .el-upload-list__item {
      width: $el-upload-size;
      height: $el-upload-size;
    }

    .el-upload-list__item-actions {

      .el-upload-list__item-preview,
      .el-upload-list__item-delete {
        line-height: $el-upload-size;
      }
    }
  }
}



.avatar-uploader {
  width: 300px;
  height: auto;
  min-height: 200px;
  text-align: center;
  border: 1px dashed #a3a9ac;
  border-radius: 10px;
  margin-bottom: 30px;

  .el-upload {
    width: 100%;
    min-height: 200px;

    i {
      width: 100%;
      line-height: 200px;
      font-size: 30px;
    }
  }
}