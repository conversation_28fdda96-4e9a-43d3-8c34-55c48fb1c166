import Axios from "@/api";
import environment from "@/api/environment";

export default {
  // 查询
  query: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}openTicket/query`,
      params
    );
  },
  // 查询
  queryAll: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}openTicket/queryAll`,
      params
    );
  },
  // 查询明细
  detailList: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}openTicket/detailList`,
      params
    );
  },
  // 新增
  add: (params) => {
    return Axios.post(`${environment.internalSystemAPI}openTicket/add`, params);
  },
  // 删除
  remove: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}openTicket/remove`,
      params
    );
  },
  // 编辑
  update: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}openTicket/update`,
      params
    );
  },
  // 获取单条信息
  getInfo: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}openTicket/getInfo`,
      params
    );
  },
  // 审核
  updateAudit: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}openTicket/updateAudit`,
      params
    );
  },
  // 审核
  getTotalOpenMoney: (params) => {
    return Axios.post(
      `${environment.internalSystemAPI}customerContract/getTotalOpenMoney`,
      params
    );
  },
};
