import API from "@/api/internalSystem/customerManage/copyPushLog";
import Pagination from "@/components/internalSystem/Pagination/Pagination.vue";
import TableView from "@/views/internalSystem/backstage/components/tableView/index.vue";
import MyDate from "@/views/internalSystem/backstage/components/myDate/index.vue";
import AuditDetail from "@/mixins/auditDetail.js";
import AddCopyPush from "@/views/internalSystem/backstage/customerManage/article/components/addCopyPush/index.vue";
// import DictAPI from "@/api/internalSystem/dict";
import { mapGetters } from "vuex";
export default {
  name: "customerPush",
  mixins: [AuditDetail],
  data() {
    return {
      title: "客户回访单",
      loading: false,
      tableData: [],
      formSearch: {
        customer_name: "",
        send_user_id: "",
        fk_template_id: "",
        startTime: "",
        endTime: "",
      },
      tableList: [
        {
          name: "编号",
          value: "copy_push_no",
        },
        {
          name: "文案主题",
          value: "title",
        },
        {
          name: "推送人",
          value: "employee_name",
        },
        {
          name: "推送客户人数",
          value: "customer_number",
        },
        {
          name: "成功推送客户人数",
          value: "success_customer_number",
        },
        {
          name: "推送时间",
          value: "actual_send_time",
        },
        {
          name: "备注",
          value: "remark",
        },
      ],
      employeeList: [],
      templateOption: [],
      isAdd: false,
      isAudit: false,
    };
  },

  mounted() {
    this.getList();
    this.$store.dispatch("getEmployee").then((res) => {
      this.employeeList = res;
    });
  },
  created() {
    // this.getDict();
  },
  methods: {
    // getDict() {
    //   DictAPI.getDict({
    //     type: "customer_push_template",
    //   }).then((res) => {
    //     this.templateOption = res.data;
    //   });
    // },
    getList(f = false) {
      this.isAdd = false;
      this.isAudit = false;
      this.loading = true;
      // setTimeout(() => {
        //修改数据
        //DOM还没更新
        this.$nextTick(() => {
          //DOM现在更新了
          let param = Object.assign(
            this.formSearch,
            this.$refs.pagination.obtain()
          );
          // let param ={}
          if (f) param.pageNum = 1;

          API.query(param)
            .then((res) => {
              this.tableData = res.data;
              this.$refs.pagination.setTotal(res.totalCount);
            })
            .finally(() => {
              this.loading = false;
            });
        });
      // }, 300);
    },
    add() {
      this.isAdd = true;
      this.$refs.addCopyPush.Show();
    },
    modify(item) {
      this.isAdd = true;
      this.$refs.addCopyPush.Show(item);
      // let params = {};
      // API.getInfo(params)
      //   .then(data => {
      //     this.isAdd = true;
      //     this.$refs.addCustomerPush.Show(data.data);
      //   })
      //   .catch(() => {});
    },
    del(item) {
      if (item.push_state === 2 || item.push_state === 3) return this.error("该记录无法删除");
      let params = {
        customer_push_log_id: item.customer_push_log_id,
      };
      API.remove(params)
        .then(() => {
          this.getList();
        })
        .catch(() => {});
    },
  },

  components: {

    Pagination,
    TableView,
    MyDate,
    AddCopyPush
  },
  computed: {
    ...mapGetters(["customer_sales_visiting_audit"]),
  },
};
